PODS:
  - Capacitor (5.7.8):
    - Capac<PERSON><PERSON><PERSON><PERSON>
  - Capacitor<PERSON>pp (5.0.7):
    - Capacitor
  - CapacitorCamera (5.0.9):
    - Capacitor
  - CapacitorCommunityBarcodeScanner (4.0.1):
    - Capacitor
  - Capac<PERSON><PERSON>ordova (5.7.8)
  - Capacitor<PERSON>evice (5.0.7):
    - Capacitor
  - CapacitorGeolocation (5.0.8):
    - Capacitor
  - CapacitorHaptics (5.0.7):
    - Capacitor
  - CapacitorKeyboard (5.0.8):
    - Capacitor
  - CapacitorPushNotifications (5.1.1):
    - Capacitor
  - CapacitorStatusBar (5.0.7):
    - Capacitor
  - Firebase/CoreOnly (8.6.0):
    - FirebaseCore (= 8.6.0)
  - Firebase/Messaging (8.6.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 8.6.0)
  - FirebaseCore (8.6.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.4)
    - GoogleUtilities/Logger (~> 7.4)
  - FirebaseCoreDiagnostics (8.6.0):
    - GoogleDataTransport (~> 9.0)
    - GoogleUtilities/Environment (~> 7.4)
    - GoogleUtilities/Logger (~> 7.4)
    - nanopb (~> 2.30908.0)
  - FirebaseInstallations (8.6.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.4)
    - GoogleUtilities/UserDefaults (~> 7.4)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseMessaging (8.6.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.4)
    - GoogleUtilities/Environment (~> 7.4)
    - GoogleUtilities/Reachability (~> 7.4)
    - GoogleUtilities/UserDefaults (~> 7.4)
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (9.1.0):
    - GoogleUtilities/Environment (~> 7.2)
    - nanopb (~> 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.5.1):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.5.1):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.5.1):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.5.1):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.5.1)"
  - GoogleUtilities/Reachability (7.5.1):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.5.1):
    - GoogleUtilities/Logger
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - PromisesObjC (2.0.0)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCommunityBarcodeScanner (from `../../node_modules/@capacitor-community/barcode-scanner`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - Firebase/Messaging

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCommunityBarcodeScanner:
    :path: "../../node_modules/@capacitor-community/barcode-scanner"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 747aadb4fa786f460d8cd9d0557cb81440a13747
  CapacitorApp: 17fecd0e6cb23feafac7eb0939417389038b0979
  CapacitorCamera: 4892c5c392f60039d853dde78bc50ba19fbd113e
  CapacitorCommunityBarcodeScanner: 7feb206489c8555a8ca0c74c57ddf49ead774eb8
  CapacitorCordova: 31ab98dca2ddcee051027a1afe7e8c85c82f7297
  CapacitorDevice: fc91bdb484dc0e70755e9b621cd557afe642613a
  CapacitorGeolocation: 0572212732f7ee65828d069a9663a274be89b9fc
  CapacitorHaptics: 7c7c206f0c96a628fed073830c96d28c4b2e772e
  CapacitorKeyboard: aec619a578235c6ce279075009a2689c2cf5c42c
  CapacitorPushNotifications: 2327900bc002f5ff49ee6d2231796d0635b4a1b0
  CapacitorStatusBar: f390fbb49b82ffb754ea4b3cf71dc8b048baf3e7
  Firebase: 21ac9f28b09a8bdfc005f34c984fca84e7e8786d
  FirebaseCore: 620b677f70f5470a8e59cb77f3ddc666f6f09785
  FirebaseCoreDiagnostics: 3721920bde3a9a6d5aa093c1d25e9d3e47f694af
  FirebaseInstallations: 0ede6ffcd215b8f93c19d9b06c1c54e2d4107e98
  FirebaseMessaging: ce0a5ee974f7bfe83b6cc5acce88c2d969e37c41
  GoogleDataTransport: 85fd18ff3019bb85d3f2c551d04c481dedf71fc9
  GoogleUtilities: 3df19e3c24f7bbc291d8b5809aa6b0d41e642437
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  PromisesObjC: 68159ce6952d93e17b2dfe273b8c40907db5ba58

PODFILE CHECKSUM: c4f23083c5c02830160d3980ff1dd35a09e449f4

COCOAPODS: 1.9.3
