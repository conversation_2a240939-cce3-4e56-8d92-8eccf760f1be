/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

ion-input {
  color: black!important;
  transform: translateZ(0);
}


.alert-wrapper{
  max-width: unset!important;
  width: 80vw!important;
}
.alert-title{
  font-size: 1rem!important;
}

.nobr{
  white-space: nowrap;
}

.flex-wrap{
  flex-wrap: wrap!important;
}
.flex-nowrap{
  flex-wrap: nowrap;
}
.flex-column{
  flex-direction: column;
}
.flex-row{
  flex-direction: row;
}

.d-none{
  display: none;
}
.d-block{
  display: block;
}
.d-inline-block{
  display: inline-block;
}
.d-flex{
  display: flex;
  flex-wrap: nowrap;
}

.border{
  border: 1px solid #dee2e6 !important;
}
.border-top{
  border-top: 1px solid #dee2e6 !important;
}
.border-bottom{
  border-bottom: 1px solid #dee2e6 !important;
}
.border-right{
  border-right: 1px solid #dee2e6 !important;
}
.border-left{
  border-left: 1px solid #dee2e6 !important;
}

.border-top-0{
  border-top: none!important;
}
.border-bottom-0{
  border-bottom: none!important;
}
.border-right-0{
  border-right: none!important;
}
.border-left-0{
  border-left: none!important;
}

.border-success{
  border-color: #19d895!important;
}
.border-danger{
  border-color: #ff6258!important;
}
.border-warning{
  border-color: #ffaf00!important;
}
.border-dark {
  border-color: #252C46 !important;
}


.rounded{
  border-radius: 0.25rem !important;
}
.rounded-top{
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}
.rounded-bottom{
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.rounded-circle{
  border-radius: 100% !important;
}

.min-w-50{
  min-width: 50px;
}
.min-w-75{
  min-width: 75px;
}
.min-w-100{
  min-width: 100px;
}
.min-w-125{
  min-width: 125px;
}
.min-w-150{
  min-width: 150px;
}
.min-w-175{
  min-width: 175px;
}
.min-w-200{
  min-width: 200px;
}
.min-w-250{
  min-width: 250px;
}
.min-w-300{
  min-width: 300px;
}
.min-w-400{
  min-width: 400px;
}
.min-w-500{
  min-width: 500px;
}

.w-0{
  width: 0;
}
.w-10{
  width: 10%;
}
.w-25{
  width: 25%;
}
.w-50{
  width: 50%;
}
.w-75{
  width: 75%;
}
.w-100{
  width: 100%;
}

.shadow {
  box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 15%);
}
.shadow-unset{
  box-shadow: unset;
}

.m-0{
  margin: 0!important;
}
.my-0{
  margin-top: 0;
  margin-bottom: 0;
}
.mx-0{
  margin-left: 0;
  margin-right: 0;
}
.mr-0{
  margin-right: 0;
}
.ml-0{
  margin-left: 0;
}
.mt-0{
  margin-top: 0;
}
.mb-0{
  margin-bottom: 0;
}


.m-1{
  margin: .25rem;
}
.my-1{
  margin-top: .25rem;
  margin-bottom: .25rem;
}
.mx-1{
  margin-left: .25rem;
  margin-right: .25rem;
}

.mr-1{
  margin-right: .25rem;
}
.ml-1{
  margin-left: .25rem;
}
.mt-1{
  margin-top: .25rem;
}
.mb-1{
  margin-bottom: .25rem;
}

.m-2{
  margin: .5rem;
}
.my-2{
  margin-top: .5rem;
  margin-bottom: .5rem;
}
.mx-2{
  margin-left: .5rem!important;
  margin-right: .5rem!important;
}

.mr-2{
  margin-right: .5rem;
}
.ml-2{
  margin-left: .5rem;
}
.mt-2{
  margin-top: .5rem;
}
.mb-2{
  margin-bottom: .5rem;
}

.m-3{
  margin: 1rem;
}
.my-3{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mx-3{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mr-3{
  margin-right: 1rem;
}
.ml-3{
  margin-left: 1rem;
}
.mt-3{
  margin-top: 1rem;
}
.mb-3{
  margin-bottom: 1rem;
}

.m-4{
  margin: 1.5rem;
}
.my-4{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.mx-4{
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.mr-4{
  margin-right: 1.5rem;
}
.ml-4{
  margin-left: 1.5rem;
}
.mt-4{
  margin-top: 1.5rem;
}
.mb-4{
  margin-bottom: 1.5rem;

}

.m-5{
  margin: 2rem;
}
.my-5{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.mx-5{
  margin-left: 2rem;
  margin-right: 2rem;
}
.mr-5{
  margin-right: 2rem;
}
.ml-5{
  margin-left: 2rem;
}
.mt-5{
  margin-top: 2rem;
}
.mb-5{
  margin-bottom: 2rem;

}

.font-weight-light {
  font-weight: 300!important;
}
.font-weight-normal {
  font-weight: 400!important;
}
.font-weight-semibold {
  font-weight: 600!important;
}
.font-weight-bold {
  font-weight: 700!important;
}


.m-5 {
  margin: 3rem !important;
}
.my-5{
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;

}
.mx-5{
  margin-left: 3rem !important;
  margin-right: 3rem !important;
}

.mt--1px{
  margin-top: -1px;
}
.mb--1px{
  margin-bottom: -1px;
}
.mr--1px{
  margin-right: -1px;
}
.ml--1px{
  margin-right: -1px;
}



.p-0{
  padding: 0!important;
}
.py-0{
  padding-top: 0!important;
  padding-bottom: 0!important;
}
.px-0{
  padding-left: 0!important;
  padding-right: 0!important;
}
.pr-0{
  padding-right: 0!important;
}
.pl-0{
  padding-left: 0!important;
}
.pt-0{
  padding-top: 0!important;
}
.pb-0{
  padding-bottom: 0!important;
}

.p-1{
  padding: .25rem;
}
.py-1{
  padding-top: .25rem;
  padding-bottom: .25rem;
}
.px-1{
  padding-left: .25rem;
  padding-right: .25rem;
}
.pr-1{
  padding-right: .25rem
}
.pl-1{
  padding-left: .25rem
}
.pt-1{
  padding-top: .25rem
}
.pb-1{
  padding-bottom: .25rem
}

.p-2{
  padding: .5rem;
}
.py-2{
  padding-top: .5rem;
  padding-bottom: .5rem;
}
.px-2{
  padding-left: .5rem;
  padding-right: .5rem;
}
.pr-2{
  padding-right: .5rem
}
.pl-2{
  padding-left: .5rem
}
.pt-2{
  padding-top: .5rem
}
.pb-2{
  padding-bottom: .5rem
}

.p-3{
  padding: 1rem;
}
.py-3{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.px-3{
  padding-left: 1rem;
  padding-right: 1rem;
}
.pr-3{
  padding-right: 1rem
}
.pl-3{
  padding-left: 1rem
}
.pt-3{
  padding-top: 1rem
}
.pb-3{
  padding-bottom: 1rem
}

.p-4{
  padding: 1.5rem;
}
.py-4{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.px-4{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.pr-4{
  padding-right: 1.5rem
}
.pl-4{
  padding-left: 1.5rem
}
.pt-4{
  padding-top: 1.5rem
}
.pb-4{
  padding-bottom: 1.5rem
}

.p-5{
  padding: 3rem;
}
.py-5{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.px-5{
  padding-left: 3rem;
  padding-right: 3rem;
}
.pr-5{
  padding-right: 3rem
}
.pl-5{
  padding-left: 3rem
}
.pt-5{
  padding-top: 3rem
}
.pb-5{
  padding-bottom: 3rem
}

.text-disabled{
  color: #c0c0c0!important;
}
.text-white{
  color: #FFF;
}
.text-success{
  color: #19d895B2;
}
.text-warning {
  color: #ffaf00 !important;
}
.text-danger {
  color: #ff6258 !important;
}
.text-primary {
  color: #2196f3 !important;
}
.text-dark {
  color: #252C46 !important;
}
.text-black {
  color: #000 !important;
}
.text-secondary {
  color: #c0c2c3 !important;
}
.text-muted {
  color: #6c757d!important;
}

.bg-white {
  background-color: #FFFFFF!important;
}
.bg-success {
  background-color: #19d895!important;
}
.bg-inverse-success {
  background: rgba(25, 216, 149, 0.2);
}
.bg-danger{
  background-color: #ff6258!important;
}
.bg-inverse-danger {
  background: rgba(255, 98, 88, 0.2);
}
.bg-primary{
  background-color: #13AFF9!important;
}
.bg-inverse-primary{
  background-color: #13AFF933!important;
}
.bg-warning{
  background-color: #ffaf00!important;
}
.bg-inverse-warning {
  background: rgba(255, 175, 0, 0.2);
}
.bg-secondary{
  background-color: #C0C2C3!important;
}
.bg-inverse-secondary{
  background: #C0C2C333!important;
}
.bg-playstore{
  background-color: #009367!important;
}
.bg-reverse{
  background-color: black;
}
.bg-unset{
  background-color: unset;
}
.bg-light-grey{
  background: #F2F3F3!important;
}

.overflow-hidden{
  overflow: hidden;
}
.overflow-auto{
  overflow: auto;
}

.text-input{
  color: black;
  font-size: 16px;
}

.text{
  color: black;
}

.flex-between{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hover-shadow{
  transition: .3s;
}
.hover-shadow:hover{
  box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}

.m-h-25{
  min-height: 25%;
}
.m-h-50{
  min-height: 50%;
}
.m-h-100{
  min-height: 100%;
}

.h-auto{
  height: auto;
}
.h-25{
  height: 25%;
}
.h-50{
  height: 50%;
}
.h-100{
  height: 100%;
}

.h-10{
  height: 10px;
}
.h-5{
  height: 5px;
}
.h-1{
  height: 1px;
}

.rotate-45 {
  transform: rotate(45deg)
}
.rotate-90 {
  transform: rotate(90deg)
}
.rotate-180 {
  transform: rotate(180deg)
}
.rotate-270 {
  transform: rotate(270deg)
}
.rotate-360 {
  transform: rotate(360deg)
}

.transition-01 {
  transition: .1s;
}
.transition-02 {
  transition: .2s;
}
.transition-025 {
  transition: .25s;
}
.transition-03 {
  transition: .3s;
}
.transition-04 {
  transition: .4s;
}
.transition-05 {
  transition: .5s;
}

.font-size-025{
  font-size: .25rem;
}
.font-size-05{
  font-size: .5rem;
}
.font-size-07{
  font-size: .7rem;
}
.font-size-075{
  font-size: .75rem;
}
.font-size-085{
  font-size: .85rem;
}
.font-size-09{
  font-size: .9rem;
}
.font-size-1{
  font-size: 1rem;
}
.font-size-105{
  font-size: 1.05rem;
}
.font-size-11{
  font-size: 1.1rem;
}
.font-size-1125{
  font-size: 1.125rem;
}
.font-size-125{
  font-size: 1.25rem;
}
.font-size-15{
  font-size: 1.5rem;
}
.font-size-175{
  font-size: 1.75rem;
}
.font-size-2{
  font-size: 2rem;
}

.pointer-event-none {
  pointer-events: none;
}

ion-card{
  background-color: rgb(245, 245, 245);
}
.card-color{
  background-color: rgb(250, 250, 250);
}

.badge {
  display: inline-block;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  font-size: 11px;
  line-height: 1;
  padding: 4px 6px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
}
.badge:empty {
  display: inline-block;
  min-width: 10px;
  min-height: 10px;
  padding: 0;
  margin-right: 10px;
  border-radius: 100%;
}
.badge-success{
  background-color: #19d895;
  border: 1px solid #19d895;
  color: #ffffff;
}
.badge-inverse-success{
  background: rgba(25, 216, 149, 0.3);
  color: #19d895;
}
.badge-primary{
  background-color: #2196f3;
  border: 1px solid #2196f3;
  color: #ffffff;
}
.badge-inverse-primary{
  background: rgba(33, 150, 243, 0.3);
  color: #2196f3;
}
.badge-warning{
  background-color: #ffaf00;
  border: 1px solid #ffaf00;
  color: #ffffff;
}
.badge-inverse-warning{
  background: rgba(255, 175, 0, 0.3);
  color: #ffaf00;
}
.badge-danger{
  background-color: #ff6258;
  border: 1px solid #ff6258;
  color: #ffffff;
}
.badge-inverse-danger{
  background: rgba(255, 98, 88, 0.3);
  color: #ff6258;
}
.badge-secondary{
  background-color: #c0c2c3;
  border: 1px solid #c0c2c3;
  color: #ffffff;
}
.badge-inverse-secondary{
  background: rgba(192, 194, 195, 0.3);
  color: #c0c2c3;
}
.badge-dark{
  background-color: #252C46;
  border: 1px solid #252C46;
  color: #ffffff;
}
.badge-inverse-dark{
  background: rgba(37, 44, 70, 0.3);
  color: #252C46;
}


.alert{
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.alert-success {
  color: #13a471;
  background-color: rgba(25, 216, 149, 0.2);
  border-color: #17c789;
}
.alert-danger {
  color: #c24a43;
  background-color: rgba(255, 98, 88, 0.2);
  border-color: #eb5a51;
}
.alert-warning {
  color: #c28500;
  background-color: rgba(255, 175, 0, 0.2);
  border-color: #eba100;
}

.opacity-25{
  opacity: 0.25;
}
.opacity-50{
  opacity: 0.50;
}
.opacity-75{
  opacity: 0.75;
}

.content-bg-unset, .content-bg-unset *{
  background-color: unset!important;
}

.modal-container{
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: start;
  justify-content: center;
  padding-top: 20vh;
}

.position-relative{
  position: relative;
}
.position-absolute{
  position: absolute;
}

.btn{
  text-decoration: none;
  display: inline-block;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 0.56rem 1.375rem;
  line-height: 1;
  border-radius: 0.1875rem;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
.btn-sm{
  padding: 0.4rem 0.81rem;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.1875rem;
  text-decoration: none;
  display: inline-block;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}

.simple-alert{
  transition: .5s ease;
  position: absolute;
  width: 100%;
  bottom: -10vh;
  display: flex;
  justify-content: center;
}
.simple-alert.active{
  bottom: 20vh;
}
.simple-alert span{
  border-radius: 1rem;
  padding: .4rem .75rem;
  background-color: rgb(75, 75, 75,);
  box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 15%);
  color: white!important;
}

.btn-light {
  color: #212529;
  background-color: #fbfbfb;
  border-color: #fbfbfb;
}
.btn-light:hover {
  color: #212529;
  background-color: #e8e8e8;
  border-color: #e2e2e2;
}

.btn-dark {
  color: #fff;
  background-color: #252C46;
  border-color: #252C46;
}
.btn-dark:hover {
  color: #fff;
  background-color: #181c2d;
  border-color: #131725;
}
.btn-inverse-dark {
  color: #252C46;
  background-color: rgba(37, 44, 70, 0.2);
  background-image: none;
  border-color: rgba(37, 44, 70, 0);
}
.btn-inverse-dark:hover {
  color: #ffffff;
  background-color: #252C46;
  border-color: #252C46;
}

.btn-secondary{
  color: #fff;
  background-color: #c0c2c3;
  border-color: #c0c2c3;
}
.btn-secondary:hover{
  color: #fff;
  background-color: #acafb0;
  border-color: #a6a9aa;
}
.btn-inverse-secondary {
  color: #c0c2c3;
  background-color: rgba(192, 194, 195, 0.2);
  border-color: rgba(192, 194, 195, 0);
}
.btn-inverse-secondary:hover {
  color: #ffffff;
  background-color: #c0c2c3;
  border-color: #c0c2c3;
}


.btn-primary{
  color: #fff;
  background-color: #2196f3;
  border-color: #2196f3;
}
.btn-primary:hover{
  color: #fff;
  background-color: #0c83e2;
  border-color: #0c7cd5;
}
.btn-inverse-primary {
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.2);
  background-image: none;
  border-color: rgba(33, 150, 243, 0);
}
.btn-inverse-primary:hover {
  color: #ffffff;
  background-color: #2196f3;
  border-color: #2196f3;
}

.btn-danger {
  color: #fff;
  background-color: #ff6258;
  border-color: #ff6258;
}
.btn-danger:hover {
  color: #fff;
  background-color: #ff3e32;
  border-color: #ff3225;
}
.btn-inverse-danger{
  color: #ff6258;
  background-color: rgba(255, 98, 88, 0.2);
  background-image: none;
  border-color: rgba(255, 98, 88, 0);
}
.btn-inverse-danger:hover {
  color: #ffffff;
  background-color: #ff6258;
  border-color: #ff6258;
}

.btn-warning {
  color: #fff;
  background-color: #ffaf00;
  border-color: #ffaf00;
}
.btn-warning:hover {
  color: #FFF;
  background-color: #d99500;
  border-color: #cc8c00;
}
.btn-inverse-warning {
  color: #ffaf00;
  background-color: rgba(255, 175, 0, 0.2);
  background-image: none;
  border-color: rgba(255, 175, 0, 0);
}
.btn-inverse-warning:hover {
  color: #ffffff;
  background-color: #ffaf00;
  border-color: #ffaf00;
}

.btn-success {
  color: #FFF;
  background-color: #19d895;
  border-color: #19d895;
}
.btn-success:hover {
  color: #fff;
  background-color: #15b67d;
  border-color: #14aa75;
}
.btn-inverse-success {
  color: #19d895;
  background-color: rgba(25, 216, 149, 0.2);
  background-image: none;
  border-color: rgba(25, 216, 149, 0);
}
.btn-inverse-success:hover {
  color: #ffffff;
  background-color: #19d895;
  border-color: #19d895;
}

.btn-playsotre {
  color: #fff;
  background-color: #009367;
  border-color: #009367;
}
.btn-applestore {
  color: #fff;
  background-color: #13AFF9;
  border-color: #13AFF9;
}

.card{
  margin: 1rem;
  padding: 1rem;
  border-radius: 0.25rem;
}

.no-ripple{
  --ripple-color: transparent;
}
.modal-select-bottom{
  width: 100%;
  align-self: flex-end!important;
}

.fr-second-toolbar, .fr-newline{
  display: none!important;
}
.fr-wrapper{
  border-radius: 0 0 10px 10px!important;
}
.fr-element{
  border-top: 1px solid;
}
.fr-box{
  border: 1px solid;
  width: 100%!important;
}

.custom-date-picker-modal{
  width: 90vw;
  max-width: 750px;
  margin: 0;
}
.custom-date-picker{
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid #dee2e6 !important;
  margin-top: .5rem;
  padding-top: .5rem;
}
.custom-date-picker div{
  width: calc(100% / 7);
  aspect-ratio: 1/1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  transition: .3s;
}
.custom-date-picker div.disabled, .custom-date-picker div.prev-month, .custom-date-picker div.next-month{
  color: #c0c2c3;
  pointer-events: none;
}
.custom-date-picker div:hover, .custom-date-picker div:focus, .custom-date-picker div.active.current-month{
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.2);
  box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 15%);
}
.custom-date-picker-days{
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid #dee2e6 !important;
  margin-top: .5rem;
  padding-top: .5rem;
}
.custom-date-picker-days div{
  width: calc(100% / 7);
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (orientation: landscape) {
  .modal-container{
    padding-top: 15vh;
  }
  .custom-date-picker-modal{
    width: 70vh;
    max-width: 50vw;
    //padding-top: 5vh;
  }
}

.mh-25-vh{
  max-height: 25vh;
}
.mh-33-vh{
  max-height: 33vh;
}
.mh-50-vh{
  max-height: 50vh;
}
.mh-66-vh{
  max-height: 66vh;
}
.mh-75-vh{
  max-height: 75vh;
}

.m--1{
  margin: -0.25rem !important;
}
.mt--1{
  margin-top : -.25rem!important;
}
.mb--1{
  margin-bottom : -.25rem!important;
}
.mr--1{
  margin-right : -.25rem!important;
}
.ml--1{
  margin-left : -.25rem!important;
}
.mx--1{
  margin-left: -0.25rem !important;
  margin-right: -0.25rem !important;
}
.my--1{
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important;
}


.m--2{
  margin: -0.5rem !important;
}
.mt--2{
  margin-top : -.5rem!important;
}
.mb--2{
  margin-bottom : -.5rem!important;
}
.mr--2{
  margin-right : -.5rem!important;
}
.ml--2{
  margin-left : -.5rem!important;
}
.mx--2{
  margin-left: -0.5rem !important;
  margin-right: -0.5rem !important;
}
.my--2{
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important;
}

.m--3{
  margin: -1rem !important;
}
.mt--3{
  margin-top : -1rem!important;
}
.mb--3{
  margin-bottom : -1rem!important;
}
.mr--3{
  margin-right : -1rem!important;
}
.ml--3{
  margin-left : -1rem!important;
}
.mx--3{
  margin-left: -1rem !important;
  margin-right: -1rem !important;
}
.my--3{
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}
.centered-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.centered-buttons ion-button {
  margin: 0;
}

.fullscreen-modal {
  --width: 100% !important;
  --height: 100% !important;
  --max-width: 100% !important;
  --max-height: 100% !important;
  --border-radius: 0 !important;
  --box-shadow: none !important;
}

.fullscreen-modal .modal-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

//@media (prefers-color-scheme: dark) {
//  .fr-wrapper{
//    border-radius: 0 0 10px 10px!important;
//  }
//  .fr-toolbar, .fr-wrapper{
//    border: none!important;
//    background-color: rgb(30, 30, 30)!important;
//  }
//  .fr-element{
//    color: white!important;
//  }
//  .fr-toolbar .fr-command.fr-btn svg path,{
//    fill: white!important;
//  }
//  .fr-open, .fr-more-toolbar{
//    background-color: rgb(50, 50, 50)!important;
//  }
//
//
//  .shadow {
//    box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 35%);
//  }
//  .text{
//    color: white;
//  }
//  ion-card{
//    background-color: rgb(30, 30, 30);
//  }
//  .card-color{
//    background-color: rgb(40, 40, 40);
//  }
//
//  .bg-reverse{
//    background-color: white;
//  }
//}
