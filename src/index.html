<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>Ionic App</title>

  <base href="/" />

  <meta name="color-scheme" content="light dark" />
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="msapplication-tap-highlight" content="no" />

  <link rel="icon" type="image/png" href="assets/icon/favicon.png" />
  <link rel="stylesheet" href="assets/css/froala.css"  />
  <link rel="stylesheet" href="assets/css/main.css"  />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <link rel="stylesheet" href="https://test.ikbentessa.nl/client/css/app.css">

  <!-- add to homescreen for ios -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />
</head>

<div id="scanner-overlay">
    <div class="scanner-frame"></div>
    <div class="blur blur-top"></div>
    <div class="blur blur-bottom"></div>
    <div class="blur blur-left"></div>
    <div class="blur blur-right"></div>
</div>

<body>
  <div style="
    transition: .5s;
    position: absolute;
    width: 90%;
    top: -200px;
    margin: 0 5%;
    z-index: 999;
    backdrop-filter: blur(15px);"
   class="network-status p-3 rounded text-white" >
  </div>

  <app-root></app-root>
</body>
<script>

  const notification = document.querySelector('.network-status');

  setTimeout(() => {
    if(!window.navigator.onLine){ isOffline(); }
  }, 2500);

  window.addEventListener('online', isOnline);
  window.addEventListener('offline', isOffline);

  function isOnline(){
    console.log('Online');
    notification.innerHTML = `Je bent weer online!`
    notification.style.backgroundColor = `rgba(25, 216, 149, 0.65)`

    setTimeout(() => {
      notification.style.top = '-200px';
    }, 1000);
  }
  function isOffline(){
    console.log('Offline');
    notification.innerHTML = `Je bent Offline!`;
    notification.style.backgroundColor = `rgba(255, 98, 88, 0.65)`;
    notification.style.top = '65px';
  }

</script>
</html>
