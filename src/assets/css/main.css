/*Dot Glow*/
.dot-glow{
  display: inline-block;
  width: 10px;
  height: 10px;
  min-width: 10px;
  border-radius: 50rem;
  transition: .3s;
}
.dot-glow-sm{
  width: 7.5px;
  height: 7.5px;
  min-width: 7.5px;
}
.dot-glow-success{
  box-shadow: 0 0 0 0.25rem #19d89533;
  background-color: #19d895;
}
.dot-glow-danger{
  box-shadow: 0 0 0 .25rem #ff625833;
  background-color: #ff6258;
}
.dot-glow-secondary{
  box-shadow: 0 0 0 .25rem #c0c2c333;
  background-color: #c0c2c3;
}
.dot-glow-primary{
  box-shadow: 0 0 0 .25rem #2196f333;
  background-color: #2196f3;
}
.dot-glow-warning{
  box-shadow: 0 0 0 .25rem #ffaf0033;
  background-color: #ffaf00;
}

/*Display Flex*/
.flex-between{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-between-top{
  display: flex;
  justify-content: space-between;
  align-items: start;
}
.flex-between-bottom{
  display: flex;
  justify-content: space-between;
  align-items: end;
}
.flex-center{
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-align{
  display: flex;
  align-items: center;
}
.flex-start{
  display: flex;
  align-items: center;
  justify-content: start;
}
.flex-end{
  display: flex;
  align-items: center;
  justify-content: end;
}


/*Font size*/
.font-size-025{
  font-size: .25rem!important;
}
.font-size-05{
  font-size: .5rem!important;
}
.font-size-075{
  font-size: .75rem!important;
}
.font-size-08{
  font-size: .8rem!important;
}
.font-size-09{
  font-size: .9rem!important;
}
.font-size-1{
  font-size: 1rem!important;
}
.font-size-11{
  font-size: 1.1rem!important;
}
.font-size-12{
  font-size: 1.2rem!important;
}
.font-size-125{
  font-size: 1.25rem!important;
}
.font-size-15{
  font-size: 1.5rem!important;
}
.font-size-175{
  font-size: 1.75rem!important;
}
.font-size-2{
  font-size: 2rem!important;
}

/*Position*/
.left-0{
  left: 0;
}
.right-0{
  right: 0;
}
.top-0{
  top: 0;
}
.bottom-0{
  bottom: 0;
}

body{
  padding-bottom: env(safe-area-inset-bottom);
}

/*Scanner*/
body.scanner-active {
  --background: transparent;
  --ion-background-color: transparent;
}
body.scanner-active > *:not(#scanner-overlay) {
  display: none !important;
}
#scanner-overlay {
  position: fixed;
  inset: 0;
  z-index: 9999;
  pointer-events: none;
}

.scanner-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 250px;
  height: 125px;
  transform: translate(-50%, -50%);
  background-color: transparent;
  border: 3px solid #00FF00;
  z-index: 2;
}

#scanner-overlay .blur {
  position: absolute;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

#scanner-overlay .blur-top {
  top: 0;
  left: 0;
  right: 0;
  height: calc(50% - 62.5px);
}

#scanner-overlay .blur-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  top: calc(50% + 62.5px);
}

#scanner-overlay .blur-left {
  top: calc(50% - 62.5px);
  bottom: calc(50% - 62.5px);
  left: 0;
  width: calc(50% - 125px);
}

#scanner-overlay .blur-right {
  top: calc(50% - 62.5px);
  bottom: calc(50% - 62.5px);
  right: 0;
  width: calc(50% - 125px);
}
#scanner-overlay {
  display: none;
}
body.scanner-active #scanner-overlay {
  display: block;
}

.input-error {
  border: 2px solid rgba(255, 0, 0, .3) !important;
  border-radius: 4px;
}

ion-item.wrap-label ion-label {
  white-space: normal !important;
  text-overflow: initial !important;
  overflow: visible !important;
}
