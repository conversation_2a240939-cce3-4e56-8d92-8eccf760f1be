import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
	{
		path: '',
		redirectTo: 'login',
		pathMatch: 'full'
	},
	{
		path: 'settings',
		loadChildren: () => import('./settings/settings.module').then( m => m.SettingsPageModule)
	},
	// {
	// 	path: 'folder/:id',
	// 	loadChildren: () => import('./folder/folder.module').then( m => m.FolderPageModule)
	// },

	{
		path: 'test',
		loadChildren: () => import('./test/test.module').then( m => m.TestPageModule)
	},
	{
		path: 'home',
		loadChildren: () => import('./home/<USER>').then( m => m.HomePageModule)
	},
	{
		path: 'login',
		loadChildren: () => import('./login/login.module').then( m => m.LoginPageModule)
	},
	{
		path: 'correctie',
		loadChildren: () => import('./correctie/correctie.module').then( m => m.CorrectiePageModule)
	},
	{
		path: 'nieuws',
		loadChildren: () => import('./nieuws/nieuws.module').then( m => m.NieuwsPageModule)
	},
	{
		path: 'ureninvoeren',
		loadChildren: () => import('./ureninvoeren/ureninvoeren.module').then( m => m.UreninvoerenPageModule)
	},
	{
		path: 'urenregistratie',
		loadChildren: () => import('./urenregistratie/urenregistratie.module').then( m => m.UrenregistratiePageModule)
	},
	{
		path: 'verlof',
		loadChildren: () => import('./verlof/verlof.module').then( m => m.VerlofPageModule)
	},
	{
		path: 'weekoverzicht',
		loadChildren: () => import('./weekoverzicht/weekoverzicht.module').then( m => m.WeekoverzichtPageModule)
	},
	{
		path: 'werkbonnen',
		loadChildren: () => import('./werkbonnen/werkbonnen.module').then( m => m.WerkbonnenPageModule)
	},
	{
		path: 'werkbonnen/new',
		loadChildren: () => import('./werkbonnen/new/new.module').then( m => m.NewPageModule)
	},
	{
		path: 'werkbon',
		loadChildren: () => import('./werkbonnen/werkbon/werkbon.module').then( m => m.WerkbonPageModule)
	},
	{
		path: 'planning',
		loadChildren: () => import('./planning/planning.module').then( m => m.PlanningPageModule)
	},
	{
		path: 'dagplanning',
		loadChildren: () => import('./planning/dagplanning/dagplanning.module').then( m => m.DagplanningPageModule)
	},
	{
		path: 'galerij',
		loadChildren: () => import('./galerij/galerij.module').then( m => m.GalerijPageModule)
	},
	{
		path: 'uploadimg',
		loadChildren: () => import('./galerij/upload/upload.module').then( m => m.UploadPageModule)
	},
	{
		path: 'klanten',
		loadChildren: () => import('./klanten/klanten.module').then( m => m.KlantenPageModule)
	},
	{
		path: 'klant',
		loadChildren: () => import('./klanten/klant/klant.module').then( m => m.KlantPageModule)
	},
	{
		path: 'klant/edit',
		loadChildren: () => import('./klanten/klant/edit/edit.module').then( m => m.EditPageModule )
	},
	{
		path: 'call/response',
		loadChildren: () => import('./klanten/response/response.module').then( m => m.ResponsePageModule)
	},
	{
		path: 'klanten/aanvragen',
		loadChildren: () => import('./klanten/overview/aanvragen/aanvragen.module').then( m => m.AanvragenPageModule)
	},
	{
		path: 'klanten/offertes',
		loadChildren: () => import('./klanten/overview/offertes/offertes.module').then( m => m.OffertesPageModule)
	},
	{
		path: 'klanten/projecten',
		loadChildren: () => import('./klanten/overview/projecten/projecten.module').then( m => m.ProjectenPageModule)
	},
	{
		path: 'klanten/werkbonnen',
		loadChildren: () => import('./klanten/overview/werkbonnen/werkbonnen.module').then( m => m.WerkbonnenPageModule)
	},
	{
		path: 'klanten/rapporten',
		loadChildren: () => import('./klanten/overview/rapporten/rapporten.module').then( m => m.RapportenPageModule)
	},
	{
		path: 'klanten/checklists',
		loadChildren: () => import('./klanten/overview/checklists/checklists.module').then( m => m.ChecklistsPageModule)
	},
	{
		path: 'rapporten',
		loadChildren: () => import('./rapporten/rapporten.module').then( m => m.RapportenPageModule)
	},
	{
		path: 'rapport',
		loadChildren: () => import('./rapporten/rapport/rapport.module').then(m => m.RapportPageModule)
	},
	{
		path: 'newrapport',
		loadChildren: () => import('./rapporten/new/new.module').then(m => m.NewPageModule)
	},
	{
		path: 'newrapport/modal',
		loadChildren: () => import('./rapporten/modal/modal.module').then(m => m.ModalPageModule)
	},
	{
		path: 'rapporten/templates',
		loadChildren: () => import('./rapporten/templates/templates.module').then(m => m.TemplatesPageModule)
	},
	{
		path: 'offertes',
		loadChildren: () => import('./offertes/offertes.module').then(m => m.OffertesPageModule)
	},
	{
		path: 'checklists',
		loadChildren: () => import('./checklists/checklists.module').then(m => m.ChecklistsPageModule)
	},
	{
		path: 'checklists/new',
		loadChildren: () => import('./checklists/new/new.module').then(m => m.NewPageModule)
	},
	{
		path: 'checklists/show',
		loadChildren: () => import('./checklists/show/show.module').then(m => m.ShowPageModule)
	},
  {
    path: 'checklists/project',
    loadChildren: () => import('./checklists/project/project.module').then(m => m.ProjectPageModule)
  },
	{
		path: 'actielijst',
		loadChildren: () => import('./actielijst/actielijst.module').then(m => m.ActielijstPageModule)
	},
	{
		path: 'beschikbaarheid',
		loadChildren: () => import('./beschikbaarheid/beschikbaarheid.module').then(m => m.BeschikbaarheidPageModule)
	},
	{
		path: 'bestanden',
		loadChildren: () => import('./bestanden/bestanden.module').then(m => m.BestandenPageModule)
	},
	{
		path: 'nav-order',
		loadChildren: () => import('./nav-order/nav-order.module').then(m => m.NavOrderPageModule)
	},
	{
		path: 'iframe',
		loadChildren: () => import('./iframe/iframe.module').then(m => m.IframePageModule)
	},
	{
		path: 'projecten',
		loadChildren: () => import('./projecten/projecten.module').then(m => m.ProjectenPageModule)
	},
	{
		path: 'projecten/create',
		loadChildren: () => import('./projecten/create/create.module').then(m => m.CreatePageModule)
	},
	{
		path: 'aanvragen',
		loadChildren: () => import('./aanvragen/aanvragen.module').then(m => m.AanvragenPageModule)
	},
	{
		path: 'aanvragen/new',
		loadChildren: () => import('./aanvragen/new/new.module').then(m => m.NewPageModule)
	},
	{
		path: 'aanvraag',
		loadChildren: () => import('./aanvragen/aanvraag/aanvraag.module').then( m => m.AanvraagPageModule)
	},
	{
		path: 'wachtwoordenkluis',
		loadChildren: () => import('./wachtwoordenkluis/wachtwoordenkluis.module').then(m => m.WachtwoordenkluisPageModule)
	},
	{
		path: 'wachtwoordenkluis/aanvragen',
		loadChildren: () => import('./wachtwoordenkluis/aanvragen/aanvragen.module').then(m => m.AanvragenPageModule)
	},
	{
		path: 'toolbox',
		loadChildren: () => import('./toolbox/index/index.module').then(m => m.IndexPageModule)
	},
	{
		path: 'toolbox/beantwoorden',
		loadChildren: () => import('./toolbox/beantwoorden/beantwoorden.module').then(m => m.BeantwoordenPageModule)
	},
	{
		path: 'declaratie',
		loadChildren: () => import('./declaratie/index/index.module').then(m => m.IndexPageModule)
	},
	{
		path: 'declaratie/overzicht',
		loadChildren: () => import('./declaratie/overzicht/overzicht.module').then(m => m.OverzichtPageModule)
	},
  {
    path: 'memo',
    loadChildren: () => import('./memo/memo/memo.module').then( m => m.MemoPageModule)
  },
  {
    path: 'todo',
    loadChildren: () => import('./todo/todo.module').then( m => m.TodoPageModule)
  },
  {
    path: 'whatsapp',
    loadChildren: () => import('./whatsapp/whatsapp.module').then( m => m.WhatsappPageModule)
  },
  {
    path: 'draw/canvas',
    loadChildren: () => import('./draw/canvas/canvas.module').then( m => m.CanvasPageModule) // ?
  },
  { 
    path: 'reset-password',
    loadChildren: () => import('./reset-password/reset-password.module').then( m => m.ResetPasswordPageModule)
  },
  {
    path: 'users',
    loadChildren: () => import('./users/users.module').then( m => m.UsersPageModule)
  }



];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
