<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug"></ion-back-button>
    </ion-buttons>
    <ion-title>Nieuwsoverzicht</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list style="height: 100%" *ngIf="loading">
    <ion-item>
      <ion-label>Laden...</ion-label>
      <ion-spinner name="lines" ></ion-spinner>
    </ion-item>
  </ion-list>


  <ion-card *ngFor="let article of articles; let i = index">
    <div *ngIf="article.image"><img src="{{ 'https://' + subdomain + '.ikbentessa.nl/api/file/news/' + article.image }}" alt=""></div>
    <ion-card-header>
      <ion-card-title>{{ article.title }}</ion-card-title>
    </ion-card-header>

    <ion-card-content class="pre">
      <div class="pre" ng-bind-html="markdown.toHTML(article.content)" [innerHTML]="article.content"></div>
<!--      {{ article.content }}-->
      <div *ngFor="let file of article.files; let i = index">
        <a href="https://{{ subdomain }}.ikbentessa.nl/api/file/news/uploads/{{ file.file }}">
          <ion-icon name="attach"></ion-icon> {{ file.file }}
        </a>
      </div>
    </ion-card-content>
  </ion-card>

  <div expand="full" *ngIf="articles.length == 15"><ion-button (click)="getAllNews()">Meer laden</ion-button></div>

</ion-content>
<style>
.pre {
  white-space: pre-wrap;
}
a {
  text-decoration: none;
  color: #000;
}
</style>
