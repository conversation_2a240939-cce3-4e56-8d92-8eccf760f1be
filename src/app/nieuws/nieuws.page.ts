import {Component, OnInit} from '@angular/core';
import { Infordb } from 'infordb';
import axios from 'axios';

@Component({
  selector: 'app-nieuws',
  templateUrl: './nieuws.page.html',
  styleUrls: ['./nieuws.page.scss'],
	providers: [Infordb],
})
export class NieuwsPage implements OnInit {

  articles = [];
  loading = true;
  subdomain = window.localStorage.getItem('subdomain');

  constructor(
    private infordb: Infordb
  ) { }

  ngOnInit() {
    this.getRecentNews();
  }

  getRecentNews() {
	  this.infordb.get(`https://${this.subdomain}.ikbentessa.nl/api/news/recent`)
      .then(res => {
        this.articles = res.data;
        this.loading = false;
      })
	    .catch(this.infordb.handleError);
  }

  getAllNews() {
    this.infordb.get(`https://${this.subdomain}.ikbentessa.nl/api/news`)
	    .then(res => {
		    this.articles = res.data;
	    })
	    .catch(this.infordb.handleError);
  }

  doRefresh(event){
    this.getRecentNews();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

}
