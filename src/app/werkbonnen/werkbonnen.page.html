<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Werkbonnen inzien</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >

  <div class="ion-text-center my-2">
    <a class="btn-sm btn-inverse-primary mx-1" *ngIf="local.length" (click)="modals.local = true;" >Lokale werkbonnen ( {{local.length}} )</a>
      <!-- Show modal to select template -->
      <a class="btn-sm btn-success mx-1 text-white"
         *ngIf="user.user_permissions['Werkbonnen aanmaken']"
         (click)="newWerkbon()">
          Nieuwe werkbon
      </a>
  </div>

  <ion-card>
    <ion-item>
      <ion-label position="floating">Zoeken</ion-label>
      <ion-input [(ngModel)]="search"></ion-input>
    </ion-item>
  </ion-card>

  <!-- filter on status and template -->
  <ion-card class="my-2">
    <ion-item>
      <ion-label>Status</ion-label>
      <ion-select [(ngModel)]="filter.status" multiple="true" cancelText="Annuleren" okText="Ok" >
        <ion-select-option *ngFor="let status of statussen" [value]="status" >{{status}}</ion-select-option>
      </ion-select>
    </ion-item>
    <ion-item>
      <ion-label>Template</ion-label>
      <ion-select [(ngModel)]="filter.template" multiple="true" cancelText="Annuleren" okText="Ok" >
        <ion-select-option *ngFor="let template of templates" [value]="template.id" >{{template.naam}}</ion-select-option>
      </ion-select>
    </ion-item>
  </ion-card>

  <div *ngIf="post && !werkbonnen.length" class="my-2" >
    <h5 class="ion-text-center">Geen werkbonnen gevonden</h5>
  </div>

  <div *ngIf="!post" class="my-2 ion-text-center" ><ion-spinner></ion-spinner></div>

  <div *ngFor="let werkbon of werkbonnen">
    <ion-card *ngIf="includes(werkbon) && filter.status.includes(werkbon.status) && filter.template.includes(werkbon.template.id)" class="bg-white text-dark font-size-1 p-2 my-2" >
      <div class="flex-between mb-3">
        <ion-card-title class="ion-text-center" ><small>{{werkbon.template.naam}}:</small> {{werkbon.werkbonnummer}}</ion-card-title>
        <span class="badge badge-{{werkbon.color}}" >{{werkbon.status}}</span>
      </div>
      <div class="text d-flex ion-justify-content-between " (click)="setShow('buttons', werkbon.id)">
        <h4 class="m-0" >Informatie</h4>
        <h4 class="m-0 d-flex" *ngIf="!show.buttons[werkbon.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
        <h4 class="m-0 d-flex" *ngIf="show.buttons[werkbon.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
      </div>
      <div *ngIf="show.buttons[werkbon.id]">
        <div class="py-1 border-bottom" *ngIf="werkbon.klant">
          <b class="d-block">{{werkbon.klant.naam ? werkbon.klant.naam : werkbon.klant.contactpersoon_voornaam+' '+werkbon.klant.contactpersoon_achternaam}}</b>
          <div class="d-flex ion-align-items-center text-primary" (click)="call(werkbon.id)" *ngIf="werkbon.klant.telefoonnummer">
            <ion-text>{{werkbon.klant.telefoonnummer}}</ion-text>
            <ion-icon class="mx-1" name="call-outline"></ion-icon>
          </div>
          <ion-text class="d-block">{{werkbon.klant.email || ''}}</ion-text>
        </div>
        <div class="py-1 border-bottom" *ngIf="werkbon.project">
          <b class="d-block">Project</b>
          <ion-text class="d-block">{{werkbon.project.projectnr}}</ion-text>
          <ion-text class="d-block">{{werkbon.project.projectnaam || ''}}</ion-text>
        </div>
        <div class="py-1 border-bottom">
          <b class="d-block">Medewerker</b>
          <ion-text class="d-block">{{werkbon.user.name}} {{werkbon.user.lastname}}</ion-text>
          <ion-text class="d-block">{{werkbon.datum}}</ion-text>
        </div>
        <div class="py-1 border-bottom" *ngIf="werkbon.vestiging">
          <b class="d-block">Vestiging</b>
          <ion-text class="d-block">{{werkbon.vestiging.straat}} {{werkbon.vestiging.huisnummer}}{{werkbon.vestiging.toevoeging}}</ion-text>
          <ion-text class="d-block">{{werkbon.vestiging.postcode}} {{werkbon.vestiging.plaats}}</ion-text>
        </div>
        <div class="py-1 border-bottom" *ngIf="werkbon.keywords">
          <b class="d-block">Velden</b>
          <div *ngFor="let value of werkbon.keywords">
            <ion-text class="d-block">{{value.titel}}: {{!isDate(value.value) ? value.value : convertDate(value.value)}}</ion-text>
          </div>
        </div>
        <div class="py-1 border-bottom" *ngIf="werkbon.opvolg_values">
          <b class="d-block">{{werkbon.status}}</b>
          <div *ngFor="let value of werkbon.opvolg_values">
            <ion-text class="d-block">{{value.titel}}: {{!isDate(value.value) ? value.value : convertDate(value.value)}}</ion-text>
          </div>
        </div>
        <div class="py-1 border-bottom" *ngIf="werkbon.opmerking">
          <b class="d-block">Opmerking</b>
          <ion-text class="d-block">{{werkbon.opmerking}}</ion-text>
        </div>
        <div *ngIf="werkbon.klant && werkbon.template.klant_signature === '1' && !werkbon.klant_signature" >
          <div class="alert alert-danger">Niet ondertekend door opdrachtgever!</div>
        </div>
      </div>
      <div class="ion-text-center my-2 mx--1">
        <a class="btn btn-inverse-primary mx-1" (click)="inzien(werkbon.id)" >Inzien</a>
        <a class="btn btn-inverse-primary mx-1" *ngIf="user.user_permissions['Werkbonnen wijzigen ( App )'] && werkbon.status == 'Uitgebracht'" (click)="edit(werkbon)" >Wijzigen</a>
        <a class="btn btn-inverse-primary mx-1" *ngIf="isAfrondenEnabled(user, werkbon) && !user.values['werkbonnen_app_versturen']" (click)="afronden(werkbon)">Afronden</a>
        <a class="btn btn-inverse-primary mx-1" *ngIf="isAfrondenEnabled(user, werkbon) && user.values['werkbonnen_app_versturen'] && user.user_permissions['Werkbonnen wijzigen ( App )']" (click)="versturen(werkbon)">Versturen</a>
      </div>
    </ion-card>
  </div>

  </ion-card>


  <!--  Template modal-->
  <div class="modal-container" *ngIf="modals.templates" (click)="modals.templates = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" >
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div *ngIf="!templates.length" class="ion-text-center py-4" >
        <ion-spinner color="primary"></ion-spinner>
      </div>
      <div *ngFor="let template of templates" class="my-2" >
        <div class="ion-text-center my-2"  >
          <ion-button color="primary" (click)="selectTemplate(template.id)" class="text" fill="clear" ><ion-text>{{template.naam}}</ion-text></ion-button>
        </div>
      </div>
    </ion-card>
  </div>

  <!--  Local modal-->
  <div class="modal-container" *ngIf="modals.local" (click)="modals.local = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" >
        <ion-label>Selecteer werkbon</ion-label>
      </ion-item>
      <div *ngFor="let row of local; let i = index" class="my-2" >
        <div class="flex-between w-100 my-2"  >
          <ion-button class="mx-2" color="primary" fill="clear" (click)="selectLocal(i)" >
            <div class="flex-between mx--1">
              <div class="mx-2 ion-text-left" >
                <div>{{row.name}}</div>
                <small *ngIf="row.edit" class="d-block" >{{row.edit.werkbonnummer}}</small>
              </div>
              <span>|</span>
              <div class="mx-1 ion-text-left" >
                <small class="d-block" >{{row.time.date}} {{row.time.time}}</small>
                <small class="d-block" >{{row.template.naam}}</small>
              </div>
            </div>
          </ion-button>

          <ion-button class="mx-1" fill="clear" color="danger" (click)="deleteLocal(i)" *ngIf="!this.user.values['app_local_delete_off']"><ion-icon name="close-outline"></ion-icon></ion-button>

        </div>
      </div>
    </ion-card>
  </div>

<!--  Afronden modal-->
  <div class="modal-container" *ngIf="modals.complete" (click)="modals.complete = false;" >
    <ion-card class="p-2" (click)="$event.stopPropagation()" >
      <div class="font-size-1 text-dark py-2" >Weet je zeker dat je de werkbon <b>{{_complete.werkbon.werkbonnummer}}</b> wilt afronden</div>
      <div class="ion-text-center pt-2">
        <a class="btn btn-success" (click)="confirmAfronden()" >Afronden</a>
      </div>
    </ion-card>
  </div>

  <!-- versturen modal -->
  <div class="modal-container" *ngIf="modals.send" (click)="modals.send = false;" >
    <ion-card class="p-2" (click)="$event.stopPropagation()" >
      <div class="font-size-1 text-dark py-2" >Weet je zeker dat je de werkbon <b>{{_send.werkbon.werkbonnummer}}</b> naar {{_send.email}} wilt versturen {{isSigned(_send.werkbon) ? 'en afronden' : ''}}</div>
      <div class="ion-text-center pt-2">
        <a class="btn btn-success" (click)="confirmSend()" >Versturen</a>
      </div>
    </ion-card>
  </div>

</ion-content>
