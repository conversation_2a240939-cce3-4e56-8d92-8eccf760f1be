import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NavController } from "@ionic/angular";
import { Infordb } from "infordb";
import {ClientStorageService} from "../services/ClientStorage/client-storage.service";

@Component({
  selector: 'app-verlof',
  templateUrl: './werkbonnen.page.html',
  styleUrls: ['./werkbonnen.page.scss'],
  providers: [Infordb],
})
export class WerkbonnenPage{

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));

  public search: any;
  public post = false;

  public templates = [];
  public werkbonnen = [];
  public statussen = [];
  public klanten = [];
  public users = [];
  public local = [];
  public preselectedTemplateId = null;
  dates: any;

  public modals = {
    templates: false,
    local: false,
    complete: false,
    send: false,
  }
  public _complete = {
    werkbon: null,
  }
  public _send = {
    werkbon: null,
    email: '',
  }

  public show = {
    buttons: {},
  };

  public filter = {
    status: [],
    template: [],
  }

  constructor(
    public infordb: Infordb,
    public router: Router,
    public nav: NavController,
    private clientStorage: ClientStorageService,
) { }

  async ionViewWillEnter() {
    this.handlePreselectedTemplate();
    await this.checkUrenIngevuld();
    this.local = this.clientStorage.get('werkbonnen_local_data', []);
    this.post = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/index`, {
      user: this.user.user_id.toString(),
      project_id: JSON.parse(localStorage.selectedProject ?? null),
    })
        .then(response => {
          if (response.status == 201) {
            const {templates, werkbonnen} = response.data;

            this.templates = templates;
            this.werkbonnen = werkbonnen;

            this.statussen = Array.from(new Set(this.werkbonnen.map((werkbon: { status: string }) => werkbon.status)));
            this.filter.status = this.statussen;
            this.filter.template = this.templates.map((template: { id: any }) => template.id);

            if (this.preselectedTemplateId) {
              const selectedId = Number(this.preselectedTemplateId);
              this.filter.template = [selectedId];
            }else{
              const id = localStorage.selectedProjectTemplateId;
              this.filter.template = id != null ? [Number(id)] : this.templates.map((template: { id: any }) => template.id);
            }

            this.post = true;
            localStorage.removeItem('selectedProject');
            localStorage.removeItem('selectedProjectTemplateId');
          }
        })
        .catch(this.infordb.handleError);
  }

  inzien(id){
    window.localStorage.setItem('werkbonId', id);
    this.callPage('werkbon');
  }
  afronden(werkbon){
    this._complete.werkbon = werkbon;
    this.modals.complete = true;
  }
  versturen(werkbon){
    this._send.werkbon = werkbon;
    this._send.email = werkbon.klant.contactpersoon_email ? werkbon.klant.contactpersoon_email : werkbon.klant.email;
    this.modals.send = true;
  }
  edit(werkbon){
    localStorage.werkbonEdit = werkbon.id
    this.callPage('werkbonnen/new');
  }

  confirmAfronden(){
    this.post = false;
    this.modals.complete = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/afronden`, {
      user: this.user.user_id.toString(),
      id: this._complete.werkbon.id.toString(),
    })
    .then(() => {
      this._complete.werkbon.status = 'Afgerond';
      this.post = true;
    })
    .catch(err => {
      this.post = true;
      this.infordb.handleError(err);
    });
  }

  confirmSend(){
    this.post = false;
    this.modals.send = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/send/signature`, {
      emails: this._send.email,
      id: this._send.werkbon.id.toString(),
    })
    .then(() => {
      if(this.isSigned(this._send.werkbon)){
        this._send.werkbon.status = 'Afgerond';
      }
      this.post = true;
    })
    .catch(err => {
      this.post = true;
      this.infordb.handleError(err);
    });
  }


  includes(werkbon){
    if(!this.search || this.search == ''){return true;}

    let str = werkbon.werkbonnummer;

    if(werkbon.klant){
      const {telefoonnummer, email} = werkbon.klant;
      str += (werkbon.opdrachtgever||'');
      str += (telefoonnummer||'');
      str += (email||'');
    }
    if(werkbon.project){
      const {projectnr, projectnaam} = werkbon.project;
      str += (projectnr||'');
      str += (projectnaam||'');
    }
    if(werkbon.vestiging){
      const {straat, huisnummer, toevoeging, postcode, plaats} = werkbon.vestiging;
      str += (straat||'');
      str += (huisnummer||'');
      str += (toevoeging||'');
      str += (postcode||'');
      str += (plaats||'');
    }
    if(werkbon.template.klant_signature === '1' && !werkbon.klant_signature){
      str += 'Niet ondertekend door opdrachtgever';
    }

    const {name, lastname} = werkbon.user;

    str += (name||'');
    str += (lastname||'');
    str += werkbon.datum;

    str = str.toLowerCase().replaceAll(' ', '');

    return str.includes(this.search.toLowerCase().replaceAll(' ', ''));
  }

  selectLocal(i){
    localStorage.werkbonnen_local_selected = i;
    localStorage.checklistTemplateId = this.local[i].template.id;

    this.modals.local = false;
    this.callPage('werkbonnen/new');
  }
  deleteLocal(i){
    this.local.splice(i, 1);
    this.clientStorage.set('werkbonnen_local_data', this.local);
  }


  call(id){
    const werkbon = this.werkbonnen.find(row => row.id == id);
    const {naam, contactpersoon_voornaam, contactpersoon_achternaam, telefoonnummer} = werkbon.klant;

    const name = naam || (contactpersoon_voornaam || '')+' '+(contactpersoon_achternaam || '')
    if(confirm(`Wilt u ${name} bellen?`)){
			window.location.href = `tel: ${telefoonnummer}`;
		}
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }
  newWerkbon() {
    if (this.preselectedTemplateId) {
      localStorage.setItem('werkbonTemplateId', this.preselectedTemplateId);
      this.router.navigate(['/werkbonnen/new']);
    } else {
      this.modals.templates = true
    }
  }

  doRefresh(event){
    this.ionViewWillEnter();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  handlePreselectedTemplate() {
    this.preselectedTemplateId = localStorage.getItem('preselectedTemplateId');
    localStorage.removeItem('preselectedTemplateId');
  }
  selectTemplate(id){
    localStorage.werkbonTemplateId = id;
    this.modals.templates = false;
    this.callPage('werkbonnen/new');
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() +1)).slice(-2) + '-' + date.getFullYear();
  }

  isDate(d){
    if(!d.includes('-') && !d.includes('/')){return false;}
    return !isNaN(new Date(d).getTime());
  }

  setShow(target, id){
    this.show[target][id] = !this.show[target][id];
  }

  async checkUrenIngevuld(){
    await this.getDatums();
    if(this.user.values.werkbonnen_app_blok_uren_incompleet?.value && this.previousDayNotFilled(this.dateNow())){
      this.infordb.notification({message: 'Zorg dat eerst dat je urenregistratie compleet is', duration: 4000})
      this.callPage('home')
    }
  }

  async getDatums() {
    this.dates = [];

    try {
      const res = await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/datums`, {
        token: this.user.api_token
      });

      this.dates = res.data;
    } catch (error) {
      this.infordb.handleError(error);
    }
  }

  previousDayNotFilled(dateString) {
    let date = new Date(dateString);

    let subtract = 1;

    if (date.getDay() == 6 || date.getDay() == 0) {
      return false;
    }

    if (date.getDay() == 1){
      subtract = 3;
    }

    date.setDate(date.getDate() - subtract);
    let yesterday = this.convertDate(date);

    let yesterdaysEntry = this.dates.find(entry => entry.datum === yesterday)

    if(!yesterdaysEntry || (!yesterdaysEntry.feestdagen && !yesterdaysEntry.verlof && !yesterdaysEntry.ingevoerd)){
      return true
    }

    return false;
  }

  dateNow(){
    const datum = new Date();
    return datum.getFullYear() + '-' + ('0' + (datum.getMonth() + 1)).slice(-2) + '-' + ('0' + datum.getDate()).slice(-2);
  }

  isAfrondenEnabled(user: any, werkbon: any): boolean {
    return (
      ((user.user_permissions['Werkbonnen beheren'] ||
      (user.user_permissions['Werkbonnen afronden ( App )'] && werkbon.user_id == user.user_id)) && user.user_permissions['Werkbonnen afronden ( App )'] && !user.values['werkbon_opvolgstappen'] && werkbon.status != 'Afgerond') ||
      ((user.user_permissions['Werkbonnen beheren'] ||
      (user.user_permissions['Werkbonnen afronden ( App )'] && werkbon.user_id == user.user_id)) && user.user_permissions['Werkbonnen afronden ( App )'] && user.values['werkbon_opvolgstappen'] && werkbon.status != 'Afgerond' && werkbon.status != 'Uitgebracht')
    );
  }
  isSigned(werkbon: any): boolean {
    return werkbon.template.klant_signature === '1' && werkbon.klant_signature;
  }
}
