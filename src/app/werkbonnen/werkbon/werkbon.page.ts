import { Component, OnInit } from '@angular/core';
import { convertDate, Infordb } from 'infordb';

@Component({
  selector: 'app-werkbon',
  templateUrl: './werkbon.page.html',
  styleUrls: ['./werkbon.page.scss'],
  providers: [Infordb]
})
export class WerkbonPage implements OnInit {

  public convertDate = convertDate;

  public post = false;

  public subdomain = window.localStorage.getItem('subdomain');
  public werkbon;
  public keywords = [];

  public db;
  public klant;
  public project;
  public opties;
  public optiesKeys;
  public user;
  public offerte;
  public offerteTeksten;
  public customInfo;
  public customInfoKeys;
  public bvs;
  public defPersonen;

  public handtekeningen = [];
  public files = [];

  public link = 'https://' + this.subdomain + '.ikbentessa.nl/api/file/';

  public show = {
    vestiging: false,
    klant: false,
    offerte: false,
  }
  public base = {
    input_slot: false,
  }


  constructor(
      private infordb: Infordb,
  ) {}

  ngOnInit() {}
  ionViewWillEnter() {
    const id = localStorage.werkbonId;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/inzien`, {
      werkbonId: id.toString(),
    })
      .then(response => {
        if (response.status != 201) { throw response }
          const {werkbon, keywords, db, klant, project, opties, user, offerte, offerteTeksten, bvs, customInfo, defPersonen} = response.data;
          this.werkbon = werkbon;
          this.db = db;
          this.klant = klant;
          this.project = project;
          this.opties = opties;
          this.user = user;
          this.offerte = offerte;
          this.offerteTeksten = offerteTeksten;
          this.bvs = bvs;
          this.customInfo = customInfo;
          this.defPersonen = defPersonen;

          this.keywordsInit(keywords);

          if(this.offerte && this.customInfo[this.offerte.template.id]){
            this.customInfoKeys = Object.keys(this.customInfo[this.offerte.template.id]);
          }
          if(this.opties){
            this.optiesKeys = Object.keys(this.opties);
          }

          if(this.werkbon.handtekeningen != null){
            this.handtekeningen = JSON.parse(this.werkbon.handtekeningen);
          }
          if(this.werkbon.files != null){
            this.files = JSON.parse(this.werkbon.files);
          }

          for(const o in this.opties){
            for(const row of this.opties[o]){
              row.value = this.jsonToArray(row.value);
            }
          }
          this.post = true;
      })
      .catch(this.infordb.handleError);
  }

  keywordsInit(keywords){
    for(const item of keywords){
      if(item.type == 'input_slot'){ this.base.input_slot = true; }

      if(item.type == 'list_select'){
        item.value = JSON.parse(item.value || '[]');
      }
      if(item.type == 'list_input'){
        item.value = JSON.parse(item.value || '[]');
        console.log(item);
      }
      if(item.type == 'images'){
        item.value = JSON.parse(item.value);
      }

      if(!item.input || !item.input.data){continue;}
      item.input.data = JSON.parse(item.input.data);
    }
    this.keywords = keywords;
  }

  verifyParent(item){
    for(const row of this.keywords){
      if(row.keyword != item.input.parent_keyword){continue;}
      return row.value == item.input.parent_value
    }
  }

  jsonToArray(input){
    let obj = JSON.parse(input);
    const rtn = [];
    for (const key in obj){
      const value = obj[key];
      rtn.push({key: key, value: value});
    }
    return rtn;
  }
}
