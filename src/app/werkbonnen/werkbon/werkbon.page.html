<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Werkbon</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="!post" >
  <div class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>
</ion-content>

<ion-content *ngIf="post" >
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >

<!--  Project-->
    <ion-card class="bg-white" *ngIf="project" >
    <div class="ion-padding flex-between" >
      <b *ngIf="project.projectnr" class="font-size-1 text-dark ">{{project.projectnr}}</b>
      <ion-label *ngIf="project.projectnaam" class="font-size-1" >{{project.projectnaam}}</ion-label>
    </div>
    <ion-item color="none" lines="none" *ngFor="let taak of werkbon.project_taken" >
      <ion-icon color="primary" name="checkbox-outline"></ion-icon>
      <ion-label class="ion-margin-horizontal" >{{taak.name}}</ion-label>
    </ion-item>
  </ion-card>

<!--  vestiging-->
    <ion-card class="ion-padding text-dark bg-white" *ngIf="werkbon && werkbon.vestiging">
      <div (click)="show.vestiging = !show.vestiging;" class="flex-between">
        <ion-label>Vestiging</ion-label>
        <ion-icon name="{{show.vestiging ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
      </div>
      <ion-grid [hidden]="!show.vestiging" class="p-0 mx--1 my-2" >
        <ion-row>
          <ion-col size="6"><b>Adres</b></ion-col>
          <ion-col size="6">{{werkbon.vestiging.straat}} {{werkbon.vestiging.huisnummer}}{{werkbon.vestiging.toevoeging}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Plaats</b></ion-col>
          <ion-col size="6">{{werkbon.vestiging.plaats}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Postcode</b></ion-col>
          <ion-col size="6">{{werkbon.vestiging.postcode}}</ion-col>
        </ion-row>
      </ion-grid>
    </ion-card>

<!--  Klant-->
    <ion-card class="ion-padding text-dark bg-white" *ngIf="werkbon && klant">
      <div (click)="show.klant = !show.klant;" class="flex-between">
          <ion-label>Klant</ion-label>
          <ion-icon name="{{show.klant ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
      </div>
      <ion-grid [hidden]="!show.klant" class="p-0 mx--1 my-2" >
        <ion-row>
            <ion-col size="6"><b>Voornaam</b></ion-col>
            <ion-col size="6">{{klant.contactpersoon_voornaam}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Achternaam</b></ion-col>
          <ion-col size="6">{{klant.contactpersoon_achternaam}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Email</b></ion-col>
          <ion-col size="6">{{klant.email}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Telefoonnummer</b></ion-col>
          <ion-col size="6">{{klant.telefoonnummer}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Bedrijfsnaam</b></ion-col>
          <ion-col size="6">{{klant.naam}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Straat</b></ion-col>
          <ion-col size="6">{{klant.straat}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Huisnummer</b></ion-col>
          <ion-col size="6">{{klant.huisnummer}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Postcode</b></ion-col>
          <ion-col size="6">{{klant.postcode}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Datum</b></ion-col>
          <ion-col size="6">{{werkbon.datum}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Plaats</b></ion-col>
          <ion-col size="6">{{klant.plaats}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>KVK</b></ion-col>
          <ion-col size="6">{{klant.kvk}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Land</b></ion-col>
          <ion-col size="6">{{klant.land}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6"><b>Door</b></ion-col>
          <ion-col size="6">{{user.name+" "+user.lastname}}</ion-col>
        </ion-row>
      </ion-grid>
    </ion-card>

<!--  Offerte-->
    <ion-card class="ion-padding text-dark bg-white" *ngIf="werkbon && offerte">
      <div (click)="show.offerte = !show.offerte" class="flex-between" >
          <ion-label>Offerte</ion-label>
        <ion-icon name="{{show.offerte ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
      </div>
      <ion-grid [hidden]="!show.offerte" class="p-0 mx--1 my-2" >
        <ion-row>
          <ion-col size="6" ><b>BV</b></ion-col>
          <ion-col size="6" >{{bvs[offerte.template.bv].name}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6" ><b>Naam</b></ion-col>
          <ion-col size="6" >{{offerte.naam}}</ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="6" ><b>Offertenummer</b></ion-col>
          <ion-col size="6" >{{offerte.offertenummer}}</ion-col>
        </ion-row>
        <ion-row *ngFor="let keyword of customInfoKeys">
          <ion-col size="6" ><b>{{customInfo[offerte.template.id][keyword]}}</b></ion-col>
          <ion-col size="6" ><span *ngIf="offerteTeksten[keyword]" [innerHTML]="offerteTeksten[keyword].tekst" ></span></ion-col>
        </ion-row>
      </ion-grid>
    </ion-card>

<!--  Opties-->
    <div *ngIf="optiesKeys && optiesKeys.length !== 0" >
      <div  *ngFor="let key of optiesKeys;" >
        <div class="ion-text-center" style="width: 30%;margin: 0 auto;border-bottom: 1px solid grey;">
          <span>{{opties[key][0].titel}}</span>
        </div>
        <ion-card class="bg-white" >
          <div *ngFor="let item of opties[key];let l = index" class="ion-margin-vertical">
            <div class="p-2 text-dark">#{{l+1}}</div>
            <ion-list>
              <ion-item *ngFor="let row of item.value" >
                <ion-label position="floating">{{row.key}}</ion-label>
                <ion-input value="{{row.value}}" [readonly]="true"></ion-input>
              </ion-item>
            </ion-list>
          </div>
        </ion-card>
      </div>
    </div>

<!-- Keywords-->
    <div *ngIf="keywords.length" >
      <ion-card class="bg-white" >
        <div *ngFor="let item of keywords" >
          <div *ngIf="(item.input && item.input.parent_keyword) ? verifyParent(item) : true" >



            <!--Text, Number, Time, Date-->
            <ion-item *ngIf="item.type === 'text' || item.type == 'number' || item.type == 'time'" >
              <ion-label position="floating">{{item.titel}}</ion-label>
              <ion-input [readonly]="true" [value]="item.value" ></ion-input>
            </ion-item>

            <!--Date-->
            <ion-item *ngIf="item.type === 'date'" >
              <ion-label position="floating">{{item.titel}}</ion-label>
              <ion-input [readonly]="true" [value]="item.value ? convertDate(item.value).date : ''" ></ion-input>
            </ion-item>

            <!--Textarea-->
            <ion-item *ngIf="item.type === 'textarea'" >
              <div class="w-100 my-2" >
                <ion-label class="my-1" >{{item.titel}}</ion-label>
                <div class="p-1 my- rounded bg-light-grey border font-size-085" [innerHTML]="item.value" ></div>
              </div>
            </ion-item>

            <!--Select-->
            <ion-item *ngIf="item.type == 'select'">
              <ion-label position="floating" >{{item.titel}}</ion-label>
              <ion-input [readonly]="true" [value]="item.value" ></ion-input>
            </ion-item>

            <!--Select uursoort-->
            <ion-item *ngIf="item.type == 'uursoorten'">
              <ion-label position="floating" >{{item.titel}}</ion-label>
              <ion-input [readonly]="true" [value]="item.value" ></ion-input>
            </ion-item>

            <!--List select-->
            <div *ngIf="item.type == 'list_select'" class="py-2 border-bottom" >
              <ion-list-header class="font-size-1">{{item.titel}}</ion-list-header>
              <div *ngFor="let row of item.value.rows;let r = index;">
                <div class="flex-between">
                  <ion-list-header>{{row.name}}</ion-list-header>
                  <div class="flex-between">
                    <ion-item lines="none">
                      <ion-label class="ion-margin-horizontal" >{{row.value}}</ion-label>
                      <ion-icon color="primary" name="checkbox-outline"></ion-icon>
                    </ion-item>
                  </div>
                </div>
                <div class="ion-text-center" *ngIf="(r + 1) != item.value.rows.length">
                  <div class="d-inline-block w-25 h-1 bg-secondary"></div>
                </div>
              </div>
            </div>

            <!--List input-->
            <div *ngIf="item.type == 'list_input'" class="py-2 border-bottom" >
              <div *ngFor="let row of item.value.rows;let r = index;" class="bg-inverse-secondary p-1 m-1 rounded">
                <div class="flex-between" [ngClass]="item.input.data.direction === 'column' ? 'flex-column' : 'flex-row'">
                  <div class="w-100 px-2 pt-2"><b>{{row.name}}</b></div>
                  <div [ngClass]="item.input.data.direction == 'column' ? 'w-100' : ''" >
                    <ion-item *ngFor="let input of item.value.inputs" lines="none" color="none" class="font-size-09 ion-no-padding px-2" style="width: {{item.input.data.direction == 'column' ? '100%' : '125px'}}">

                      <ion-label position="floating" *ngIf="!input.data?.hide_name" >{{input.name}}:</ion-label>

                      <ion-input *ngIf="input.type == 'text' || input.type == 'number' || input.type == 'select' || input.type == 'radio'" [readonly]="true" [placeholder]="input.name" [value]="row.values[input.name] || ''" ></ion-input>
                      <div *ngIf="input.type == 'images'" class="mt-3 w-100">
                        <div *ngFor="let src of row.values[input.name] ? row.values[input.name] : [];let s = index" class="my-2" >
                          <div *ngIf="src" class="ion-text-center p-2">
                            <img style="max-height: 45vh" class="rounded shadow" src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{src}}">
                          </div>
                        </div>
                      </div>
                    </ion-item>
                  </div>
                </div>
              </div>
            </div>


            <!--Project value-->
            <div *ngIf="item.type == 'project_value' && werkbon.project" class="p-2" >
              <span class="font-size-1 text-black">{{item.data.prefix}} <span *ngFor="let value of item.data.values">{{werkbon.project[value] ? (werkbon.project[value]+' ') : ''}}</span> {{item.data.afterfix}}</span>
            </div>

            <!--Signature-->
            <div *ngIf="item.type == 'signature'">
              <div class="font-size-1 text-black p-2" >{{item.titel}}</div>
              <img src="https://{{subdomain}}.ikbentessa.nl/api/file/{{item.value}}" alt="" class="w-100" style="background-color: #00000005;">
            </div>

            <!--Image-->
            <div *ngIf="item.type == 'image'" class="p-2" >
              <div *ngIf="item.value">
                <span class="font-size-1 text-black my-1" >{{item.titel}}<span *ngIf="item.required === '1'">*</span></span>
                <div class="ion-text-center" >
                  <img style="max-height: 45vh;" class="rounded" src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{item.value}}" >
                </div>
              </div>
            </div>

            <!--Header-->
            <div *ngIf="item.type == 'header'" class="p-2 my-3" style="background-color: {{item.input.data.background || 'none'}}" >
              <span style="font-size: {{item.input.data.size || '1rem'}}; color: {{item.input.data.color || '#000'}}; font-weight: {{item.input.data.weight || 500}};">{{item.titel}}</span>
            </div>

            <!--Images-->
            <div *ngIf="item.type == 'images'" >
              <ion-label>{{item.naam}}</ion-label>
              <div class="d-flex flex-wrap ion-justify-content-evenly p-1" >
                <ion-thumbnail slot="start" *ngFor="let file of item.value; let i = index;" class="m-1" >
                  <ion-img class="rounded" src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{file}}"></ion-img>
                </ion-thumbnail>
              </div>
            </div>

          </div>
        </div>
      </ion-card>
    </div>

<!--  Klant signature-->
    <ion-card class="bg-white" *ngIf="werkbon.klant_id && werkbon.template.klant_signature === '1'" >
      <div *ngIf="werkbon.klant_signature" >
        <ion-item lines="none" *ngIf="project" >
          <ion-label>Handtekening {{project.opdrachtgever}}</ion-label>
        </ion-item>
        <ion-img style="width: 100%; background-color: #00000005;" src="{{link}}{{werkbon.klant_signature}}" ></ion-img>
      </div>
      <div *ngIf="!werkbon.klant_signature">
        <div class="alert alert-danger m-0">Niet ondertekend door opdrachtgever!</div>
      </div>
    </ion-card>

<!--  input slot-->
    <div *ngIf="base.input_slot">
      <ion-card>
        <div *ngFor="let item of keywords" >
          <ion-item *ngIf="item.type == 'input_slot'" >
            <ion-label position="floating">{{item.titel}}</ion-label>
            <ion-input value="{{item.value}}" [readonly]="true"></ion-input>
          </ion-item>
        </div>
      </ion-card>
    </div>

<!--  Planning regels-->
    <ion-card *ngIf="werkbon && werkbon.regels && werkbon.regels.length" >
      <ion-list *ngFor="let row of werkbon.regels">
        <ion-item>
          <ion-label position="floating">Naam</ion-label>
          <ion-input value="{{row.naam}}" [readonly]="true"></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Aantal</ion-label>
          <ion-input value="{{row.aantal}}" [readonly]="true"></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Prijs</ion-label>
          <ion-input value="{{row.prijs}}" [readonly]="true"></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">BTW</ion-label>
          <ion-input value="{{row.btw}}%" [readonly]="true"></ion-input>
        </ion-item>
      </ion-list>
    </ion-card>

<!--  planning definitief aantal personen-->
    <ion-card *ngIf="offerte && offerte.activiteiten.length && defPersonen" class="bg-white" >
      <div class="text-dark p-2" >Definitief aantal personen</div>
      <ion-list *ngFor="let activiteit of offerte.activiteiten">
        <ion-item>
          <ion-label>{{activiteit.naam}}</ion-label>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Aantal</ion-label>
          <ion-input *ngIf="defPersonen[activiteit.id]" [readonly]="true" [value]="defPersonen[activiteit.id].aantal"></ion-input>
          <ion-input *ngIf="!defPersonen[activiteit.id]" [readonly]="true" [value]="activiteit.aantal"></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Prijs</ion-label>
          <ion-input *ngIf="defPersonen[activiteit.id]" [readonly]="true" [value]="defPersonen[activiteit.id].prijs"></ion-input>
          <ion-input *ngIf="!defPersonen[activiteit.id]" [readonly]="true" [value]="activiteit.bedrag"></ion-input>
        </ion-item>
      </ion-list>
    </ion-card>

<!--  Handtekeningen-->
    <ion-card *ngIf="handtekeningen.length" class="bg-white" >
      <div class="text-dark p-2" >Handtekeningen</div>
      <div *ngFor="let src of handtekeningen" style="border-bottom: 1px solid grey;" >
        <ion-img style="width: 100%; background-color: #00000005;" class="ion-margin-vertical" src="{{link}}werkbonnen/handtekeningen/{{src}}" ></ion-img>
      </div>
    </ion-card>

<!--  Files-->
    <ion-card *ngIf="files.length" >
    <p class="ion-margin-horizontal" >Bestanden</p>
    <div *ngFor="let src of files" class="bg-white" >
      <ion-img style="width: 100%; max-width: 650px;" class="mt-2" src="{{link}}explorer/files/{{src}}" ></ion-img>
    </div>
  </ion-card>

<!--  Betalingsoptie-->
    <div *ngIf="werkbon && werkbon.betalingsoptie" >
    <ion-card>
      <ion-item>
        <ion-label position="floating">Betalingsoptie</ion-label>
        <ion-input value="{{werkbon.betalingsoptie}}" [readonly]="true"></ion-input>
      </ion-item>
    </ion-card>
  </div>

<!--  Opmerking-->
    <div *ngIf="werkbon && werkbon.opmerking" >
    <ion-card>
      <ion-item>
        <ion-label position="floating">Opmerking</ion-label>
        <ion-textarea value="{{werkbon.opmerking}}" [readonly]="true"></ion-textarea>
      </ion-item>
    </ion-card>
  </div>

  </ion-card>
</ion-content>
