<ion-header>
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-button (click)="backButton()">
                <ion-icon name="arrow-back-outline" class="mr-1"></ion-icon>
                <span>Terug</span>
            </ion-button>
        </ion-buttons>
        <ion-title *ngIf="!edit">Nieuwe werkbon</ion-title>
        <ion-title *ngIf="edit">Werkbon {{edit.werkbonnummer}}</ion-title>
    </ion-toolbar>
</ion-header>

<ion-content *ngIf="!post">
    <div class="ion-text-center ion-margin">
        <ion-spinner></ion-spinner>
    </div>
</ion-content>

<ion-content *ngIf="post">
    <ion-card class="m-0 bg-inverse-secondary m-h-100">

        <ion-card *ngIf="local.index !== null && local.data[local.index]" class="bg-unset shadow-unset">
            <div class="alert alert-success m-0 flex-between">
                <div>Lokale versie: <b>{{local.data[local.index].name}}</b></div>
                <div class="d-flex" > <ion-icon name="save-outline"></ion-icon> <span class="mx-2" >{{local.interval_timer}}</span> </div>
            </div>
        </ion-card>

        <div>
            <!--      Project-->
            <ion-card class="bg-white" *ngIf="template.project === '1'">
                <div class="flex-between border-bottom pl-2">
                    <ion-text class="text-input">Project:</ion-text>
                    <ion-text (click)="openProjectModal()" class="ion-text-right">
                        <ion-button fill="clear">
                            <ion-icon *ngIf="!project" name="swap-horizontal-outline"></ion-icon>
                            <span *ngIf="project">
                                <span class="d-block">{{project.projectnr}}</span>
                                <small class="d-block">{{project.projectnaam}}</small>
                            </span>
                        </ion-button>
                    </ion-text>
                </div>
            </ion-card>

            <!--      Klant-->
            <ion-card class="bg-white" *ngIf="template.project !== '1'">
                <div class="flex-between border-bottom pl-2">
                    <ion-text class="text-input">Klant:</ion-text>
                    <ion-text (click)="openKlantModal()" class="ion-text-right">
                        <ion-button fill="clear">
                            <ion-icon *ngIf="!klant" name="swap-horizontal-outline"></ion-icon>
                            <span *ngIf="klant">
                                <span class="d-block">{{klant.naam}}</span>
                                <small class="d-block">{{klant.straat}} {{klant.huisnummer}}{{klant.toevoeging}} {{klant.plaats}}</small>
                            </span>
                        </ion-button>
                    </ion-text>
                </div>
            </ion-card>

            <!--      Contactpersoon ( Select )-->
            <ion-card class="bg-white" *ngIf="klant && settings.contactpersoon_select == 'ja'" >
                <div class="flex-between border-bottom pl-2">
                    <ion-text class="text-input">Contactpersoon:</ion-text>
                    <ion-text (click)="this.modals.contactpersoon = true;" class="ion-text-right">
                        <ion-button fill="clear">
                            <ion-icon *ngIf="!contactpersoon" name="swap-horizontal-outline"></ion-icon>
                            <span *ngIf="contactpersoon">
                                <span class="d-block" >{{contactpersoon.name}}</span>
                            </span>
                        </ion-button>
                    </ion-text>
                </div>
            </ion-card>

            <!--      Vestiging-->
            <ion-card class="bg-white" *ngIf="template.vestiging === '1'" >
                <div class="flex-between border-bottom pl-2">
                    <ion-text class="text-input">Vestiging:</ion-text>
                    <ion-text (click)="openVestigingModal()" class="ion-text-right">
                        <ion-button fill="clear">
                            <ion-icon *ngIf="!vestiging" name="swap-horizontal-outline"></ion-icon>
                            <span *ngIf="vestiging">
                <span class="d-block">{{vestiging.plaats}}</span>
              </span>
                        </ion-button>
                    </ion-text>
                </div>
            </ion-card>

            <!--       Taken select-->
            <ion-card class="bg-white" *ngIf="!edit && project && project.uncompleted_taken.length">
                <div class="flex-between text p-2" (click)="show.taken = !show.taken">
                    <h6 class="m-0">Taken</h6>&nbsp;
                    <ion-icon name="{{show.taken ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
                </div>
                <div *ngIf="show.taken">
                    <ion-item *ngFor="let taak of project.uncompleted_taken" lines="none">
                        <ion-checkbox [(ngModel)]="taak.selected" (ionChange)="selectTaak()"></ion-checkbox>
                        <ion-label class="ion-margin-horizontal">{{taak.name}}</ion-label>
                    </ion-item>
                </div>
            </ion-card>

            <!--      Taken edit-->
            <ion-card class="bg-white" *ngIf="edit && edit.project_taken.length">
                <div class="flex-between text p-2" (click)="show.taken = !show.taken">
                    <h6 class="m-0">Taken</h6>&nbsp;
                    <ion-icon name="{{show.taken ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
                </div>
                <div *ngIf="show.taken">
                    <ion-item *ngFor="let taak of edit.project_taken" lines="none">
                        <ion-icon color="primary" name="checkbox-outline"></ion-icon>
                        <ion-label class="ion-margin-horizontal">{{taak.name}}</ion-label>
                    </ion-item>
                </div>
            </ion-card>

            <!--project info-->
            <ion-card class="bg-white" *ngIf="project">
              <div class="flex-between text p-2" (click)="show.project = !show.project">
                <h6 class="m-0">Project</h6>&nbsp;
                <ion-icon name="{{show.project ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
              </div>
              <ion-grid *ngIf="show.project">
                <ion-row *ngIf="project.naam">
                  <ion-col size="5"><b>Projectnaam</b></ion-col>
                  <ion-col size="7">{{project.naam}}</ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="5"><b>Projectnummer</b></ion-col>
                  <ion-col size="7">{{project.projectnr}}</ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="5"><b>Opdrachtgever</b></ion-col>
                  <ion-col size="7">{{project.opdrachtgever}}</ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="5"><b>Omschrijving</b></ion-col>
                  <ion-col size="7">{{project.omschrijving}}</ion-col>
                </ion-row>
                  <ion-row *ngFor="let custom of project.custom">
                      <ion-col size="5" *ngIf="custom.type != 'file'">
                          <b>{{ custom.name }}</b>
                      </ion-col>
                      <ion-col size="7" *ngIf="custom.type != 'file'">
                          <ng-container [ngSwitch]="true">
                              <ng-container *ngSwitchCase="custom.keyword === 'woocommerce_items'">
                                  <table>
                                      <tr *ngFor="let item of custom.list">
                                          <td><span class="bg-inverse-secondary rounded px-1 border">{{ item.quantity }}</span></td>
                                          <td>{{ item.name }}</td>
                                      </tr>
                                  </table>
                              </ng-container>
                              <ng-container *ngSwitchCase="custom.type == 'date'">
                                  {{ convertDate(custom.value) }}
                              </ng-container>
                              <ng-container *ngSwitchDefault>
                                  {{ custom.value }}
                              </ng-container>
                          </ng-container>
                      </ion-col>
                  </ion-row>

              </ion-grid>
            </ion-card>

            <!--   Project   Contactpersoon-->
            <ion-card class="bg-white" *ngIf="template.contactpersoon !== '1' && project?.contactpersoon">
                <div class="flex-between text p-2" (click)="show.contactpersoon = !show.contactpersoon">
                    <h6 class="m-0">Project Contactpersoon</h6>&nbsp;
                    <ion-icon name="{{show.contactpersoon ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
                </div>
                <ion-grid *ngIf="show.contactpersoon">
                    <ion-row>
                        <ion-col size="5"><b>Naam</b></ion-col>
                        <ion-col size="7">{{project.contactpersoon.voornaam}} {{project.contactpersoon.achternaam}}</ion-col>
                    </ion-row>
                    <ion-row *ngIf="project.contactpersoon.email" >
                        <ion-col size="5"><b>Email</b></ion-col>
                        <ion-col size="7">{{project.contactpersoon.email}}</ion-col>
                    </ion-row>
                    <ion-row *ngIf="project.contactpersoon.telefoon" >
                        <ion-col size="5"><b>Telefoon</b></ion-col>
                        <ion-col size="7">
                            <a class="text-primary" (click)="$event.stopPropagation(); call(project.contactpersoon.telefoon)" >
                                <ion-icon name="call-outline"></ion-icon>
                                <span class="ml-2" >{{project.contactpersoon.telefoon}}</span>
                            </a>
                        </ion-col>
                    </ion-row>
                    <ion-row *ngIf="project.contactpersoon.mobiel" >
                        <ion-col size="5"><b>Mobiel</b></ion-col>
                        <ion-col size="7">
                            <a class="text-primary" (click)="$event.stopPropagation(); call(project.contactpersoon.mobiel)" >
                                <ion-icon name="call-outline"></ion-icon>
                                <span class="ml-2" >{{project.contactpersoon.mobiel}}</span>
                            </a>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card>

            <!--      klant info-->
            <ion-card class="bg-white" *ngIf="reserveerplein">
                <div class="flex-between text p-2" (click)="show.reserveerplein = !show.reserveerplein">
                    <h6 style="font-size: 22px;margin: 0 10px">Reserveerplein</h6>&nbsp;
                    <ion-icon name="{{show.reserveerplein ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
                </div>
                <ion-grid *ngIf="show.reserveerplein">
                    <ion-row>
                        <ion-col size="6"><b>Naam</b></ion-col>
                        <ion-col size="6">{{reserveerplein.naam}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="6"><b>Telefoonnummer</b></ion-col>
                        <ion-col size="6">{{reserveerplein.telefoonnummer}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="6"><b>E-mail</b></ion-col>
                        <ion-col size="6">{{reserveerplein.emailadres}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="6"><b>Asset</b></ion-col>
                        <ion-col size="6">{{reserveerplein.assetnaam}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="6"><b>Betaalmethode</b></ion-col>
                        <ion-col size="6">{{reserveerplein.betaalmethode}}</ion-col>
                    </ion-row>
                    <ion-row *ngFor="let product of reserveerplein.reserveringen_producten">
                        <ion-col size="6"><b>Product</b></ion-col>
                        <ion-col size="6">{{product.productnaam}}</ion-col>
                        <ion-col size="6"><b>Aantal</b></ion-col>
                        <ion-col size="6">{{product.aantal}}</ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card>
            <ion-card class="bg-white" *ngIf="project && project.klant">
                <div class="flex-between text p-2" (click)="show.klant = !show.klant">
                    <h6 class="m-0">Klant</h6>&nbsp;
                    <ion-icon name="{{show.klant ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
                </div>
                <ion-grid *ngIf="show.klant">
                    <ion-row *ngIf="project.klant.naam">
                        <ion-col size="5"><b>Bedrijfsnaam</b></ion-col>
                        <ion-col size="7">{{project.klant.naam}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="5"><b>Voornaam</b></ion-col>
                        <ion-col size="7">{{project.klant.contactpersoon_voornaam}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="5"><b>Achternaam</b></ion-col>
                        <ion-col size="7">{{project.klant.contactpersoon_achternaam}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="5"><b>Email</b></ion-col>
                        <ion-col size="7">{{project.klant.email}}</ion-col>
                    </ion-row>
                    <ion-row *ngIf="project.klant.telefoonnummer" >
                        <ion-col size="5"><b>Telefoonnummer</b></ion-col>
                        <ion-col size="7">
                            <a class="text-primary" (click)="$event.stopPropagation(); call(project.klant.telefoonnummer)" >
                                <ion-icon name="call-outline"></ion-icon>
                                <span class="ml-2" >{{project.klant.telefoonnummer}}</span>
                            </a>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card>

            <!--      Contactpersoon ( Klant )-->
            <ion-card class="bg-white" *ngIf="project && project.klant && !project.contactpersoon">
              <div class="flex-between text p-2" (click)="show.contactpersoon = !show.contactpersoon">
                <h6 class="m-0">Contactpersoon</h6>&nbsp;
                <ion-icon name="{{show.contactpersoon ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
              </div>
              <ion-grid *ngIf="show.contactpersoon">
                <ion-row>
                  <ion-col size="5"><b>Naam</b></ion-col>
                  <ion-col size="7">{{project.klant.contactpersoon_voornaam}} {{project.klant.contactpersoon_achternaam}}</ion-col>
                </ion-row>
                <ion-row *ngIf="project.klant.contactpersoon_email" >
                  <ion-col size="5"><b>Email</b></ion-col>
                  <ion-col size="7">{{project.klant.contactpersoon_email}}</ion-col>
                </ion-row>
                <ion-row *ngIf="project.klant.contactpersoon_telefoon" >
                  <ion-col size="5"><b>Telefoon</b></ion-col>
                  <ion-col size="7">
                    <a class="text-primary" (click)="$event.stopPropagation(); call(project.klant.contactpersoon_telefoon)" >
                      <ion-icon name="call-outline"></ion-icon>
                      <span class="ml-2" >{{project.klant.contactpersoon_telefoon}}</span>
                    </a>
                  </ion-col>
                </ion-row>
                <ion-row *ngIf="project.klant.contactpersoon_mobiel" >
                  <ion-col size="5"><b>Mobiel</b></ion-col>
                  <ion-col size="7">
                    <a class="text-primary" (click)="$event.stopPropagation(); call(project.klant.contactpersoon_mobiel)" >
                      <ion-icon name="call-outline"></ion-icon>
                      <span class="ml-2" >{{project.klant.contactpersoon_mobiel}}</span>
                    </a>
                  </ion-col>
                </ion-row>
              </ion-grid>
            </ion-card>

            <!--      Offerte-->
            <ion-card class="bg-white" *ngIf="project && project.offerte">
                <div class="flex-between text p-2" (click)="show.offerte = !show.offerte">
                    <h6 class="m-0">Offerte</h6>&nbsp;
                    <ion-icon name="{{show.offerte ? 'caret-up-outline' : 'caret-down-outline'}}"></ion-icon>
                </div>
                <ion-grid *ngIf="show.offerte">
                    <ion-row>
                        <ion-col size="5"><b>BV</b></ion-col>
                        <ion-col size="7">{{project.offerte.template._bv.name}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="5"><b>Naam</b></ion-col>
                        <ion-col size="7">{{project.offerte.naam}}</ion-col>
                    </ion-row>
                    <ion-row>
                        <ion-col size="5"><b>Offertenummer</b></ion-col>
                        <ion-col size="7">{{project.offerte.offertenummer}}</ion-col>
                    </ion-row>
                    <ion-row *ngFor="let preview of project.offerte.custom_preview">
                        <ion-col size="5"><b>{{preview.name}}</b></ion-col>
                        <ion-col size="7" [innerHTML]="preview.value"></ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card>
        </div>

        <!--      Opties-->
        <div *ngFor="let group of opties;">
            <div class="ion-text-center" style="width: 30%;margin: 0 auto;border-bottom: 1px solid grey;">
                <span>{{group[0].titel}}</span>
            </div>
            <ion-grid>
                <ion-row>
                    <ion-col size="6" size-sm="6" *ngFor="let optie of group;let optieKey = index"
                             class="ion-text-center">
                        <ion-card class=" ion-text-center ion-padding-vertical bg-white"
                                  (click)="selectOptie(optie.index, optieKey)"
                                  [style]="optiesData.selected[optie.index] == optieKey ? 'box-shadow: inset 0 0 15px -7px grey' : ''">
                            {{optie.naam}}
                        </ion-card>
                    </ion-col>
                </ion-row>
            </ion-grid>
            <ion-card *ngIf="optiesData.selected[group[0].index] !== undefined" class="bg-white">
                <ion-list *ngFor="let rows of optiesData.rows[group[0].index];let i = index">
                    <div class="text-dark p-2">
                        <p>#{{i + 1}}</p>
                    </div>
                    <ion-item *ngFor="let row of rows">
                        <ion-label position="floating">{{row.name}}</ion-label>
                        <ion-input [(ngModel)]="row.value" [type]="row.type"></ion-input>
                    </ion-item>
                </ion-list>
                <div class="ion-text-right p-2">
                    <ion-button (click)="addOptieInput(group[0].index)">Regel toevoegen</ion-button>
                </div>
            </ion-card>
        </div>

        <!--      Keywords-->
        <div *ngIf="keywords.length">
            <ion-card class="bg-white">

                <div *ngFor="let item of keywords">
                    <div *ngIf="item.parent_keyword ? verifyParent(item) : true">

                        <!-- Text -->
                        <ion-item class="wrap-label" *ngIf="item.type === 'text'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                            <ion-label position="floating">{{item.naam}}<span *ngIf="item.required === '1'">*</span>
                            </ion-label>
                            <ion-input [(ngModel)]="item.value"></ion-input>
                        </ion-item>

                        <!-- Number -->
                        <ion-item class="wrap-label" *ngIf="item.type === 'number'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                            <ion-label position="floating">{{item.naam}}<span *ngIf="item.required === '1'">*</span></ion-label>
                            <ion-input [(ngModel)]="item.value" type="number"></ion-input>
                        </ion-item>

                        <!-- Textarea -->
                        <ion-item class="wrap-label" *ngIf="item.type == 'textarea'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                            <ion-label position="floating">{{item.naam}}<span *ngIf="item.required === '1'">*</span>
                            </ion-label>
                            <div class="editorParent mb-2 mt-5 w-100">
                                <textarea [froalaEditor] [(ngModel)]="item.value" [innerHtml]="item.value"></textarea>
                            </div>
                        </ion-item>

                        <!--Time-->
                        <ion-item class="wrap-label" *ngIf="item.type === 'time'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                            <ion-label>{{item.naam}}<span *ngIf="item.required === '1'">*</span></ion-label>
                            <div class="px-2 py-1" slot="end"
                                 (click)="timeSelect.select(item, 'value', item.data?.interval || 5 )">{{item.value || 'Tijd selecteren'}}</div>
                        </ion-item>

                        <!--Date-->
                        <ion-item class="wrap-label" *ngIf="item.type === 'date'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                            <ion-label>{{item.naam}}<span *ngIf="item.required === '1'">*</span></ion-label>
                            <div class="px-2 py-1" slot="end"
                                 (click)="dateSelect.select(item, 'value')">{{item.value ? convertDate(item.value).date : 'Datum selecteren'}}</div>
                        </ion-item>

                        <!--checkbox-->
                        <ion-item class="wrap-label" *ngIf="item.type == 'checkbox'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                            <ion-label>{{ item.naam }}</ion-label>
                            <ion-checkbox slot="end" [(ngModel)]="item.value"></ion-checkbox>
                        </ion-item>

                        <!--Select-->
                        <ion-item *ngIf="item.type == 'select' || item.type == 'select_finish'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}} wrap-label">
                            <ion-label position="floating">{{item.naam}}<span *ngIf="item.required === '1'">*</span>
                            </ion-label>
                            <ion-select [(ngModel)]="item.value" [value]="item.value">
                                <ion-select-option *ngFor="let option of item.data.options"
                                                   [value]="option.value">{{option.name}}</ion-select-option>
                            </ion-select>
                        </ion-item>

                        <!--Select uursoort-->
                        <ion-item *ngIf="item.type == 'uursoorten'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}} wrap-label">
                          <ion-label position="floating" >{{item.naam}}<span *ngIf="item.required === '1'">*</span></ion-label>
                          <ion-select [(ngModel)]="item.value" [value]="item.value" >
                            <ion-select-option *ngFor="let uursoort of user.uursoorten" [value]="uursoort.id" >{{uursoort.code}}. {{uursoort.name}}</ion-select-option>
                          </ion-select>
                        </ion-item>

                        <!--List select-->
                        <div *ngIf="item.type == 'list_select'" class="py-2 border-bottom">
                            <ion-list-header class="font-size-1">{{item.naam}}<span
                                    *ngIf="item.required === '1'">*</span>
                            </ion-list-header>
                            <div *ngFor="let row of item.data.rows;let r = index;">
                                <div class="flex-between">
                                    <div class="flex-between" *ngIf="row.custom">
                                        <ion-button *ngIf="row.custom" fill="clear" color="danger"
                                                    (click)="removeListSelectRow(item, r)">
                                            <ion-icon name="close-outline"></ion-icon>
                                        </ion-button>
                                        <ion-input [(ngModel)]="row.name" placeholder="Naam"
                                                   class="text-black font-size-09"></ion-input>
                                    </div>
                                    <ion-list-header *ngIf="!row.custom">{{row.name}}</ion-list-header>
                                    <ion-radio-group [(ngModel)]="row.value">
                                        <ion-item *ngFor="let option of item.data.options" lines="none">
                                            <ion-radio [value]="option.value"></ion-radio>
                                            <ion-label class="ion-margin-horizontal">{{option.name}}</ion-label>
                                        </ion-item>
                                    </ion-radio-group>
                                </div>
                                <div class="ion-text-center" *ngIf="(r + 1) != item.data.rows.length">
                                    <div class="d-inline-block w-25 h-1 bg-secondary"></div>
                                </div>
                            </div>
                            <div *ngIf="item.data.multiple" class="ion-text-center">
                                <ion-button (click)="addListSelectRow(item)" fill="clear" class="font-size-1">
                                    <ion-icon name="add-outline"></ion-icon>
                                </ion-button>
                            </div>
                        </div>

                        <!--List input-->
                        <div *ngIf="item.type == 'list_input'" class="py-2 border-bottom">
                            <ion-list-header *ngIf="item.naam" class="font-size-1">{{item.naam}} <span  *ngIf="item.required === '1'">*</span> </ion-list-header>
                            <div *ngFor="let row of item.data.rows;let r = index;"
                                 class="bg-inverse-secondary p-1 m-1 rounded">
                                <div class="flex-between" [ngClass]="item.data.direction === 'column' ? 'flex-column' : 'flex-row'">
                                    <div class="flex-between w-100" *ngIf="row.custom">
                                        <ion-button *ngIf="row.custom" fill="clear" color="danger" (click)="removeListSelectRow(item, r)">
                                            <ion-icon name="close-outline"></ion-icon>
                                        </ion-button>
                                        <ion-input [(ngModel)]="row.name" placeholder="Naam" class="text-black font-size-09"></ion-input>
                                    </div>
                                    <div class="w-100 px-2 pt-2" *ngIf="!row.custom" ><b>{{row.name}}</b></div>
                                    <div [ngClass]="item.data.direction == 'column' ? 'w-100' : ''">
                                        <ion-item *ngFor="let input of item.data.inputs" lines="none" color="none" class="font-size-09 ion-no-padding px-2" style="width: {{item.data.direction == 'column' ? '100%' : '125px'}}">
                                            <div *ngIf="input.parent_keyword  ? verifyListInputParent(input, item.data.inputs) : true">

                                                <ion-label *ngIf="!input.data?.hide_name" position="floating">{{input.name}}:</ion-label>

                                                <ion-input *ngIf="input.type == 'text' || input.type == 'number' || input.type == 'date' || input.type == 'time'"  [(ngModel)]="row.values[input.name]" [type]="input.type" [placeholder]="input.name" #ionInputRef [attr.data-meta-name]="input.name"></ion-input>
                                                <ion-select *ngIf="input.type == 'select'" [(ngModel)]="row.values[input.name]">
                                                    <ion-select-option *ngFor="let option of input.data.options" [value]="option.value">{{option.name}}</ion-select-option>
                                                </ion-select>

                                                <ion-radio-group class="d-flex" [ngClass]="input.data.direction === 'column' ? 'flex-column' : 'flex-row'" *ngIf="input.type == 'radio'" [(ngModel)]="row.values[input.name]">
                                                  <ion-item *ngFor="let option of input.data.options" class="ion-no-padding" lines="none" color="none" >
                                                    <ion-radio [value]="option.value"></ion-radio>
                                                    <ion-label class="ion-margin-horizontal">{{option.name}}</ion-label>
                                                  </ion-item>
                                                </ion-radio-group>

                                                <div *ngIf="input.type == 'images'" class="mt-3 w-100">
                                                    <div *ngFor="let src of row.values[input.name] ? row.values[input.name] : [];let s = index" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}} my-2" >
                                                        <div *ngIf="!src" class="btn-inverse-primary ion-text-center p-2 font-size-09 rounded shadow" (click)="selectListInputImage(row, input, s)">
                                                            Selecteer afbeelding
                                                        </div>
                                                        <div *ngIf="src" class="ion-text-center p-2" (click)="selectListInputImage(row, input, s)">
                                                            <img style="max-height: 45vh" class="rounded shadow" src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{src}}">
                                                        </div>
                                                        <div *ngIf="src && input?.canEdit" class="btn-inverse-primary ion-text-center p-2 rounded shadow"
                                                                (click)="editImage(s,item,row)">
                                                            <h4 class="my-1">Afbeelding bewerken</h4>
                                                        </div>
                                                    </div>
                                                    <div *ngIf="item.data?.singleImage ? !(row.values | keyvalue).length : true" class="ion-text-center" > <ion-button fill="clear" (click)="addListInputImage(row, input)" ><ion-icon name="add-outline"></ion-icon></ion-button> </div>
                                                </div>
                                                <div *ngIf="input.type === 'barcode'" class="mt-3 w-100">
                                                    <div class="btn-inverse-primary ion-text-center p-2 font-size-09 rounded shadow"
                                                         (click)="scanBarcode(row, input)">
                                                        Scan barcode
                                                    </div>

                                                    <ion-label class="mt-3">Gescande code:</ion-label>

                                                    <ion-input
                                                            name="{{input.name}}"
                                                            label-placement="floating"
                                                            [attr.data-meta-name]="input._uniqueIdMap?.[row.name]"
                                                            [(ngModel)]="row.values[input.name]"
                                                            readonly
                                                            type="text">
                                                    </ion-input>

                                                    <ng-container *ngFor="let fieldName of getBarcodeFieldNames(input)">
                                                        <ion-label class="mt-3">{{ fieldName }}:</ion-label>
                                                        <ion-input
                                                                [name]="fieldName"
                                                                label-placement="floating"
                                                                [attr.data-meta-name]="fieldName"
                                                                [(ngModel)]="row.values[fieldName]"
                                                                [readonly]="input.barcodeFields[fieldName]?.readonly"
                                                                [required]="input.barcodeFields[fieldName]?.required"
                                                                [type]="input.barcodeFields[fieldName]?.type || 'text'">
                                                        </ion-input>
                                                    </ng-container>
                                                </div>
                                            </div>
                                        </ion-item>
                                    </div>
                                </div>
                            </div>

                          <div class="ion-text-center my-3">
                            <ion-buttons class="centered-buttons">
                              <div *ngIf="item.data.multiple" class="ion-text-center">
                                <ion-button (click)="addListInputRow(item)" fill="clear" class="btn btn-inverse-primary p-0">
                                  Toevoegen <ion-icon name="add-outline"></ion-icon>
                                </ion-button>
                              </div>
                              <ion-button *ngIf="item.list_input_templates?.length" fill="clear" class="btn btn-inverse-primary p-0 m-0" (click)="openInputListTemplateModal(item)">
                                <ion-icon name="chevron-down-outline"></ion-icon>
                              </ion-button>
                            </ion-buttons>
                          </div>
                        </div>

                        <!--Signature-->
                        <div *ngIf="item.type == 'signature'">
                            <div class="flex-between px-2 py-1">
                        <span class="font-size-1 text-black">{{item.naam}}<span
                                *ngIf="item.required === '1'">*</span></span>
                                <a class="btn text-danger" (click)="clearCanvasKeyword(item)">
                                    <ion-icon name="refresh-outline"></ion-icon>
                                </a>
                            </div>
                            <canvas id="signature-item-{{item.id}}" style="background-color: #00000005;"
                                    [height]="canvas.height" [width]="canvas.width" (mousedown)="startDrawing($event)"
                                    (touchstart)="startDrawing($event)" (touchmove)="moved($event)"
                                    (mousemove)="moved($event)"
                                    (mouseup)="endDrawingKeyword(item)" (touchend)="endDrawingKeyword(item)"></canvas>
                        </div>

                        <!--Image-->
                        <div *ngIf="item.type == 'image'" class="p-2">
                            <span class="font-size-1 text-black">{{item.naam}}<span *ngIf="item.required === '1'">*</span></span>
                            <div *ngIf="!item.value">
                                <div class="btn-inverse-primary ion-text-center p-2 rounded shadow"
                                     (click)="selectImage(item)">
                                    <h4 class="my-1">Selecteer afbeelding</h4>
                                </div>
                            </div>
                            <div *ngIf="item.value">
                                <div class="ion-text-center" (click)="selectImage(item)">
                                    <img style="max-height: 45vh" class="rounded shadow" src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{item.value}}">
                                </div>
                                <div *ngIf="item.data?.canEdit" class="btn-inverse-primary ion-text-center p-2 rounded shadow"
                                        (click)="editImage(null,item)">
                                    <h4 class="my-1">Afbeelding bewerken</h4>
                                </div>
                            </div>
                        </div>

                        <!--Header-->
                        <div *ngIf="item.type == 'header'" class="p-2 my-3" style="background-color: {{item.data.background || 'none'}}" >
                            <span style="font-size: {{item.data.size || '1rem'}}; color: {{item.data.color || '#000'}}; font-weight: {{item.data.weight || 500}};">{{item.naam}}</span>
                        </div>

                        <!--Images-->
                        <div *ngIf="item.type == 'images'" >
                            <ion-item>
                                <ion-label>{{item.naam}}<span *ngIf="item.required === '1'">*</span></ion-label>
                                <ion-button fill="clear" (click)="selectImage(item)">
                                    <ion-icon name="add-outline"></ion-icon>
                                </ion-button>
                            </ion-item>
                            <ion-item *ngFor="let file of item.data.files; let i = index;" text-wrap>
                                <ion-thumbnail slot="start">
                                    <ion-img class="rounded"
                                             src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{file}}"></ion-img>
                                </ion-thumbnail>
                                <ion-button slot="end" fill="clear" (click)="deleteImage(i, item)">
                                    <ion-icon slot="icon-only" name="trash"></ion-icon>
                                </ion-button>
                                <ion-button slot="end" fill="clear" (click)="editImage(i, item)">
                                    <ion-icon slot="icon-only" name="create"></ion-icon>
                                </ion-button>
                            </ion-item>
                            <ion-item *ngIf="base.uploading_image">
                                <ion-spinner color="primary"></ion-spinner>
                                <ion-button class="opacity-50" slot="end" fill="clear">
                                    <ion-icon slot="icon-only" name="trash"></ion-icon>
                                </ion-button>
                            </ion-item>
                        </div>

                    </div>
                </div>
            </ion-card>
        </div>

        <!--    Klant signature-->
        <ion-card *ngIf="verifyKlantSignature()" class="bg-white">
            <ion-item lines="none">
                <ion-label>Handtekening {{project?.opdrachtgever}}</ion-label>
            </ion-item>
            <canvas id="klant-signature" style="background-color: #00000005;" [width]="canvas.width"
                    [height]="canvas.height" (mousedown)="startDrawing($event)" (touchstart)="startDrawing($event)"
                    (touchmove)="moved($event)" (mousemove)="moved($event)" (mouseup)="endDrawingKlant()"
                    (touchend)="endDrawingKlant()"></canvas>
            <div class="ion-text-right">
                <ion-button color="primary" fill="clear" (click)="clearCanvasKlant()">
                    <ion-icon name="refresh-outline"></ion-icon>
                </ion-button>
            </div>
        </ion-card>

        <!--      Slot-->
        <div *ngIf="base.input_slot">
            <ion-card>
                <div *ngFor="let item of keywords">
                    <ion-item *ngIf="item.type == 'input_slot'" [ngClass]="{'input-error': item.showError}" class="keyword-{{item.keyword}}">
                        <ion-label position="floating">{{item.naam}}</ion-label>
                        <ion-input [(ngModel)]="item.value"></ion-input>
                    </ion-item>
                </div>
            </ion-card>
        </div>

        <!--      planning definitief aantal personen-->
        <div *ngIf="project && project.offerte && project.offerte.planning && project.offerte.planning.length">
            <ion-card class="bg-white">
                <div class="text-dark p-2">Definitief aantal personen</div>
                <div *ngFor="let planning of project.offerte.planning">
                    <ion-item>
                        <ion-label class="text-muted">{{planning.naam}}</ion-label>
                    </ion-item>
                    <ion-item>
                        <ion-label position="floating">Aantal</ion-label>
                        <ion-input [(ngModel)]="planning.aantal"></ion-input>
                    </ion-item>
                    <ion-item>
                        <ion-label position="floating">Prijs</ion-label>
                        <ion-input [(ngModel)]="planning.bedrag"></ion-input>
                    </ion-item>
                </div>
            </ion-card>
        </div>

        <!--      planningRegels-->
        <div *ngIf="planningRegels">
            <ion-card class="ion-padding">
                <div>
                    <span>{{planningRegels.naam}}</span>
                </div>
                <div>
                    <ion-item>
                        <ion-label position="floating">Aantal personen</ion-label>
                        <ion-input [(ngModel)]="planningRegels.aantal" type="number" [value]="planningRegels.aantal"></ion-input>
                    </ion-item>
                </div>
                <div>
                    <ion-item>
                        <ion-label position="floating">Prijs</ion-label>
                        <ion-input [(ngModel)]="planningRegels.prijs" type="number" [value]="planningRegels.prijs"></ion-input>
                    </ion-item>
                </div>
                <div>
                    <ion-item>
                        <ion-label position="floating">BTW</ion-label>
                        <ion-input [(ngModel)]="planningRegels.btw" type="number" [value]="planningRegels.btw"></ion-input>
                    </ion-item>
                </div>
            </ion-card>
        </div>

        <!--      Handtekening-->
        <div *ngIf="template && template.handtekening != 0">
            <ion-card class="bg-white">
                <ion-item lines="none">
                    <ion-label>Handtekeningen</ion-label>
                    <ion-button fill="clear" (click)="addHandtekening()">
                        <ion-icon name="add-outline"></ion-icon>
                    </ion-button>
                </ion-item>
                <div class="ion-text-center" *ngFor="let l of canvas.arr; let i = index;">
                    <canvas id="canvas{{i}}" style="background-color: #00000005;" [width]="canvas.width"
                            [height]="canvas.height" (mousedown)="startDrawing($event)"
                            (touchstart)="startDrawing($event)" (touchmove)="moved($event)" (mousemove)="moved($event)"
                            (mouseup)="endDrawing(i)" (touchend)="endDrawing(i)"></canvas>
                    <div class="border-top ion-text-right">
                        <ion-button color="danger" fill="clear" (click)="removeCanvas(i)">
                            <ion-icon name="close-outline"></ion-icon>
                        </ion-button>
                        <ion-button color="primary" fill="clear" (click)="clearCanvas(i)">
                            <ion-icon name="refresh-outline"></ion-icon>
                        </ion-button>
                    </div>
                </div>
            </ion-card>
        </div>

        <!--      Files-->
        <div *ngIf="template && template.file != 0">
            <ion-card>
                <ion-item>
                    <ion-label>Bijlagen</ion-label>
                    <ion-button fill="clear" (click)="selectImage()">
                        <ion-icon name="add-outline"></ion-icon>
                    </ion-button>
                </ion-item>
                <ion-item *ngFor="let img of images; let i = index;" text-wrap>
                    <ion-thumbnail slot="start">
                        <ion-img class="rounded"
                                 src="https://{{subdomain}}.ikbentessa.nl/api/file/explorer/files/{{img}}"></ion-img>
                    </ion-thumbnail>
                    <ion-button slot="end" fill="clear" (click)="editImage(i)">
                        <ion-icon slot="icon-only" name="create"></ion-icon>
                    </ion-button>
                    <ion-button slot="end" fill="clear" (click)="deleteImage(i)">
                        <ion-icon slot="icon-only" name="trash"></ion-icon>
                    </ion-button>
                </ion-item>
                <ion-item *ngIf="base.uploading_image">
                    <ion-spinner color="primary"></ion-spinner>
                    <ion-button class="opacity-50" slot="end" fill="clear">
                        <ion-icon slot="icon-only" name="trash"></ion-icon>
                    </ion-button>
                </ion-item>
            </ion-card>
        </div>

        <!--      Betalingsoptie-->
        <ion-card *ngIf="template && template.betalingsoptie != 0">
            <ion-item>
                <ion-label>Betaalmethode</ion-label>
                <ion-select [(ngModel)]="base.betalingsoptie">
                    <ion-select-option value="Pin">Pin</ion-select-option>
                    <ion-select-option value="Contant">Contant</ion-select-option>
                    <ion-select-option value="Factuur">Factuur</ion-select-option>
                    <ion-select-option value="Online betaald">Online betaald</ion-select-option>
                </ion-select>
            </ion-item>
        </ion-card>

        <!--      Opmerking-->
        <ion-card *ngIf="template.opmerking === '1'">
            <ion-item>
                <ion-label position="floating">Opmerking</ion-label>
                <ion-textarea [(ngModel)]="base.opmerking"></ion-textarea>
            </ion-item>
        </ion-card>

        <!--      Submit-->
        <div *ngIf="loadingCustomFunction" class="ion-text-center">
            <ion-spinner></ion-spinner>
        </div>
        <div *ngIf="!loadingCustomFunction" class="ion-text-center my-3">
            <div class="ion-text-center" *ngIf="button">
                <a class="btn btn-inverse-primary mx-1" *ngIf="local.index === null" (click)="localStoreModal()">Lokaal
                    opslaan</a>
                <a class="btn btn-inverse-primary mx-1" *ngIf="local.index !== null" (click)="localStore()">Lokale
                    versie opslaan</a>
                <a class="btn btn-inverse-success" (click)="submit()">Opslaan</a>
            </div>
            <ion-spinner color="success" *ngIf="!button"></ion-spinner>
        </div>


    </ion-card>

    <!--  Select project-->
    <div class="modal-container" *ngIf="modals.project" (click)="modals.project = false;">
        <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
            <ion-item>
                <ion-label position="floating">Zoeken</ion-label>
                <ion-input [(ngModel)]="projectSearch" (keyup)="debounce(searchProject, 800, $event);
                " class="ion-text-left"></ion-input>
            </ion-item>
            <div class="ion-padding overflow-auto mh-33-vh">
                <div *ngFor="let row of projecten">
                    <div class="ion-text-center mb-1">
                        <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectProject(row.id)">
                            <div class="p-1">
                                <span class="d-block">{{row.projectnaam || ''}}, {{row.opdrachtgever || ''}}</span>
                                <small class="opacity-50">{{row.projectnr}}</small>
                            </div>
                        </ion-button>
                    </div>
                </div>
            </div>
        </ion-card>
    </div>

    <!--  Select klant-->
    <div class="modal-container" *ngIf="modals.klant" (click)="modals.klant = false;">
        <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
            <ion-item>
                <ion-label position="floating">Zoeken</ion-label>
                <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
            </ion-item>
            <div class="ion-padding overflow-auto mh-33-vh">
                <div *ngFor="let row of klanten">
                    <div class="ion-text-center mb-1" *ngIf="searchKlant(row)">
                        <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectKlant(row)">
                            <div class="p-1">
                                <span class="d-block">{{row.naam || ''}}</span>
                                <small class="opacity-50">{{row.straat}} {{row.huisnummer}}{{row.toevoeging}} {{row.plaats}}</small>
                            </div>
                        </ion-button>
                    </div>
                </div>
            </div>
        </ion-card>
    </div>

    <!--  Select Contact-->
    <div class="modal-container" *ngIf="modals.contactpersoon" (click)="modals.contactpersoon = false;">
        <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
            <ion-item>
                <ion-label position="floating">Zoeken</ion-label>
                <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
            </ion-item>
            <div class="ion-padding overflow-auto mh-33-vh">

                <div *ngIf="klant" >

                    <!--Main Contact-->
                    <div class="ion-text-center mb-1" *ngIf="(klant.contactpersoon_voornaam || klant.contactpersoon_achternaam) && searchContact(klant, true)">
                        <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectContactpersoon(null)">
                            <div class="p-1">
                                <span class="d-block">{{klant.contactpersoon_voornaam || ''}} {{klant.contactpersoon_achternaam || ''}}</span>
                            </div>
                        </ion-button>
                    </div>

                    <!--Extra Contact-->
                    <div *ngFor="let contact of klant.contactpersonen">
                        <div class="ion-text-center mb-1" *ngIf="searchContact(contact, false)">
                            <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectContactpersoon(contact.id)">
                                <div class="p-1">
                                    <span class="d-block">{{contact.voornaam || ''}} {{contact.achternaam}}</span>
                                </div>
                            </ion-button>
                        </div>
                    </div>

                </div>


            </div>
        </ion-card>
    </div>

    <!--  Select vestiging-->
    <div class="modal-container" *ngIf="modals.vestiging" (click)="modals.vestiging = false;">
        <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
            <ion-item>
                <ion-label position="floating">Zoeken</ion-label>
                <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
            </ion-item>
            <div class="ion-padding overflow-auto mh-33-vh">
                <div *ngFor="let row of user.vestigingen">
                    <div class="ion-text-center mb-1" *ngIf="searchVestiging(row)">
                        <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectVestiging(row)">
                            <div class="p-1">
                                <span class="d-block">{{row.plaats}}</span>
                                <small class="d-block opacity-50">{{row.straat}} {{row.huisnummer}}{{row.toevoeging}}</small>
                            </div>
                        </ion-button>
                    </div>
                </div>
            </div>
        </ion-card>
    </div>

    <!-- input_list_template select -->
    <div class="modal-container" *ngIf="modals.input_list_template" (click)="modals.input_list_template = false;">
      <ion-card class="p-2 text-dark" style="width: 90vw; max-width: 650px;" (click)="$event.stopPropagation()">
        <ion-item>
          <ion-label position="floating">Zoeken</ion-label>
          <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
        </ion-item>
        <div class="ion-padding overflow-auto mh-33-vh">
          <div *ngFor="let list_input_template of searchListInputTemplates(list_input.item.list_input_templates)">
            <div class="ion-text-center mb-1" *ngIf="searchListInputTemplates(list_input.item.list_input_templates)">
              <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="addListInputRow(list_input_template, list_input_template.name, undefined, true)">
                <div class="p-1">
                  <span class="d-block">{{list_input_template.name}}</span>
                </div>
              </ion-button>
            </div>
          </div>
        </div>
<!--        todo toevoegen verwijderen-->
<!--        <ion-button fill="solid" color="danger"  class="ml-1">-->
<!--        <ion-icon name="trash-outline"></ion-icon>-->
<!--        </ion-button>-->
      </ion-card>
    </div>

    <!--  Select list_input dataset-->
    <div class="modal-container" *ngIf="list_input.modal" (click)="list_input.modal = false;">
        <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
            <ion-item>
                <ion-label position="floating">Zoeken</ion-label>
                <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
            </ion-item>
            <div class="ion-padding overflow-auto mh-33-vh">

                <div class="ion-text-center py-4" *ngIf="!datasets[list_input.dataset]">
                    <ion-spinner color="primary"></ion-spinner>
                </div>
                <div *ngIf="datasets[list_input.dataset]">
                    <div class="ion-text-center mb-1">
                        <ion-button class="h-auto ion-text-wrap" fill="clear"
                                    (click)="addListInputRow(list_input.item, '')">
                            <span class="d-block p-1">Leeg</span>
                        </ion-button>
                    </div>

                    <div *ngFor="let item of datasets[list_input.dataset].items">
                        <div class="ion-text-center mb-1" *ngIf="searchListInputRow(item)">
                            <ion-button class="h-auto ion-text-wrap" fill="clear"
                                        (click)="addListInputRow(list_input.item, item.name, item.id)">
                                <span class="d-block p-1">{{item.name}}</span>
                            </ion-button>
                        </div>
                    </div>

                </div>
            </div>
        </ion-card>
    </div>

    <!--Local save-->
    <div class="modal-container" *ngIf="modals.local" (click)="modals.local = false;">
        <ion-card (click)="$event.stopPropagation()" class="w-100 bg-white">
            <div class="p-2 flex-between text-dark font-size-1">
                <ion-text>{{template.name}}</ion-text>
                <ion-text>{{today.date}}</ion-text>
            </div>
            <ion-item>
                <ion-label position="floating">Naam</ion-label>
                <ion-input [(ngModel)]="modals.name"></ion-input>
            </ion-item>
            <div class="ion-text-center my-2 mx--1">
                <a class="btn btn-inverse-danger mx-1" (click)="goBack()">Niet opslaan</a>
                <a class="btn btn-inverse-success mx-1" (click)="localStore()">Lokaal opslaan</a>
            </div>
        </ion-card>
    </div>

    <!--Time select-->
    <div class="modal-container" *ngIf="timeSelect.modal" (click)="timeSelect.modal = false;">
        <ion-card class="p-2 text-dark" style="width: 90vw; max-width: 750px;" (click)="$event.stopPropagation()">
            <div class="d-flex ion-justify-content-around font-size-125">

                <div class="flex-between w-100">
                    <div class="bg-secondary h-1 w-100 px-2"></div>
                </div>

                <!--      Hours-->
                <div (scroll)="timeSelect.scrollTime('hour')" style="max-height: 150px;" class="overflow-auto w-100"
                     id="custom-time-hour">
                    <div style="height: 39px"></div>
                    <div class="ion-text-center my-4" id="custom-time-hour-{{h}}"
                         *ngFor="let h of timeSelect.vars.hours" (click)="timeSelect.selectTimeValue('hour', h)">
                        <span class="{{timeSelect.vars.time.hour == h ? 'text-primary' : ''}}">{{('0' + h).slice(-2)}}</span>
                    </div>
                    <div style="height: 39px"></div>
                </div>

                <div class="flex-between w-50">
                    <div class="bg-secondary h-1 w-100 px-2"></div>
                </div>

                <!--      Minutes-->
                <div (scroll)="timeSelect.scrollTime('min')" style="max-height: 150px;" class="overflow-auto w-100"
                     id="custom-time-min">
                    <div style="height: 39px"></div>
                    <div class="ion-text-center my-4" id="custom-time-min-{{m}}"
                         *ngFor="let m of timeSelect.vars.minutes" (click)="timeSelect.selectTimeValue('min', m)">
                        <span class="{{timeSelect.vars.time.min == m ? 'text-primary' : ''}}">{{('0' + m).slice(-2)}}</span>
                    </div>
                    <div style="height: 39px"></div>
                </div>

                <div class="flex-between w-100">
                    <div class="bg-secondary h-1 w-100 px-2"></div>
                </div>

            </div>
            <div class="mt-1 pt-1 ion-text-right border-top">
                <ion-button fill="clear" (click)="timeSelect.confirmTime()">OK</ion-button>
            </div>
        </ion-card>
    </div>

    <!--Date select-->
    <div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;">
      <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()">

        <div class="flex-between my-1 mx--2">
          <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)">
            <ion-icon name="chevron-back-outline"></ion-icon>
          </ion-button>
          <span class="mx-2">{{dateSelect.vars.date.year}}</span>
          <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)">
            <ion-icon name="chevron-forward-outline"></ion-icon>
          </ion-button>
        </div>

        <div class="flex-between my-1 mx--2">
          <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)">
            <ion-icon name="chevron-back-outline"></ion-icon>
          </ion-button>
          <span class="mx-2">{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
          <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)">
            <ion-icon name="chevron-forward-outline"></ion-icon>
          </ion-button>
        </div>

        <div class="flex-between custom-date-picker-days">
          <div>ma</div>
          <div>di</div>
          <div>wo</div>
          <div>do</div>
          <div>vr</div>
          <div>za</div>
          <div>zo</div>
        </div>
        <div class="custom-date-picker">
          <div
            *ngFor="let day of dateSelect.vars.completeDays"
            [ngClass]="{
              'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
              'prev-month': day.type === 'prev',
              'current-month': day.type === 'current',
              'next-month': day.type === 'next'
            }"
            (click)="dateSelect.confirmDate(day)">
            {{ day.day }}
          </div>
        </div>

      </ion-card>
    </div>

</ion-content>
