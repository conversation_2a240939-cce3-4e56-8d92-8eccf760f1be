import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Platform, ModalController } from '@ionic/angular';
import { timeSelect, dateSelect, convertDate, Infordb } from 'infordb';
import { ClientStorageService } from "../../services/ClientStorage/client-storage.service";
import { CanvasPage } from '../../draw/canvas/canvas.page';
import { BarcodeScanner } from '@capacitor-community/barcode-scanner';
import { Camera } from '@capacitor/camera';
import { Geolocation } from '@capacitor/geolocation';

@Component({
  selector: 'app-new',
  templateUrl: './new.page.html',
  styleUrls: ['./new.page.scss'],
  providers: [Infordb]

})
export class NewPage implements OnInit {
  public timeSelect = timeSelect;
  public dateSelect = dateSelect;
  public convertDate = convertDate;

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public button = true;
  public post = false;
  public template;
  public today = this.convertDate(new Date());

  public customFunctionData = localStorage.getItem('customFunctionResponseData');
  public noBack = false;
  public nativeBackBtn;

  public planning;
  public project;
  public contactpersoon;
  public vestiging;
  public klant;
  public edit;

  public base = {
    betalingsoptie: null,
    opmerking: '',
    uploading_image: false,
    input_slot: false,
    timeoutId: null,
  }
  public modals = {
    search: '',
    name: '',
    project: false,
    vestiging: false,
    klant: false,
    local: false,
    contactpersoon: false,
    input_list_template: false,
  }
  public canvas = {
    drawing: false,
    width: this.plt.width(),
    height: this.plt.width() * 0.35,
    saveX: 0,
    saveY: 0,
    lineWidth: 5,
    arr: [],
  }
  public show = {
    project: false,
    klant: false,
    contactpersoon: false,
    offerte: false,
    reserveerplein: false,
    taken: true,
  }
  public optiesData = {
    selected: {},
    rows: {},
  }
  public settings = {
    pause_listener: null,
    contactpersoon_select: this.user.values['werkbon_contactpersoon_aanpassen_app']?.value || '[]',
  }
  public local = {
    interval_time: 120,
    interval_timer: '',
    save_time: new Date().getTime(),
    back: false,
    index: null,
    data: [],
  }
  public datasets = {}

  public intervals = {
    local_store: null,
    local_store_time: null,
  }

  public list_input = {
    modal: false,
    dataset: null,
    item: null,
  }

  public keywords = [];
  public klanten = [];
  public projecten: any;
  public opties = [];
  public images = [];
  public planningRegels;
  public reserveerplein;

  public projectSearch = '';

  public loadingCustomFunction = false;

  constructor(
    public router: Router,
    private plt: Platform,
    private infordb: Infordb,
    private clientStorage: ClientStorageService,
    private modalCtrl: ModalController
  ) {
    this.localSubscribe();
  }

  ngOnInit() {
    this.nativeBackBtn = this.plt.backButton.subscribeWithPriority(9999, () => {
      if (this.noBack) {
        this.infordb.notification({ message: 'Deze werkbon is verplicht om verder te kunnen' });
      }
    });
  }

  ngOnDestroy() {
    if (this.nativeBackBtn) {
      this.nativeBackBtn.unsubscribe();
    }
  }

  ionViewWillEnter() {
    this.varsInit();
    let id = localStorage.werkbonTemplateId;

    id = this.customFunctionData ? this.setCustomFunctionData() : id;

    this.intervals.local_store = setInterval(() => { this.localStore(true) }, this.local.interval_time * 1000);
    this.intervals.local_store_time = setInterval(() => { this.autoSaveTime() }, 1000);

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/create`, {
      id: id.toString(),
    })
      .then(response => {
        if (response.status != 201) { throw response; }

        const { template, keywords, opties, projecten, klanten } = response.data;
        this.template = template;
        this.projecten = projecten;
        this.klanten = klanten;
        this.keywordsInit(keywords);
        this.optiesInit(opties);
        this.planningInit();
        this.localInit();
        this.editInit();

        this.post = true;
      })
      .catch(this.infordb.handleError);
  }
  ionViewWillLeave() {
    this.noBack = false;
    this.button = true;

    clearInterval(this.intervals.local_store);
    clearInterval(this.intervals.local_store_time);
    this.localUnsubscribe();

    //deactivate barcode scanner
    this.barcodeScannerDeactivate();
  }

  triggerCustomFunction(
      value: any,
      data: { className: string; functionName: string },
      inputElement: any,
      row: any,
      input: any
  ) {
    this.loadingCustomFunction = true;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/trigger/customfunction`, {
      className: data.className,
      functionName: data.functionName,
      data: value,
    })
        .then(response => {
          console.log(`Response from custom function:`, response);
        })
        .catch(err => {
          const message = err.response?.data?.message || 'Onbekende fout';

          this.infordb.notification({
            message: message,
            duration: 4000,
          });

          if (inputElement?.getInputElement) {
            inputElement.getInputElement().then(nativeInput => {
              nativeInput.value = '';
            });
          }

          if (input?.name && row?.values) {
            row.values[input.name] = '';
          }
        })
        .finally(() => {
          this.loadingCustomFunction = false;
        });
  }
  localSubscribe() {
    this.settings.pause_listener = this.plt.pause.subscribe(() => {
      this.localUnsubscribe()
      this.localStore();
    });
  }
  localUnsubscribe() {
    this.settings.pause_listener.unsubscribe();
  }
  localStoreModal() {
    this.modals.name = 'AutoSave'
    this.modals.local = true;
  }
  localStore(stay = false) {
    const data = {
      name: this.modals.name || 'AutoSave',
      time: this.convertDate(new Date()),

      edit: this.edit,
      keywords: this.keywords,
      template: this.template,
      project: this.project,
      vestiging: this.vestiging,
      contactpersoon: this.contactpersoon,
      planning: this.planning,
      planningRegels: this.planningRegels,
      optiesData: this.optiesData,
      images: this.images,
      base: this.base,
      canvas: this.canvas,
    }

    if (this.local.index !== null && this.local.data[this.local.index]) {
      data.name = this.local.data[this.local.index].name;
      this.local.data[this.local.index] = data;
    }
    else {
      this.local.data.push(data);
      this.local.index = (this.local.data.length - 1);
    }

    try {
      this.clientStorage.set('werkbonnen_local_data', this.local.data);
    }
    catch (e) {
      this.infordb.notification({ message: 'Local storage (bijna) vol, AutoSave is niet mogelijk!' });
      return;
    }

    this.modals.local = false;
    this.local.save_time = new Date().getTime();

    this.infordb.notification({ message: 'Uw wijzigingen zijn opgeslagen. ( Lokale opslag )', position: stay ? 'top' : 'bottom' });

    clearInterval(this.intervals.local_store);
    this.intervals.local_store = setInterval(() => { this.localStore(true) }, this.local.interval_time * 1000);

    if (!stay) {
      this.localUnsubscribe();
      this.goBack();
    }
  }
  localInit() {
    this.local.index = localStorage.werkbonnen_local_selected ? Number(localStorage.werkbonnen_local_selected) : null;
    this.local.data = this.clientStorage.get('werkbonnen_local_data', [])

    localStorage.removeItem('werkbonnen_local_selected');

    if (this.local.index === null || !this.local.data[this.local.index]) { return; }

    const { edit, keywords, template, project, contactpersoon, vestiging, klant, planning, planningRegels, optiesData, inputSlot, images, base, canvas } = this.local.data[this.local.index];

    this.edit = edit;
    this.keywords = keywords;
    this.template = template;
    this.project = project;
    this.contactpersoon = contactpersoon;
    this.vestiging = vestiging;
    this.klant = klant;
    this.planning = planning;
    this.planningRegels = planningRegels;
    this.optiesData = optiesData;
    this.images = images;
    this.base = base;
    this.canvas = canvas;

    const redraw = {
      sub: this.subdomain,
      template: this.template,
      keywords: this.keywords,
      canvas: this.canvas,
      fnc: this.redrawSignatures
    }
    this.redrawSignatures(redraw)
  }
  autoSaveTime() {
    // interval_time: time in seconds between each iteration
    // interval_timer: countdown to the next localStore
    // save_time: timestamp of the last localStore

    let { interval_time, save_time } = this.local;
    if (!save_time) { return; }

    let seconds = interval_time - Number(((new Date().getTime() - save_time) / 1000).toFixed(0));

    const minutes = Math.floor(seconds / 60);
    seconds = seconds - (minutes * 60)

    this.local.interval_timer = `${('0' + minutes).slice(-2)}:${('0' + seconds).slice(-2)}`;
  }

  openProjectModal() {
    if (this.edit && this.project && this.project.id) { return; }

    this.modals.project = true
  }
  openVestigingModal() {
    if (this.edit && this.edit.vestiging_id) { return }

    this.modals.vestiging = true
  }

  openKlantModal() {
    if (this.edit && this.edit.klant) { return }

    this.modals.klant = true
  }
  openInputListTemplateModal(item) {
    this.list_input.item = item;
    this.modals.input_list_template = true
  }

  varsInit() {
    this.project = null;
    this.vestiging = null;
    this.klant = null;
    this.contactpersoon = null;
  }

  redrawSignatures(redraw) {
    const { sub, template, keywords, canvas, fnc } = redraw;

    try {
      if (template.klant_signature_value) {
        const cvs = document.getElementById(`klant-signature`) as HTMLCanvasElement;
        if (!cvs) {
          setTimeout(fnc, 10, redraw)
          return;
        }

        const ctx = cvs.getContext('2d');
        const img = new Image;

        img.onload = () => {
          ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, canvas.width, canvas.height);
        }
        img.crossOrigin = 'anonymous';
        img.src = template.klant_signature_value;
      }

      for (const i in canvas.arr) {
        const row = canvas.arr[i];
        const cvs = document.getElementById(`canvas${i}`) as HTMLCanvasElement;
        if (!cvs) {
          setTimeout(fnc, 10, redraw)
          return;
        }

        const ctx = cvs.getContext('2d');
        const img = new Image;

        img.onload = () => {
          ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, canvas.width, canvas.height);
        }
        img.crossOrigin = 'anonymous';

        if (row.url) {
          img.src = `https://${sub}.ikbentessa.nl/api/file/werkbonnen/handtekeningen/${row.url}`;
        }
        else {
          img.src = row.src;
        }
      }

      for (const item of keywords) {
        if (item.type != 'signature' || !item.value) { continue }

        const cvs = document.getElementById(`signature-item-${item.id}`) as HTMLCanvasElement;
        if (!cvs) {
          setTimeout(fnc, 10, redraw)
          return;
        }

        const ctx = cvs.getContext('2d');
        const img = new Image;

        img.onload = () => {
          ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, canvas.width, canvas.height);
        }
        img.crossOrigin = 'anonymous';
        img.src = item.value;
      }
    }
    catch (e) {
      alert(`Er is iets foutgegaan bij het ophalen van lokale data! ${e.message}`)
    }
  }
  backButton() {
    if (this.noBack){
      this.infordb.notification({ message: 'Deze werkbon is verplicht om verder te kunnen' });
      return;
    }

    if (this.local.index !== null && this.local.data[this.local.index]) {
      this.localStore();
      return;
    }

    this.modals.name = 'AutoSave';
    if (this.edit) {
      this.modals.name = this.edit.werkbonnummer;
    }

    this.modals.local = true;
  }
  goBack() {

    let previous = 'werkbonnen';
    if (this.planning) { previous = 'dagplanning'; }

    this.callPage(previous);
  }

  submit() {
    this.button = false;
    this.noBack = false;

    let inputs = [];
    let projectTaken = [];
    let offerteAantalPersonen;

    try {
      if (!this.project && !this.klant && (!this.user.values['werkbon_project_or_klant_required'] || this.user.values['werkbon_project_or_klant_required'].value != 'ja')) {
        if (!confirm('Geen project of klant geselecteerd, wilt u doorgaan?')) {
          this.button = true;
          return false;
        }
      } else if (!this.project && !this.klant && (this.user.values['werkbon_project_or_klant_required'] && this.user.values['werkbon_project_or_klant_required'].value == 'ja')) {
        alert('Kies een klant of project!');
        this.button = true;
        return false;
      }

      if (this.project && this.project.uncompleted_taken && !this.edit) {
        for (const taak of this.project.uncompleted_taken) {
          if (taak.selected) { projectTaken.push(taak.id) }
        }
      }
      if (this.project && this.project.offerte) {
        offerteAantalPersonen = this.project.offerte.planning;
      }

      for (const i in this.optiesData.rows) {
        const group = this.opties.find(row => row[0].index == i)[0];
        for (const rows of this.optiesData.rows[i]) {
          const value = {};
          for (const row of rows) {
            value[row.name] = row.value;
          }
          inputs.push({
            keyword: group.keyword,
            db: group.id,
            titel: group.titel,
            value: JSON.stringify(value),
            type: 'optie',
          });
        }
      }
      for (const item of this.keywords) {

        let required_opmerking = '';

        if (item.type == 'list_select') {
          item.value = true;
          for (const row of item.data.rows) {
            if (!row.value) {
              required_opmerking = `( ${row.name} )`
              item.value = false;
            }
          }
        }
        if (item.type == 'list_input') {
          item.value = true;
          for (const row of item.data.rows) {
            for (const input of item.data.inputs) {

              if (input.required && !row.values[input.name]) {
                required_opmerking = `( ${row.name ?? ''} - ${input.name ?? ''} )`;
                item.value = false;
              }

              if (input.barcodeFields) {
                for (const [fieldName, fieldConfig] of Object.entries(input.barcodeFields)) {
                  const config = fieldConfig as { required?: boolean };
                  if (config.required && !row.values[fieldName]) {
                    required_opmerking = `( ${row.name ?? ''} - ${fieldName} )`;
                    item.value = false;
                  }
                }
              }
            }
          }
        }

        if (item.required === '1' && !item.value) {
          if (!item.parent_keyword || this.verifyParent(item)) {
            alert(`${item.naam} is verplicht! ${required_opmerking}`);

            item.showError = true;

            this.button = true;

            setTimeout(() => {
              const el = document.querySelector('.keyword-' + item.keyword);
              if (el) {
                el.scrollIntoView({ behavior: 'smooth', block: 'center' });
              }
            }, 100);

            return false;
          }
        }else{
          item.showError = false;
        }

        let naam = item.naam;
        let value = item.value;

        if (item.type == 'list_select') { value = item.data; }
        if (item.type == 'list_input') { value = item.data; }
        if (item.type == 'images') { value = item.data.files; }

        inputs.push({
          keyword: item.keyword,
          db: item.id,
          titel: naam,
          value: value,
          type: item.type,
        })
      }

      if (this.template.handtekening == 2) {
        if (!this.canvas.arr.length) {
          alert('Handtekening is verplicht!');
          this.button = true;
          return false;
        }
      }
      if (this.template.file == 2) {
        if (!this.images[0]) {
          alert('Bijlage is verplicht!');
          this.button = true;
          return false;
        }
      }
      if (this.template.betalingsoptie == 2) {
        if (!this.base.betalingsoptie) {
          alert('Selecteer betalingsoptie');
          this.button = true
          return false;
        }
      }
    }
    catch (err) {
      this.infordb.handleError(err);
      if (confirm('Er heeft zich een fout opgetreden tijdens het compilen van uw werkbon. Wilt u uw ingevulde gegevens aan ons versturen zodat we deze kunnen herstellen?')) { this.postPlainData(); }
      this.button = true;
      return;
    }

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/store`, {
      edit: this.edit ? this.edit.id : null,
      bv: this.template.bv,
      klantId: this.klant ? this.klant.id : (this.project ? this.project.klant_id : null),
      projectId: this.project ? this.project.id : null,
      contactId: this.contactpersoon?.id || null,
      vestigingId: this.vestiging ? this.vestiging.id : null,
      planningId: this.planning ? this.planning.id : null,
      projectTaken: JSON.stringify(projectTaken),
      inputs: JSON.stringify(inputs),
      offerteAantalPersonen: JSON.stringify(offerteAantalPersonen),
      user: this.user.user_id,
      template: this.template.id,
      images: JSON.stringify(this.images),
      planningRegels: JSON.stringify(this.planningRegels),
      betalingsoptie: this.base.betalingsoptie || null,
      opmerking: this.base.opmerking || null,
      canvas: JSON.stringify(this.canvas.arr),
      klantSignature: this.template.klant_signature_value || null,
    }, {})
      .then(response => {
        if (response.status != 201) { throw response }
        this.infordb.notification({ message: 'Werkbon opgeslagen' });

        let customFunctionResponse = this.customFunction(response.data.custom_fn_response)
        if (customFunctionResponse && customFunctionResponse.redirect) {
          this.callPage(customFunctionResponse.redirect);
          return;
        }

        if (this.local.index !== null) {
          this.local.data.splice(this.local.index, 1)
          this.local.index = null;
          this.clientStorage.set('werkbonnen_local_data', this.local.data);
        }

        this.goBack();
      })
      .catch(err => {
        this.button = true;
        this.infordb.handleError(err);
      });
  }

  postPlainData() {
    const token = this.infordb.randomString(5);
    const data = JSON.stringify({
      edit: this.edit,
      keywords: this.keywords,
      template: this.template,
      project: this.project,
      vestiging: this.vestiging,
      klant: this.klant,
      planning: this.planning,
      planningRegels: this.planningRegels,
      optiesData: this.optiesData,
      images: this.images,
      base: this.base,
      canvas: this.canvas,
    });

    this.post = false;
    alert(`Uw request-token: ${token}`)

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/debug/post`, {
      reference_token: token,
      werkbon: data
    })
      .then(response => this.post = true)
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      })
  }

  addListSelectRow(item) {
    item.data.rows.push({
      name: '',
      custom: true,
    })
  }

  addListInputRow(item, name = undefined, itemid = undefined, listInputTemplate = false) {
    this.list_input.item = item;

    if (item.data.dataset && name === undefined) {
      this.list_input.modal = true;
      this.list_input.dataset = item.data.dataset;

      if (!this.datasets[item.data.dataset]) {
        this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/offertes/datasets/get`, {
          api_token: localStorage.api_token,
          id: item.data.dataset.toString(),
        })
            .then(response => {
              const { dataset } = response.data;
              if (!dataset) { throw response; }
              this.datasets[dataset.id] = dataset;
            })
            .catch(this.infordb.handleError);
      }
      return;
    }

    this.list_input.modal = false;
    this.modals.search = '';

    if (listInputTemplate) {
      const target_item = this.keywords.find(row => row.id == item.keyword_id);
      if (!target_item) { return; }

      let rows = JSON.parse(JSON.stringify(item.data.rows));
      rows = rows.map(row => {
        const newRow = { ...row, custom: true };

        for (const input of item.data.inputs) {
          if (!input._uniqueIdMap) input._uniqueIdMap = {};
          input._uniqueIdMap[newRow.name] = input.name + '_' + this.infordb.randomString(5);
        }

        return newRow;
      });

      target_item.data.rows = target_item.data.rows.concat(rows);
      this.modals.input_list_template = false;
      return;
    }

    const newRow = {
      name: name,
      id: itemid,
      values: {},
      custom: true,
    };

    // Assign unique IDs for this new row
    for (const input of item.data.inputs) {
      if (!input._uniqueIdMap) input._uniqueIdMap = {};
      input._uniqueIdMap[name] = input.name + '_' + this.infordb.randomString(5);
    }

    item.data.rows.push(newRow);
  }
  removeListSelectRow(item, r) {
    item.data.rows.splice(r, 1)
  }
  searchListInputRow(item) {
    const name = item.name.toLowerCase().replace(/ /g, '');
    const string = this.modals.search.toLowerCase().replace(/ /g, '');

    return name.includes(string);
  }

  keywordsInit(keywords) {
    for (const item of keywords) {
      if (item.type == 'input_slot') { this.base.input_slot = true; }
      if (item.data) { item.data = JSON.parse(item.data); }

      for (const list_input_template of item.list_input_templates) {
        if (list_input_template.data) {
          list_input_template.data = JSON.parse(list_input_template.data);
        }
      }
    }
    this.keywords = keywords;
  }
  planningInit() {
    if (!localStorage.planningWerkbon) { return }

    this.planning = JSON.parse(localStorage.planningWerkbon);
    localStorage.removeItem('planningWerkbon');

    this.base.opmerking = this.planning.opmerking || '';

    if (this.planning.aantal_personen) {
      const { legenda, project, aantal_personen, prijs, btw } = this.planning

      this.planningRegels = {
        naam: legenda ? legenda.naam : (project ? project.projectnaam : ''),
        aantal: aantal_personen || 0,
        prijs: prijs || 0,
        btw: btw || 0,
      };
    }

    if (this.planning.custom) {
      for (const item of this.planning.custom) {
        if (item.type == 'dataset') {
          const keyword = this.keywords.find(row => (row.data?.dataset ?? null) == item.value[0].dataset_id);
          if (!keyword) { continue; }

          keyword.data.rows = [];
          for (const row of item.value) {
            keyword.data.rows.push({
              name: row.name,
              id: row.item_id,
              values: row,
              custom: true,
            })
          }

        }
      }
    }

    let projectId = null;
    if (this.planning.project) {
      projectId = this.planning.project.id.toString();
    }
    else if (this.planning.offerte && this.planning.offerte.project) {
      projectId = this.planning.offerte.project.id.toString();
    }

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/projecten/first`, {
      id: projectId,
    })
      .then(response => {
        if (response.status != 201) { throw response; }

        let project = response.data.project;
        if (project) {
          this.selectProject(project.id);
        }
      })
      .catch(this.infordb.handleError);

    if (this.planning.werkbon_keywords) {
      const keys = this.planning.werkbon_keywords;
      if (keys[this.template.id]) {
        for (const key in keys[this.template.id]) {
          const item = this.keywords.find(row => row.keyword == key);
          if (!item) { continue; }
          item.value = keys[this.template.id][key];
        }
      }
    }

    if (this.project) {
      for (const taak of this.planning.taken) {
        const ptaak = this.project.uncompleted_taken.find(row => row.id == taak.id);
        if (!ptaak) { continue; }
        ptaak.selected = true;
      }
    }

    // Reserveerplein
    if (this.planning.reserveerplein_id && this.user.values.reserveerpleinId) {
      this.infordb.get(`https://reserveerplein.nl/api/reservering/${this.user.values.reserveerpleinId.value}/${this.planning.reserveerplein_id}`)
        .then(response => {
          if (response.status == 201) {
            const { reservering } = response.data;
            this.reserveerplein = reservering;
          }
        })
        .catch(this.infordb.handleError);
    }
  }
  optiesInit(opties) {
    let temp = [];
    for (let i in opties) {
      let group = [];
      for (let row of opties[i]) {
        row.rows = JSON.parse(row.rows || '[]');
        group.push(row);
      }
      temp.push(group);
    }
    this.opties = temp;
  }
  editInit() {
    const id = localStorage.werkbonEdit;
    localStorage.removeItem('werkbonEdit');
    if (!id) { return; }

    this.post = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/edit`, {
      id: id,
    })
      .then((response) => {
        if (response.status != 201) { throw response; }

        try {
          const { werkbon } = response.data;
          this.edit = werkbon;

          this.vestiging = this.user.vestigingen.find(row => row.id == werkbon.vestiging_id);
          this.images = JSON.parse(werkbon.files || '[]');
          this.base.opmerking = werkbon.opmerking;
          this.base.betalingsoptie = werkbon.betalingsoptie;

          this.selectProject(werkbon.project?.id ?? null);
          this.selectContactpersoon(werkbon.contactpersoon_id)

          if (werkbon.vestiging) {
            this.vestiging = werkbon.vestiging;
          }
          if (werkbon.handtekeningen) {
            const handtekeningen = JSON.parse(werkbon.handtekeningen || '[]');
            for (const row of handtekeningen) {
              this.canvas.arr.push({
                url: row,
              });
            }
            const redraw = {
              sub: this.subdomain,
              template: this.template,
              keywords: this.keywords,
              canvas: this.canvas,
              fnc: this.redrawSignatures
            }
            this.redrawSignatures(redraw)
          }
          for (const taak of werkbon.project_taken) {
            this.prefillByTaak(taak);
          }
          for (const value of werkbon.keywords) {
            const keyword = this.keywords.find(row => row.keyword == value.keyword);
            if (!keyword || !value.value) { continue; }

            keyword.value = value.value;

            if (keyword.type == 'list_input') {
              keyword.data = JSON.parse(value.value);
              for(const row of keyword.data.rows) {
                for(const key in row.values){
                  if(keyword.data.inputs.find(input => input.name == key)?.inverse ?? false){
                    row.values[key] = parseFloat(row.values[key]) * -1;
                  }
                }
              }
            }
            if (keyword.type == 'images') {
              keyword.data.files = JSON.parse(value.value);
            }
          }

          this.post = true;
        }
        catch (e) { this.infordb.handleError(e) }

      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }

  verifyParent(item) {
    for (const row of this.keywords) {
      if (row.keyword != item.parent_keyword) { continue; }
      return row.value == item.parent_value
    }
  }
  verifyListInputParent(input, inputs) {
    for (const row of inputs) {
      if (input.parent_keyword && (row.name === input.parent_keyword)) {
        if (row.value !== undefined && row.value !== null && row.value !== '') {
          return true;
        } else {
          return false;
        }
      }
    }
    return false;
  }
  verifyKlantSignature() {
    //check if serting is turned on and klant is selected
    if (this.template.klant_signature === '0') { return false; }
    if (!this.klant && (!this.project || !this.project.klant)) { return false }

    const data = JSON.parse(this.template.klant_signature_data || '[]');

    //if no parent element is set
    if (!data.parent || !data.expected_value) { return true; }

    const keyword = this.keywords.find(row => row.keyword == data.parent);
    if (!keyword) { return true; }

    return keyword.value == data.expected_value;
  }

  selectProject(projid) {
    if (!projid) {
      this.project = null;
      return;
    }
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/projecten/first`, {
      id: projid.toString(),
    })
      .then(response => {
        if (response.status != 201) { throw response; }

        let proj = response.data.project;
        this.project = proj;
        this.klant = proj.klant;
        this.modals.project = false;

        if (this.planning) {
          let selectedTaken = this.planning.taken;

          for (const taak of this.project.uncompleted_taken) {
            const isSelected = selectedTaken.some(selected => selected.id === taak.id);

            if (isSelected) {
              taak.selected = true;
              this.prefillByTaak(taak);
            }
          }
        }

        this.vestiging = this.user.vestigingen.find(row => row.id == this.project.vestiging_id)

        if (this.project && this.project.offerte) {
          this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/offerteContent`, {
            offerteId: this.project.offerte.id.toString(),
          })
            .then(response => {
              if (response.status == 201) {

                const { teksten, planning } = response.data;

                this.project.offerte.keywords = teksten;
                this.project.offerte.planning = planning
                this.offerteCustomPreviewInit();
                this.offertePlanningInit();
              }
            })
            .catch(this.infordb.handleError);
        }
      })
      .catch(this.infordb.handleError);
  }
  selectVestiging(vest) {
    this.vestiging = vest;
    this.modals.search = '';
    this.modals.vestiging = false;
  }
  selectContactpersoon(contact_id) {
    if (!contact_id && this.klant) {
      this.contactpersoon = {
        id: null,
        name: `${this.klant.contactpersoon_voornaam || ''} ${this.klant.contactpersoon_achternaam}`
      }
    }
    else if (contact_id && this.klant) {
      const contact = this.klant.contactpersonen.find(row => row.id == contact_id)
      this.contactpersoon = {
        id: contact_id,
        name: `${contact.voornaam || ''} ${contact.achternaam}`
      }
    } else {
      this.contactpersoon = {
        id: null,
        name: ''
      }
    }

    this.modals.search = '';
    this.modals.contactpersoon = false;
  }

  selectKlant(klant) {
    this.klant = klant;
    this.contactpersoon = null;

    this.modals.search = '';
    this.modals.klant = false;
  }

  selectTaak() {
    for (const taak of this.project.uncompleted_taken) {
      if (taak.selected) { this.prefillByTaak(taak); }
    }
  }
  prefillByTaak(taak) {
    for (const custom of taak.custom) {
      const keyword = this.keywords.find(row => row.keyword == custom.keyword);
      if (!keyword) { continue }

      keyword.value = custom.value;
    }
  }

  searchVestiging(vestiging) {
    return this.modals.search ? vestiging.plaats.toLowerCase().includes(this.modals.search.toLowerCase()) : true;
  }
  searchKlant(klant) {
    return this.modals.search ? klant.naam.toLowerCase().includes(this.modals.search.toLowerCase()) : true;
  }
  searchListInputTemplates(list_input_templates) {
    return (list_input_templates || []).filter(list_input_template =>
      list_input_template.name.toLowerCase().includes(this.modals.search.toLowerCase())
    );
  }

  searchContact(contact, main) {
    let string = '';
    if (main) {
      string = `${contact.contactpersoon_voornaam || ''}${contact.contactpersoon_achternaam}`
    }
    else {
      string = `${contact.voornaam || ''}${contact.achternaam}`
    }

    return this.modals.search ? string.toLowerCase().includes(this.modals.search.toLowerCase()) : true;

    return true;
  }

  offerteCustomPreviewInit() {
    if (!this.user.values['werkbon_offerte_info']) { return; }

    const json = JSON.parse(this.user.values['werkbon_offerte_info'].value || '{}');
    const { keywords } = this.project.offerte;

    if (!json[this.project.offerte.template_id]) { return; }

    this.project.offerte.custom_preview = [];
    let values = json[this.project.offerte.template_id];


    for (const k in values) {
      this.project.offerte.custom_preview.push({
        name: values[k],
        value: keywords[k] ? keywords[k].tekst : '',
      })
    }
  }
  offertePlanningInit() {
    for (const planning of this.project.offerte.planning) {
    }
  }

  selectOptie(i, x) {
    const group = this.opties.find(row => row[0].index == i);
    this.optiesData.selected[i] = x;
    this.optiesData.rows[i] = this.copy([group[x].rows]);
  }
  addOptieInput(i) {
    const copy = this.copy(this.optiesData.rows[i][0]);
    for (const row of copy) {
      row.value = ''
    }

    this.optiesData.rows[i].push(copy);
  }
  checkObject(obj) {
    let rtn = false;
    for (const i in obj) {
      if (obj[i] !== '') {
        rtn = true;
      }
    }
    return rtn;
  }

  callPage(page) {
    this.modals.local = false;
    const currentPage = this.router.url.replace('/', '').toLowerCase();
    page = page.toLowerCase();

    try {
      if (page == currentPage) {
        window.location.reload();
      }
      else{
        this.router.navigate([`/${page}`]);
      }
    } catch (e) {
      alert(e);
    }
  }
  call(number) {
    if (!confirm(`Wilt u ${number} bellen?`)) { return }

    window.location.href = `tel: ${number}`;
  }
  copy(arr) {
    return JSON.parse(JSON.stringify(arr));
  }

  addHandtekening() {
    this.canvas.arr.push({
      src: null,
    });
  }
  removeCanvas(i) {
    this.canvas.arr.splice(i, 1);
  }
  startDrawing(ev) {
    ev.preventDefault();
    this.canvas.drawing = true;
    const canvasPosition = ev.target.getBoundingClientRect();
    this.canvas.saveX = ev.touches[0].pageX - canvasPosition.x;
    this.canvas.saveY = ev.touches[0].pageY - canvasPosition.y;
  }
  endDrawing(i) {
    this.canvas.drawing = false;
    const cvs = document.getElementById('canvas' + i) as HTMLCanvasElement;
    this.canvas.arr[i].src = cvs.toDataURL();
  }
  moved(ev) {
    ev.preventDefault();
    if (!this.canvas.drawing) { return; }
    const canvasPosition = ev.target.getBoundingClientRect();
    const ctx = ev.target.getContext('2d');
    const currentX = ev.touches[0].pageX - canvasPosition.x;
    const currentY = ev.touches[0].pageY - canvasPosition.y;
    ctx.lineJoin = 'round';
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = this.canvas.lineWidth;
    ctx.beginPath();
    ctx.moveTo(this.canvas.saveX, this.canvas.saveY);
    ctx.lineTo(currentX, currentY);
    ctx.closePath();
    ctx.stroke();
    this.canvas.saveX = currentX;
    this.canvas.saveY = currentY;
  }
  clearCanvas(i) {
    const cvs = document.getElementById('canvas' + i) as HTMLCanvasElement;
    const ctx = cvs.getContext('2d');
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    this.canvas.arr[i].src = null;
  }

  endDrawingKeyword(item) {
    this.canvas.drawing = false;
    const cvs = document.getElementById('signature-item-' + item.id) as HTMLCanvasElement;
    item.value = cvs.toDataURL();
  }
  clearCanvasKeyword(item) {
    const cvs = document.getElementById('signature-item-' + item.id) as HTMLCanvasElement;
    const ctx = cvs.getContext('2d');
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    item.value = null;
  }

  endDrawingKlant() {
    this.canvas.drawing = false;
    const cvs = document.getElementById('klant-signature') as HTMLCanvasElement;
    this.template.klant_signature_value = cvs.toDataURL();
  }
  clearCanvasKlant() {
    const cvs = document.getElementById('klant-signature') as HTMLCanvasElement;
    const ctx = cvs.getContext('2d');
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    this.template.klant_signature_value = null;
  }

  // camera functions
  selectImage(item = null) {
    this.localStore(true);
    this.localUnsubscribe();

    this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/upload`)
      .finally(() => {
        this.localSubscribe()
      })
      .then(response => {
        if (!response.status) { return }

        if (!item) {
          this.images.push(response.data.url);
          return
        }

        if (item.type == 'image') { item.value = response.data.url; }
        else if (item.type == 'images') { item.data.files.push(response.data.url); }

      })
      .catch(err => {
        this.infordb.notification({ message: 'Er is iets foutgegaan tijdens uploaden!' });
      })
  }
  deleteImage(index, item = null) {
    if (!item) {
      this.images.splice(index, 1);
      return;
    }

    if (item.type == 'image') { item.value = null; }
    if (item.type == 'images') { item.data.files.splice(index, 1); }
  }

  async editImage(index, item = null,row = null) {
    const imageUrl = item?.type === 'list_input' ? row['values'].Foto[index] : item?.type === 'images' ? item.data.files[index] : item?.value ?? this.images[index];

    const modal = await this.modalCtrl.create({
      component: CanvasPage,
      componentProps: {
        uploadUrl: `https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/upload`,
        fileUrl: `https://${this.subdomain}.ikbentessa.nl/api/file/explorer/files/`,
        image: imageUrl,
        dataset: 42
      },
      cssClass: 'fullscreen-modal',
      breakpoints: [0, 1],
      initialBreakpoint: 1,
      backdropDismiss: false
    });

    await modal.present();

    const { data } = await modal.onWillDismiss();
    if (data) {
      if (item) {
        if (item.type === 'images') {
          item.data.files[index] = data.url;
        } else if (item.type == 'list_input') {
          row['values'].Foto[index] = data.url;
        } else {
          item.value = data.url;
        }
      } else {
        this.images[index] = data.url;
      }
    }
  }

  selectListInputImage(row, input, index) {
    this.localStore(true);
    this.localUnsubscribe();

    if (!row.values[input.name]) { row.values[input.name] = []; }

    this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/upload`)
      .finally(() => {
        this.localSubscribe()
      })
      .then(response => {
        if (!response.status) { return; }

        row.values[input.name][index] = response.data.url;
      })
      .catch(err => {
        this.infordb.notification({ message: 'Er is iets foutgegaan tijdens uploaden!' });
      })
  }
  addListInputImage(row, input) {
    if (!row.values[input.name]) { row.values[input.name] = []; }
    row.values[input.name].push(null);
  }

  getBarcodeFieldNames(input) {
    return input.barcodeFields ? Object.keys(input.barcodeFields) : [];
  }
  async scanBarcode(row: any, input: any) {
    const status = await Camera.checkPermissions();

    if (status.camera !== 'granted') {
      alert('Camera toegang is nodig. Ga naar Instellingen > Machtigingen om toegang toe te staan.');
      return;
    }

    document.body.classList.add('scanner-active');
    BarcodeScanner.hideBackground();

    try {
      const result = await BarcodeScanner.startScan();

      if (result.hasContent) {
        row.values[input.name] = result.content;

        setTimeout(() => {
          const inputSelector = `ion-input[data-meta-name="${input._uniqueIdMap?.[row.name]}"]`;
          const barcodeEl = document.querySelector(inputSelector) as any;

          if (barcodeEl) {
            barcodeEl.value = result.content;

            barcodeEl.dispatchEvent(new CustomEvent('ionChange', {
              detail: { value: result.content },
              bubbles: true,
            }));

            this.handleBarcodeChange(result.content, input, row, barcodeEl);
          } else {
            this.handleBarcodeChange(result.content, input, row, null);
          }
        }, 100);

        await this.setGps(row, input);
      }
    } catch (error) {
      console.error("Scan error:", error);
    } finally {
      this.barcodeScannerDeactivate()
    }
  }


  async setGps(row: any, input: any) {
    if (!input?.barcodeFields?.GPS) return;

    try {
      const gpsPermission = await Geolocation.checkPermissions();
      if (gpsPermission.location !== 'granted') {
        alert('Locatie toegang is nodig. Ga naar Instellingen > Machtigingen om toegang toe te staan.');
        return;
      }

      const position = await Geolocation.getCurrentPosition();
      const coords = `${position.coords.latitude}, ${position.coords.longitude}`;
      row.values['GPS'] = coords;

    } catch (error) {
      console.error("GPS error:", error);
    }
  }

  handleBarcodeChange(value: any, input: any, row: any, inputElement?: any) {
    if (!value) return;

    const className = input.customFunction?.className;
    const functionName = input.customFunction?.functionName;

    if (className && functionName) {
      this.triggerCustomFunction(value, { className, functionName }, inputElement, row, input);
    }

    if (input.gps) {
      this.setGps(row, input);
    }
  }

  barcodeScannerDeactivate() {
    BarcodeScanner.showBackground();
    document.querySelector('body').classList.remove('scanner-active');
    BarcodeScanner.stopScan();
  }


  debounce(func, delay, ev) {

    clearTimeout(this.base.timeoutId);
    this.base.timeoutId = setTimeout(() => func(ev, this), delay);
  }

  searchProject(ev, _this) {
    let searchvalue = ev.target.value;
    _this.infordb.post(`https://${_this.subdomain}.ikbentessa.nl/api/projecten/search`, {
      search: searchvalue,
    })
      .then(response => {
        if (response.status != 200) { throw response; }
        _this.projecten = response.data;
      })
      .catch(_this.infordb.handleError);
  }

  setCustomFunctionData() {
    let data = JSON.parse(this.customFunctionData);
    let projectId = data?.werkbon?.project_id || null;
    let id = data.targetTemplate;

    this.selectProject(projectId);

    this.noBack = true
    this.customFunctionData = null;
    localStorage.removeItem('customFunctionResponseData');
    return id;
  }
  customFunction(response) {
    if(response == null){ return {redirect: false};}
    if (typeof response === 'string') {
      response = JSON.parse(response);
    }

    const data = response?.data;
    const redirect = response?.redirect;

    if (redirect) {
      localStorage.setItem('customFunctionResponseData', JSON.stringify(data));
      return {redirect: redirect};
    }

    return {redirect: false};
  }
}
