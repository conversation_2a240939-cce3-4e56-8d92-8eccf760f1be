import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-galerij',
  templateUrl: './galerij.page.html',
  styleUrls: ['./galerij.page.scss'],
	providers: [Infordb]
})
export class GalerijPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public post = false;
  public fotos;
  public beheren;


  constructor(
      private infordb: Infordb,
      public router: Router,
  ) {

  }



  ngOnInit() {
    this.fotos = [];
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/pictures`, {
      user: this.user.user_id,
    }, {})
        .then(response => {
          this.post = true;
          if (response.status === 201) {
						const { beheren, fotos } = response.data;
            this.beheren = beheren;
            this.fotos = fotos;
          }
        })
        .catch(err => {
          this.post = true;
          this.infordb.handleError(err)
        });
  }

  slice(text, l){
    return text.slice(0, l);
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  doRefresh(event) {
    this.ngOnInit();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

}
