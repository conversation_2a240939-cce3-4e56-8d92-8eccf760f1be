<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Galerij</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button class="ion-margin" (click)="callPage('uploadimg')">
      <ion-icon name="add-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <ion-card *ngFor="let foto of fotos" class="ion-margin-vertical" style="margin-left: 0; margin-right: 0;">
    <div class="ion-padding" >

      <div style="display: inline-block; width: 50%" >
        <b style="font-size: 1.3rem">{{foto.titel}}</b>
      </div>
      <div style="display: inline-block; width: 50%" >
        <small style="display: block" class="ion-text-right">{{foto.datum}} {{slice(foto.tijd, 5)}}</small>
        <small style="display: block" class="ion-text-right">{{slice(foto.user.name, 1)}}. {{foto.user.lastname}}</small>
      </div>
    </div>
    <img style="width: 100%;" src="https://{{subdomain}}.ikbentessa.nl/api/file/galerij/{{foto.src}}" >
    <div class="ion-padding" >
      <span class="ion-margin-vertical" style="display: block">{{foto.omschrijving}}</span>
      <b *ngIf="foto.klant && foto.klant.naam" style="display: block" class="ion-text-right"> {{foto.klant.naam}}</b>
      <b *ngIf="foto.klant" style="display: block" class="ion-text-right">{{foto.klant.contactpersoon_voornaam}} {{foto.klant.contactpersoon_achternaam}}</b>
    </div>
  </ion-card>

</ion-content>
