import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-upload',
  templateUrl: './upload.page.html',
  styleUrls: ['./upload.page.scss'],
  providers: [Infordb]

})
export class UploadPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klanten = [];
  public button = true;
	
	public modals = {
		klant: false,
		search: '',
	}
	public form = {
		klant: null,
		img: null,
		titel: '',
		omschrijving: '',
	}

  constructor(
      public router: Router,
      public infordb: Infordb,
  ) {}

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/pictures/klanten`, {
      api_token: localStorage.api_token,
    }, {})
      .then(response => {
				console.log(response);
        if (response.status === 201) {
					const { klanten } = response.data;
          this.klanten = klanten;
        }
      })
      .catch(err => {
        this.infordb.handleError(err)
      });
  }

  submit(){
		const { klant, img, titel, omschrijving } = this.form;
		
		if(!titel || !img){
			this.infordb.notification({message: 'Vul alle verplichte velden in'})
		}

    this.button = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/pictures/store`, {
      user: this.user.user_id,
      klant: klant?.id || null,
      titel: titel,
      omschrijving: omschrijving,
      src: img,
    }, {})
        .then(response => {
          if (response.status === 201) {
            this.button = true;
            alert('Foto succesvol opgeslagen!');
            this.callPage('galerij');
          }
        })
        .catch(err => {
          this.button = true;
          this.infordb.handleError(err)
        });
  }

  async selectImage(){
    this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/pictures/upload`)
	    .then(response => {
				this.form.img = response.data.url
		    console.log(this.form);
	    })
  }
	
	searchKlant(klant){
		return this.modals.search ? klant.titel.toLowerCase().includes(this.modals.search.toLowerCase()) : true;
	}
	
	
	callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

}
