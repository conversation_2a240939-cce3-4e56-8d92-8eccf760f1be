<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Foto uploaden</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-card class="ion-margin-vertical" style="margin-left: 0; margin-right: 0;" >
    <div *ngIf="!form.img" >
      <div class="focus ion-margin ion-text-center" style="border-radius: 10px;padding: 3.5rem 0;border: 1px solid #dee2e6" (click)="selectImage()">
        <b style="font-size: 18px;" >Bestand selecteren</b>
      </div>
    </div>
    <div *ngIf="form.img" >
      <div class="ion-text-right" >
        <ion-icon class="ion-margin" (click)="selectImage();" name="refresh-outline"></ion-icon>
      </div>
      <img style="width: 100%"  src="https://{{subdomain}}.ikbentessa.nl/api/file/galerij/{{form.img}}" >
    </div>
    <ion-list>
      <ion-item>
        <ion-label>Klant</ion-label>
        <ion-button fill="clear" (click)="modals.klant = true" >{{form.klant?.titel || 'Klant selecteren'}}</ion-button>
      </ion-item>
      <ion-item>
        <ion-label  position="floating">Titel*</ion-label>
        <ion-input [(ngModel)]="form.titel"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label  position="floating">Omschrijving</ion-label>
        <ion-textarea [(ngModel)]="form.omschrijving" ></ion-textarea>
      </ion-item>
    </ion-list>
    <ion-button [disabled]="!button" color="success" expand="full" (click)="submit()" >Opslaan</ion-button>
  </ion-card>

  <!--  Select klant-->
  <div class="modal-container" *ngIf="modals.klant" (click)="modals.klant = false;">
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
      <ion-item>
        <ion-label position="floating">Zoeken</ion-label>
        <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
      </ion-item>
      <div class="ion-padding overflow-auto mh-33-vh">
        <div *ngFor="let row of klanten">
          <div class="ion-text-center mb-1" *ngIf="searchKlant(row)">
            <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="form.klant = row; modals.klant = false;">
              <div class="p-1">
                <span class="d-block">{{row.titel || ''}}</span>
                <small class="opacity-50">{{row.straat}} {{row.huisnummer}}{{row.toevoeging}} {{row.plaats}}</small>
              </div>
            </ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>


</ion-content>
