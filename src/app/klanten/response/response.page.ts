import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import {FormBuilder, FormGroup} from '@angular/forms';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-response',
  templateUrl: './response.page.html',
  styleUrls: ['./response.page.scss'],
  providers: [ Infordb ]
})
export class ResponsePage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public call = JSON.parse(window.localStorage.getItem('call'));
  public data = JSON.parse(window.localStorage.getItem('callData'));
  public smsTemplates = JSON.parse(window.localStorage.getItem('smsTemplates'));


  public opmerking;
  public form: FormGroup;
  public button = true;
  public smsModal = false;

  constructor(
      private formBuilder: FormBuilder,
      private router: Router,
      private infordb: Infordb,
  ) {
    this.form = this.formBuilder.group({
      opmerking: this.formBuilder.control(''),
    });
  }

  ngOnInit() {
  }

  submit(){
    localStorage.setItem('klantId', this.data.klant_id);
    this.button = false;
    if (this.form.value.opmerking === ''){
      this.callPage('klant');
      return false;
    }
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/call/opmerking`, {
      callId: this.call.id,
      opmerking: this.form.value.opmerking,
    }, {})
        .then(response => {
          this.callPage('klant');
        })
        .catch(err => {
          this.button = true;
          this.infordb.handleError(err)
        });
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  selectSms(state){
    this.smsModal = state;
  }

  sendSms(i){
		const content = encodeURIComponent(this.smsTemplates[i].content);
	  window.location.href = `sms: ${this.data.nummer}?body=${content}`;
	  
	  this.button = false;
	  this.smsModal = false;
	  this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/sms/store`, {
		  user: this.user.user_id,
		  klant: this.data.klant_id,
		  number: this.data.nummer,
		  message: this.smsTemplates[i].content,
	  }, {})
		  .then(postRes => {
			  if (postRes.status === 201) {
				  this.callPage('klant');
			  }
		  })
		  .catch(err => {
			  this.button = true;
			  this.infordb.handleError(err);
			  this.callPage('klant');
			  
		  });
  }

}
