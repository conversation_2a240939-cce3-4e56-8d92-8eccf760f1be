<ion-header *ngIf="!smsModal" >
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="data && call && !smsModal" >
  <ion-list class="ion-padding-vertical" >
    <p class="ion-margin-horizontal ion-text-wrap" >U heeft zojuist {{data.name}} gebeld, wilt u hier een opmerking aan toevoegen?</p>
    <ion-item [formGroup]="form" style="background-color: rgba(0, 0, 0, 0)">
      <ion-label position="floating">Opmerking</ion-label>
      <ion-textarea formControlName="opmerking" ></ion-textarea>
    </ion-item>
  </ion-list>

  <div class="ion-margin-vertical" >
    <ion-button expand="full" color="success" [disabled]="!button" (click)="submit()">Opmerking opslaan</ion-button>
  </div>
  <div class="ion-margin-vertical" >
    <ion-button expand="full" color="primary" [disabled]="!button" (click)="selectSms(true)">SMS versturen</ion-button>
  </div>

</ion-content>

<ion-content *ngIf="smsModal" >

  <ion-fab vertical="top" horizontal="end" slot="fixed">
    <ion-fab-button (click)="selectSms(false)" class="ion-margin" >
      <ion-icon name="close-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div *ngIf="smsTemplates.length" >
    <ion-list>
      <ion-item *ngFor="let sms of smsTemplates; let i = index" (click)="sendSms(i)" >
        <h6>{{sms.name}}</h6>
      </ion-item>
    </ion-list>
  </div>

  <div *ngIf="!smsTemplates.length" >
    <h5 class="ion-text-center">Geen templates gevonden</h5>
  </div>
</ion-content>
