import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from "infordb";

@Component({
  selector: 'app-checklists',
  templateUrl: './checklists.page.html',
  styleUrls: ['./checklists.page.scss'],
	providers: [Infordb]
})
export class ChecklistsPage implements OnInit {


  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant = JSON.parse(localStorage.getItem('klant'));
  public klantId = localStorage.getItem('klantId');
  public post = false;

  public checklists;

  public templates = [];
  public templatesModal = false;


  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
  }

  ionViewWillEnter(){
    this.post = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/checklists`, {
      klant: this.klantId,
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
					const { checklists, templates } = response.data;
          this.checklists = checklists;
          this.templates = templates;
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err)
      });
  }

  selectTemplate(id){
    localStorage.setItem('checklistTemplateId', id);
    localStorage.setItem('checklistKlantId', this.klantId);
    this.callPage('checklists/new');
  }

  showCk(id){
    localStorage.setItem('checklistId', id);
    this.callPage('checklists/show');
  }

  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + date.getFullYear();
  }




}
