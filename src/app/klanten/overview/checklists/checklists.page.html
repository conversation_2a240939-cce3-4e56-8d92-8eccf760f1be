<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>{{klant.titel}} Checklists</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <ion-fab *ngIf="user.user_permissions['Checklists aanmaken'] && !templatesModal" vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button (click)="templatesModal = true" color="primary" class="ion-margin" >
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div *ngIf="post && checklists && !checklists.length" class="ion-text-center ion-margin" >
    <h4>Geen checklists gevonden</h4>
  </div>

  <div *ngIf="post" >
    <ion-card *ngFor="let ck of checklists" (click)="showCk(ck.id)" >
      <ion-list lines="none">

        <ion-item>
          <ion-icon *ngIf="ck.active == 1" color="warning" class="ion-margin-horizontal" name="hourglass-outline"></ion-icon>
          <ion-icon *ngIf="ck.active == 0" color="success" class="ion-margin-horizontal" name="checkmark-done-outline"></ion-icon>
          <ion-label>
            <h1>{{ck.checklistnummer}}</h1>
            <p class="text-muted" *ngIf="ck.klant && ck.klant.naam" >{{ck.user.name}} {{ck.user.lastname.slice(0, 1)}}. <ion-icon name="arrow-forward-outline" class="v-align" ></ion-icon> {{ck.klant.naam}}</p>
            <p class="text-muted" *ngIf="ck.klant && !ck.klant.naam" >{{ck.user.name}} {{ck.user.lastname.slice(0, 1)}}. <ion-icon name="arrow-forward-outline" class="v-align" ></ion-icon> {{ck.klant.contactpersoon_voornaam}} {{ck.klant.contactpersoon_achternaam}}</p>
          </ion-label>
          <ion-text class="ion-text-right ion-align-self-start ion-margin-vertical text-muted" >
            {{convertDate(ck.datum)}}
          </ion-text>
        </ion-item>

      </ion-list>
    </ion-card>
  </div>

  <div class="modal-container" *ngIf="templatesModal" (click)="templatesModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" color="none" >
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div *ngFor="let template of templates" class="my-2" >
        <div class="ion-text-center my-2"  >
          <ion-button (click)="selectTemplate(template.id)" class="text" fill="clear" ><ion-text class="text">{{template.name}}</ion-text></ion-button>
        </div>
      </div>
    </ion-card>
  </div>

</ion-content>
