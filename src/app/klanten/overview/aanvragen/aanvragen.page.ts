import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {NavController} from '@ionic/angular';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-aanvragen',
  templateUrl: './aanvragen.page.html',
  styleUrls: ['./aanvragen.page.scss'],
	providers: [Infordb]
})
export class AanvragenPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant = JSON.parse(window.localStorage.getItem('klant'));
  public post = false;
  public aanvragen;
  public statussen = [];
  public modules = {};

  public statusAanvraag;
  public statusModal;

  public checklistAanvraag;
  public checklistTemplates;
  public checklistModal = false;


  constructor(
      private infordb: Infordb,
      public router: Router,
      public nav: NavController
  ) { }

  ngOnInit() {
    for(const module of this.user.modules){
      this.modules[module.module.name] = true;
    }

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/aanvragen`, {
      klant: this.klant.id,
    }, {})
      .then(response => {
        this.post = true;
        if (response.status == 201) {
					const { aanvragen, statussen, checklistTemplates } = response.data;
          this.aanvragen = aanvragen;
          this.statussen = statussen;
          this.checklistTemplates = checklistTemplates;
        }
      })
      .catch(err => {
      this.post = true;
      this.infordb.handleError(err);
    });
  }

  inzien(id){
    window.localStorage.setItem('aanvraagId', id);
    this.callPage('aanvraag');
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() +1)).slice(-2) + '-' + date.getFullYear();
  }

  status(id){
    for(const aanvraag of this.aanvragen){
      if(aanvraag.id === id){
        this.statusAanvraag = aanvraag;
      }
    }
    this.statusModal = true;
  }
  selectStatus(id, status){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/aanvragen/status/select`, {
      id: id,
      status: status,
    }, {})
      .then(response => {
        this.statusAanvraag.status_intern = status;
        this.statusModal = false;
      })
      .catch(this.infordb.handleError);
  }

  checklist(template){
    localStorage.checklistTemplateId = template;
    localStorage.checklistAanvraagId = this.checklistAanvraag;
    this.callPage('checklists/new');
    this.checklistModal = false;
  }
  selectChecklist(id){
    this.checklistAanvraag = id;
    this.checklistModal = true;
  }

  offerte(id){
    localStorage.iframeRoute = `iframe_offertes_select`;
    localStorage.iframeData = JSON.stringify({
      aanvraag: id,
    });
    this.callPage('iframe');
  }

}
