import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {NavController} from '@ionic/angular';
import { Infordb } from "infordb";

@Component({
  selector: 'app-werkbonnen',
  templateUrl: './werkbonnen.page.html',
  styleUrls: ['./werkbonnen.page.scss'],
	providers: [Infordb]
})
export class WerkbonnenPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant = JSON.parse(window.localStorage.getItem('klant'));
  public post = false;
  public werkbonnen;
  public klanten;
  public projecten;
  public users;


  constructor(
      private infordb: Infordb,
      public router: Router,
      public nav: NavController
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/werkbonnen`, {
      klant: this.klant.id,
    })
        .then(response => {
          this.post = true;
          if (response.status == 201) {
						const { werkbonnen, klanten, projecten, users } = response.data;
            this.werkbonnen = werkbonnen;
            this.klanten = klanten;
            this.projecten = projecten;
            this.users = users;
          }
        }).catch(err => {
          this.post = true;
          this.infordb.handleError(err)
    });
  }

  inzien(id){
    window.localStorage.setItem('werkbonId', id);
    this.callPage('werkbon');
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

}
