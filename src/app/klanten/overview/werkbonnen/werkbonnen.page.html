<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Werkbonnen inzien</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="werkbonnen && werkbonnen.length == 0" class="ion-text-center ion-margin" >
    <h4>Geen werkbonnen gevonden</h4>
  </div>

  <div *ngIf="werkbonnen && werkbonnen.length != 0" >
    <ion-card (click)="inzien(werkbon.id)" *ngFor="let werkbon of werkbonnen" class="ion-padding" >
      <ion-card-subtitle class="ion-text-right" >{{users[werkbon.user_id].name}}&nbsp;{{users[werkbon.user_id].lastname}}&nbsp;{{werkbon.datum}}</ion-card-subtitle>
      <ion-card-title>{{werkbon.werkbonnummer}}</ion-card-title>
      <ion-card-subtitle *ngIf="projecten[werkbon.project_id]" >{{projecten[werkbon.project_id].projectnr}}&nbsp;{{projecten[werkbon.project_id].projectnaam}}</ion-card-subtitle>
    </ion-card>
  </div>
</ion-content>
