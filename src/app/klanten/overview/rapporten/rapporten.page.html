<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Rapporten</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post && rapporten.length === 0" >
    <h5 class="ion-text-center">Geen rapporten gevonden</h5>
  </div>

  <ion-card *ngFor="let rapport of rapporten" class="ion-margin-vertical ion-padding" style="margin-left: 0; margin-right: 0;" >
    <ion-grid>
      <ion-row>
        <ion-col size="12" >
          <ion-card-title>{{rapport.naam}}</ion-card-title>
          <ion-card-subtitle>{{convertDate(rapport.datum)}}</ion-card-subtitle>
          <ion-card-subtitle *ngIf="rapport.klant">{{rapport.klant.naam ? rapport.klant.naam : rapport.klant.contactpersoon_voornaam + ' ' + rapport.klant.contactpersoon_achternaam }}</ion-card-subtitle>
        </ion-col>
        <ion-col size="6">
          <ion-button expand="full" color="primary" class="ion-text-center" (click)="setRapport(rapport.id); callPage('rapport')" >Inzien</ion-button>
        </ion-col>
        <ion-col size="6" >
          <ion-button expand="full" color="primary" class="ion-text-center" href="https://{{subdomain}}.ikbentessa.nl/api/rapporten/pdf/{{rapport.id}}" >PDF</ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-card>

</ion-content>
