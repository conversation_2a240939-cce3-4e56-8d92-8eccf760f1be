import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import { Infordb } from "infordb";


@Component({
  selector: 'app-rapporten',
  templateUrl: './rapporten.page.html',
  styleUrls: ['./rapporten.page.scss'],
	providers: [Infordb],
})
export class RapportenPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant = JSON.parse(window.localStorage.getItem('klant'));
  public post = false;
  public rapporten = [];

  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/klant`, {
      klant: this.klant.id,
    }, {})
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          this.rapporten = response.data.rapporten;
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err)
      });
  }

  doRefresh(event) {
    this.ngOnInit();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() + 1)) + '-' + date.getFullYear();
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  setRapport(id){
    localStorage.setItem('rapportId', id);
  }

}
