<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <ion-fab *ngIf="user.user_permissions['Checklists aanmaken'] && !templatesModal" vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button (click)="templatesModal = true" color="primary" class="ion-margin" >
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div *ngIf="offertes && offertes.length == 0" class="ion-text-center ion-margin" >
    <h4>Geen offertes gevonden</h4>
  </div>

    <ion-list *ngIf="offertes && offertes.length != 0">
        <ion-item *ngFor="let offerte of offertes">
            <ion-avatar slot="start" style="font-size: 20px;">
                <ion-icon *ngIf="offerte.status == 'Afgewezen'" color="danger" class="icon ion-margin-horizontal" name="close-outline"></ion-icon>
                <ion-icon *ngIf="offerte.status == 'Akkoord'" color="success" class="icon ion-margin-horizontal" name="checkmark-done-outline"></ion-icon>
                <ion-icon *ngIf="offerte.status != 'Afgewezen' && offerte.status != 'Akkoord'" color="warning" class="icon ion-margin-horizontal" name="repeat-outline"></ion-icon>
            </ion-avatar>
            <ion-label>
                <h2>{{offerte.naam}}</h2>
                <h3 class="text-muted">{{offerte.offertenummer}}</h3>
                <h3 class="text-muted">
                    <span *ngIf="offerte.klanten.naam">{{offerte.klanten.naam}}, </span>
                    {{offerte.klanten.contactpersoon_voornaam}} {{offerte.contactpersoon_achternaam}}
                </h3>
                <h3 class="text-muted">{{convertDate(offerte.offerte_datum)}}</h3>
            </ion-label>

            <div slot="end">
                <ion-button color="primary" (click)="selectOfferteTemplate(offerte.id)">Inzien</ion-button>
            </div>

        </ion-item>
    </ion-list>

    <div class="modal-container" *ngIf="modals.templates" (click)="modals.templates = false;" >
        <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
            <ion-item lines="none" >
                <ion-label>Selecteer template</ion-label>
            </ion-item>
            <div *ngIf="!filteredSubTemplates().length && !hasRoleForOfferte()" class="ion-text-center py-4" >
                Geen templates beschikbaar
            </div>
            <div class="ion-text-center my-2"  >
                <ion-button *ngIf="hasRoleForOfferte()" color="primary" href="https://{{subdomain}}.ikbentessa.nl/offertes/token/{{selectedOfferte.token}}" class="text" fill="clear" ><ion-text>{{selectedOfferte.templateNaam}}</ion-text></ion-button>
            </div>
            <div *ngFor="let subTemplate of filteredSubTemplates()" class="my-2" >
                <div class="ion-text-center my-2"  >
                    <ion-button color="primary" href="https://{{subdomain}}.ikbentessa.nl/offertes/token/{{selectedOfferte.token}}?subtemplate={{subTemplate.id}}" class="text" fill="clear" ><ion-text>{{subTemplate.naam}}</ion-text></ion-button>
                </div>
            </div>
        </ion-card>
    </div>

  <div class="modal-container" *ngIf="templatesModal" (click)="templatesModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" color="none" >
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div *ngFor="let template of templates" class="my-2" >
        <div class="ion-text-center my-2"  >
          <ion-button (click)="selectTemplate(template.id)" class="text" fill="clear" ><ion-text class="text">{{template.naam}}</ion-text></ion-button>
        </div>
      </div>
    </ion-card>
  </div>

</ion-content>
