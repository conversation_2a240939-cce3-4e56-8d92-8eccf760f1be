import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-offertes',
  templateUrl: './offertes.page.html',
  styleUrls: ['./offertes.page.scss'],
	providers: [Infordb],
})
export class OffertesPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant = JSON.parse(window.localStorage.getItem('klant'));
  public post = false;

  public offertes;
  public templates;

  public selectedOfferte

  public templatesModal = false;

  public modals = {
    templates: false,
  }

  constructor(
      private infordb: Infordb,
      private router: Router,
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/offertes`, {
      klant: this.klant.id,
    })
        .then(response => {
          this.post = true;
          if (response.status === 201) {
            const { offertes, templates } = response.data;
            this.offertes = offertes;
            this.templates = templates;
          }
        })
        .catch(err => {
          this.post = true;
          this.infordb.handleError(err);
        });
  }

  convertDate(d){
      const date = new Date(d);
      return ('0' + date.getDate()).slice(-2) + '-' + ('0' + date.getMonth()).slice(-2) + '-' + date.getFullYear();
  }
  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  selectTemplate(id){
    const link = `iframe/offertes/create/${id}/${this.klant.id}`;
    localStorage.setItem('iframeRoute', link);
    this.callPage('iframe');
  }

  hasRoleForOfferte() {
    const template = this.templates.find(template => template.id == this.selectedOfferte.template_id);
    return template.roles.some(role => role.id == this.user.role.id);
  }

  filteredSubTemplates() {
    const template_id = this.selectedOfferte.template_id;
    const template = this.templates.find(template => template.id == template_id);

    return template.subtemplate.filter(subTemplate =>
        subTemplate.roles.some(role => role.id == this.user.role.id)
    );
  }

  selectOfferteTemplate(id){
    this.modals.templates = true;

    this.selectedOfferte = this.offertes.find(offerte => offerte.id == id);
    const template = this.templates.find(template => template.id == this.selectedOfferte.template_id);
    this.selectedOfferte.templateNaam = template.naam;
  }
}
