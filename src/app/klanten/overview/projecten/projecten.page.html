<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="projecten && projecten.length == 0" class="ion-text-center ion-margin" >
    <h4>Geen projecten gevonden</h4>
  </div>

  <ion-list *ngIf="projecten && projecten.length != 0" >
    <ion-item *ngFor="let project of projecten" >
      <ion-avatar slot="start" style="font-size: 20px;">
        <ion-icon *ngIf="project.active == 1" color="warning" class="icon ion-margin-horizontal" name="repeat-outline"></ion-icon>
        <ion-icon *ngIf="project.active != 1" color="success" class="icon ion-margin-horizontal" name="checkmark-done-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>{{project.projectnaam}}</h2>
        <h3 class="text-muted" >{{project.projectnr}}</h3>
        <h3 class="text-muted" >{{project.opdrachtgever}}</h3>
      </ion-label>
      <div slot="end" class="text-muted">
        <small>Gewerkte uren</small>
      </div>
      <div slot="end">
        <h4>{{project.gewerkteUren}}</h4>
      </div>
    </ion-item>
  </ion-list>

</ion-content>
