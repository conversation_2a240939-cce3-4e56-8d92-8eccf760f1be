import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-projecten',
  templateUrl: './projecten.page.html',
  styleUrls: ['./projecten.page.scss'],
	providers: [Infordb]
})
export class ProjectenPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant = JSON.parse(window.localStorage.getItem('klant'));
  public post = false;
  public projecten;

  constructor(
      private infordb: Infordb,
      private router: Router,
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/projecten`, {
      klant: this.klant.id,
    }, {})
        .then(response => {
          this.post = true;
          if (response.status === 201) {
            this.projecten = response.data.projecten;
          }
        })
        .catch(err => {
          this.post = true;
          this.infordb.handleError(err)
        });
  }

}
