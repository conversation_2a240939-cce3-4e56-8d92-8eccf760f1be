import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';
import axios from "axios";

@Component({
  selector: 'app-klanten',
  templateUrl: './klanten.page.html',
  styleUrls: ['./klanten.page.scss'],
  providers: [Infordb]
})
export class KlantenPage{

  public post = false;
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klanten = [];
  public searchValue = '';
  public searched = false;

	public request = {
		page: 1,
		loaded: null,
		total: null,
		search_ids: [],
		search_value: '',
	}
	public cancel_tokens = {
		get: null,
		search: null,
	}

  constructor(
			private infordb: Infordb,
      private router: Router,
  ) { }

  ionViewWillEnter() {
	  this.klanten = [];
	  this.initKlanten();
  }

	async initKlanten(options: any = {}){
		try{
			this.post = false;

			//Set post data
			const data: any = {
				user: this.user.user_id.toString(),
				paginate: 25,
				page: this.request.page,
        status: 1,
			};
			if(this.request.search_ids.length) {
				data.ids = this.request.search_ids;
			}

			//Generate cancel token
			if(this.cancel_tokens.get){
				this.cancel_tokens.get.cancel();
			}
			this.cancel_tokens.get = axios.CancelToken.source();

			//Request
			const response = await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/klanten/get`, data, { cancelToken: this.cancel_tokens.get.token});
			this.cancel_tokens.get = null;

			if(response.status !== 201){ throw response; }

			const { all_ids, klanten } = response.data;

			this.klanten = this.klanten.concat(klanten);
			this.post = true;
			this.request.loaded = this.klanten.length;
			this.request.total = all_ids.length;

			if(options?.event){ options.event.target.complete(); }
		}
		catch (e) {
			this.post = false;
			this.infordb.handleError(e);

			if(options?.event){ options.event.target.complete(); }
		}
	}

	loadKlanten(event){
		this.request.page++;
		this.initKlanten({event: event});
	}
	async searchKlanten(){
		try{
			this.post = false;
			this.klanten = [];
			this.request.search_ids = [];

			//Cancel ongoing requests
			if(this.cancel_tokens.get){
				this.cancel_tokens.get.cancel();
			}
			if(this.cancel_tokens.search){
				this.cancel_tokens.search.cancel();
			}

			if(this.request.search_value){

				//Generate cancel token
				this.cancel_tokens.search = axios.CancelToken.source();

				const { data } = await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/klanten/search`, {search: this.request.search_value}, { cancelToken: this.cancel_tokens.search.token });
				this.cancel_tokens.search = null;
				this.request.search_ids = data.map(klant => klant.id);
			}

			this.request.page = 1;
			this.initKlanten();
		}
		catch (e) {
			this.infordb.handleError(e);
		}
	}


  redirectKlant(id){
    localStorage.setItem('klantId', id);
    try {
      this.router.navigate([`/klant`]);
    } catch (e) {
      alert(e);
    }
  }
  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }


}
