<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>

    <ion-title>Klanten</ion-title>
    <span class="pr-4" slot="end" *ngIf="request.loaded && request.total" >{{request.loaded}} / {{request.total}}</span>

  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-fab  vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button *ngIf="user.user_permissions['Klanten beheren']" (click)="callPage('klant/edit')" class="ion-margin" >
      <ion-icon name="add-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>


  <ion-card>
    <ion-item>
      <ion-label position="floating">Zoeken</ion-label>
      <ion-input [(ngModel)]="request.search_value" (ionInput)="searchKlanten()" ></ion-input>
    </ion-item>
  </ion-card>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>
  <div *ngIf="post && !klanten.length" >
    <h5 class="ion-text-center">Geen klanten gevonden</h5>
  </div>



  <div *ngIf="klanten.length" >
    <div *ngFor="let klant of klanten" (click)="redirectKlant(klant.id)" >
      <ion-card class="p-2" >
        <div class="font-size-1125 text-black" >
          <div *ngIf="klant.naam" >{{klant.naam}}</div>
          <div *ngIf="!klant.naam" >{{klant.contactpersoon_voornaam}} {{klant.contactpersoon_achternaam}}</div>
        </div>
        <div>{{klant.debiteurnummer}}</div>

        <div *ngIf="klant.email" class="mt-2" >
          <div class="font-size-07" >Email</div>
          <div class="text-black font-size-09" >{{klant.email}},</div>
        </div>

        <div *ngIf="klant.telefoonnummer" class="mt-2" >
          <div class="font-size-07" >Telefoon</div>
          <div class="text-black  font-size-09" >{{klant.telefoonnummer}},</div>
        </div>

        <div *ngIf="klant.plaats || klant.straat || klant.postcode" class="mt-2" >
          <div class="font-size-07" >Bezoekadres</div>
          <div class="text-black font-size-09" >
            <div>{{klant.straat}} {{klant.huisnummer}}{{klant.toevoeging}},</div>
            <div>{{klant.postcode}} {{klant.plaats}}</div>
          </div>
        </div>

      </ion-card>
    </div>
  </div>

  <ion-infinite-scroll (ionInfinite)="loadKlanten($event)">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>



</ion-content>
