import {Component, OnInit, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {IonContent} from '@ionic/angular';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-klant',
  templateUrl: './klant.page.html',
  styleUrls: ['./klant.page.scss'],
  providers: [Infordb]
})

export class KlantPage implements OnInit {

  @ViewChild(IonContent) content: IonContent;


  public post = false;
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public klant;
  public smsTemplates;
  public emailTemplates;
  public modules = {};
  public smsModal = false;
  public submitBtn = true;
  public opmerkingModal = false;
  public opmerkingValue;
  public emailModal = false;
  public selectedEmail;

  public storeCallVars = {
    name: null,
    nummer: null,
    klant_id: null,
  };


  constructor(
      private infordb: Infordb,
      private router: Router,
  ) { }

  ngOnInit(){
    for(const m of this.user.modules){
      this.modules[m.module.name] = true;
    }
  }

  ionViewWillEnter(){
    const klantId = localStorage.getItem('klantId');

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/klant`, {
      klant: klantId,
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
					const { klant, smsTemplates, emailTemplates } = response.data;

          this.klant = klant;
          this.smsTemplates = smsTemplates;
          this.emailTemplates = emailTemplates;

          localStorage.setItem('klant', JSON.stringify(this.klant));
          localStorage.setItem('smsTemplates', JSON.stringify(this.smsTemplates));
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }

  maps(i?){
    let locatie;
    if(i === undefined){
      locatie = this.klant;
    }
    else{
      locatie = this.klant.locaties[i];
    }

    let adres = '';
    if(locatie.straat){
      adres += locatie.straat + ' ';
    }
    if(locatie.huisnummer){
      if(locatie.toevoeging){
        adres += locatie.huisnummer + locatie.toevoeging + ' ';
      }
      else{
        adres += locatie.huisnummer + ' ';
      }
    }
    if(locatie.postcode){
      adres += locatie.postcode+ ' ';
    }
    if(locatie.plaats){
      adres += locatie.plaats;
    }
	  
	  window.location.href = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(adres)}`;
  }

  edit(){
    localStorage.klantEdit = JSON.stringify(this.klant);
    this.callPage('klant/edit');
  }

  call(){
    this.storeCallVars.klant_id = this.klant.id;
    this.storeCallVars.name = this.klant.contactpersoon_voornaam+' '+this.klant.contactpersoon_achternaam;
    this.storeCallVars.nummer = this.klant.telefoonnummer;

    if(confirm('Wilt u '+this.klant.contactpersoon_voornaam+' '+this.klant.contactpersoon_achternaam+' bellen?')){
			window.location.href = `tel: ${this.klant.telefoonnummer}`;
      this.storeCall('FAIL');
		}
  }
  callCp(index, nummer){
    const cp = this.klant.contactpersonen[index];

    this.storeCallVars.nummer = nummer;
    this.storeCallVars.name = cp.voornaam+' '+cp.achternaam;
    this.storeCallVars.klant_id = this.klant.id;

    if(confirm('Wilt u '+cp.voornaam+' '+cp.achternaam+' bellen?')){
	    window.location.href = `tel: ${nummer}`;
			this.storeCall('FAIL');
    }
  }
  storeCall(res){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/call/store`, {
      user: this.user.user_id,
      klantId: this.klant.id,
      number: this.klant.telefoonnummer,
      response: res,
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
					const { call } = response.data;

          localStorage.setItem('call', JSON.stringify(call));
          localStorage.setItem('callData', JSON.stringify(this.storeCallVars));
					
          this.callPage('call/response');
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }

  selectSms(state){
    this.smsModal = state;
  }

  sendSms(i){
		const content = encodeURIComponent(this.smsTemplates[i].content);
		window.location.href = `sms: ${this.klant.telefoonnummer}?body=${content}`;
	  
	  this.smsModal = false;
	  this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/sms/store`, {
		  user: this.user.user_id,
		  klant: this.klant.id,
		  number: this.klant.telefoonnummer,
		  message: this.smsTemplates[i].content,
	  })
		  .then(response => {
			  this.post = true;
			  if (response.status === 201) {
					const { klant } = response.data;
				  this.klant = klant;
				  localStorage.setItem('klant', JSON.stringify(this.klant));
			  }
		  })
		  .catch(err => {
			  this.post = true;
			  this.infordb.handleError(err);
		  });
  }

  callPage(page) {
      try {
          this.router.navigate([`/${page.toLowerCase()}`]);
      } catch (e) {
          alert(e);
      }
}

  convertDate(date){
    const d = new Date(date);
    const datum = ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + d.getFullYear();
    const tijd = d.getHours() + ':' + ('0' + d.getMinutes()).slice(-2);
    return {date: datum, time: tijd};
  }

  scroll(id){
    const y = document.getElementById(id).offsetTop;
    this.content.scrollByPoint(0, y, 500);
  }

  confirmOpmerkingDelete(i){
    if(confirm('Weet u deze opmerking verwijderen')){
      this.klant.opmerkingen.splice(4, 1);
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/opmerking/delete`, {
        opmerking: this.klant.opmerkingen[i].id,
        klant: this.klant.id,
      }, {})
        .then(response => {
	        this.infordb.notification({message: 'Opmerking verwijderd!'});
          this.ionViewWillEnter();
        })
        .catch(err => {
          this.post = true;
          this.infordb.handleError(err)
        });
    }
  }
  submitOpmerking(){
    if(!this.opmerkingValue){
      alert('Opmerking kan niet leeg zijn!');
      return false;
    }
    this.submitBtn = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/opmerking/store`, {
      user: this.user.user_id,
      klant: this.klant.id,
      opmerking: this.opmerkingValue,
    }, {})
      .then(response => {
        this.submitBtn = true;
        this.opmerkingModal = false;
        this.opmerkingValue = '';
        this.infordb.notification({message: 'Opmerking opgeslagen!'});
	      this.ionViewWillEnter();
      })
      .catch(err => {
        this.submitBtn = true;
        this.infordb.handleError(err);
      });
  }

  email(state){
    this.selectedEmail = null;
    this.emailModal = state;
  }
  selectEmail(i){
    this.selectedEmail = { ...this.emailTemplates[i] };
    this.selectedEmail.ontvanger = this.klant.email;
  }
  submitEmail(){
    if(!this.selectedEmail || !this.selectedEmail.afzender || !this.selectedEmail.ontvanger || !this.selectedEmail.onderwerp || !this.selectedEmail.content){
      alert('Alle velden zijn verplicht');
      return false;
    }
    this.submitBtn = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/email/store`, {
      email: JSON.stringify(this.selectedEmail),
      klant: this.klant.id,
      user: this.user.user_id,
    }, {})
      .then(response => {
        this.submitBtn = true;
        this.selectedEmail = null;
        this.emailModal = false;
        this.infordb.notification({message: 'Email verzonden!'});
        if (response.status === 201) {
					const { klant } = response.data;
          this.klant = klant;
          localStorage.setItem('klant', JSON.stringify(this.klant));
        }
      })
      .catch(err => {
        this.submitBtn = true;
        this.infordb.handleError(err);
      });
  }

}
