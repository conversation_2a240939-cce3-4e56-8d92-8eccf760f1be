<ion-header *ngIf="!smsModal && !opmerkingModal && !emailModal" >
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="klant && klant.naam">{{klant.naam}}</ion-title>
    <ion-title *ngIf="klant && !klant.naam">{{klant.contactpersoon_voornaam}} {{klant.contactpersoon_achternaam}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="!smsModal && !opmerkingModal && !emailModal" >
  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post && klant" class="p-1" >

    <ion-card class="mx-0 bg-unset">
      <div class="ion-text-center my-2" >
        <div class="muted-border" style="display: inline-block; border-radius: 100%; overflow: hidden">
          <ion-icon style="font-size: 100px;padding: 20px;" name="person"></ion-icon>
        </div>
      </div>
      <div class="my-2 text-dark">
        <h4 class="ion-text-center m-0" *ngIf="klant.naam" >{{klant.naam}}</h4>
        <h4 class="ion-text-center m-0" >{{klant.contactpersoon_voornaam}} {{klant.contactpersoon_achternaam}}</h4>
      </div>
      <div class="m-2 text-dark">
        <div class="d-flex ion-justify-content-between m-1" *ngIf="klant.email" >
          <div>Email:</div>
          <div>{{klant.email}}</div>
        </div>
        <div class="d-flex ion-justify-content-between m-1" *ngIf="klant.email" >
          <div>Telefoonnummer:</div>
          <div>{{klant.telefoonnummer}}</div>
        </div>
        <div class="d-flex ion-justify-content-between m-1" *ngIf="klant.straat" >
          <div>Straat</div>
          <div>{{klant.straat}}</div>
        </div>
        <div class="d-flex ion-justify-content-between m-1" *ngIf="klant.huisnummer" >
          <div>Huisnummer</div>
          <div>{{klant.huisnummer}}{{klant.toevoeging}}</div>
        </div>
        <div class="d-flex ion-justify-content-between m-1" *ngIf="klant.postcode" >
          <div>Postcode</div>
          <div>{{klant.postcode}}</div>
        </div>
        <div class="d-flex ion-justify-content-between m-1" *ngIf="klant.plaats" >
          <div>Woonplaats</div>
          <div>{{klant.plaats}}</div>
        </div>
        <div class="d-flex ion-justify-content-between m-1">
          <div>BV:</div>
          <div>{{klant._bv.name}}</div>
        </div>
      </div>
      <div class="m-2 text-dark" *ngIf="klant.contactpersonen.length" >
        <div class="m-1 ion-text-center">
          <h4 class="m-0">Contactpersonen</h4>
          <div *ngFor="let cp of klant.contactpersonen;let index = index;" class="ion-text-left border-bottom py-1 my-1" >
            <b>{{cp.voornaam}} {{cp.achternaam}}</b>
            <div class="d-flex ion-justify-content-between ion-align-items-center" *ngIf="cp.email" >
                <div>{{cp.email}}</div>
            </div>
            <div class="d-flex ion-justify-content-between ion-align-items-center" *ngIf="cp.telefoon" >
              <div>
                <div>{{cp.telefoon}}</div>
              </div>
              <ion-button  fill="clear" (click)="callCp(index, cp.telefoon)" class="mx-1" >
                <ion-icon name="call-outline"></ion-icon>
              </ion-button>
            </div>
            <div class="d-flex ion-justify-content-between ion-align-items-center" *ngIf="cp.mobiel" >
                <div>
                  <div>{{cp.mobiel}}</div>
                </div>
                <ion-button fill="clear" (click)="callCp(index, cp.mobiel)" class="mx-1" >
                  <ion-icon name="call-outline"></ion-icon>
                </ion-button>
              </div>
          </div>
        </div>
      </div>
      <div class="m-2 text-dark" *ngIf="klant.locaties.length" >
        <div class="m-1 ion-text-center">
          <h4 class="m-0">Locaties</h4>
          <div *ngFor="let locatie of klant.locaties;let index = index;" class="ion-text-left border-bottom py-1 my-1" >
            <div class="d-flex ion-justify-content-between ion-align-items-center" >
              <div>
                <div>{{locatie.straat}} {{locatie.huisnummer}}{{locatie.toeveoging}}, {{locatie.postcode}} {{locatie.plaats}}</div>
              </div>
              <ion-button  fill="clear" (click)="maps(index)" class="mx-1" >
                <ion-icon name="map-outline"></ion-icon>
              </ion-button>
            </div>
          </div>
        </div>
      </div>
    </ion-card>

    <div class="mx-0" *ngIf="klant.telefoonnummer || klant.email" >
      <ion-row class="overflow-auto flex-nowrap" >
        <div *ngIf="user.user_permissions['Klanten beheren']" class="d-inline-block" >
          <ion-fab-button (click)="edit()" class="ion-margin mb-0" >
              <ion-icon name="create-outline"></ion-icon>
          </ion-fab-button>
          <span class="button-footer" >Wijzigen</span>
        </div>
        <div class="d-inline-block" >
          <ion-fab-button (click)="opmerkingModal = true" class="ion-margin mb-0" >
            <ion-icon name="bookmark-outline"></ion-icon>
          </ion-fab-button>
          <span class="button-footer" >Opmerking</span>
        </div>
        <div *ngIf="klant.telefoonnummer" class="d-inline-block" >
          <ion-fab-button (click)="call()" class="ion-margin mb-0" >
            <ion-icon name="call-sharp"></ion-icon>
          </ion-fab-button>
          <span class="button-footer" >Bellen</span>
        </div>
        <div *ngIf="klant.telefoonnummer" class="d-inline-block" >
          <ion-fab-button (click)="selectSms(true)" class="ion-margin mb-0" >
            <ion-icon name="chatbubble-ellipses-outline"></ion-icon>
          </ion-fab-button>
          <span class="button-footer" >SMS</span>
        </div>
        <div *ngIf="klant.email" class="d-inline-block" >
          <ion-fab-button (click)="email(true)" class="ion-margin mb-0" >
            <ion-icon name="mail-outline"></ion-icon>
          </ion-fab-button>
          <span class="button-footer" >Email</span>
        </div>
        <div *ngIf="klant.email" class="d-inline-block" >
          <ion-fab-button (click)="maps()" class="ion-margin mb-0" >
            <ion-icon name="map-outline"></ion-icon>
          </ion-fab-button>
          <span class="button-footer" >Maps</span>
        </div>
      </ion-row>
    </div>

    <ion-card style=" margin-right: 0; margin-left: 0;" >
      <ion-list lines="none" >
        <ion-item *ngIf="modules['Rapporten']" class=" ion-margin-vertical" (click)="callPage('klanten/rapporten')">
          <div style="display: inline-block; width: 70%" >
            <ion-icon class="icon ion-margin-horizontal " name="book-outline"></ion-icon>
            <span class="text" >Rapporten</span>
          </div>
          <div style="display: inline-block; width: 30%; text-align: right" >
            <span class="text" >{{klant.rapporten.length}}</span>
            <ion-icon class="icon ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
          </div>
        </ion-item>
        <ion-item *ngIf="modules['Checklists']" class=" ion-margin-vertical" (click)="callPage('klanten/checklists')">
          <div style="display: inline-block; width: 70%" >
            <ion-icon class="icon ion-margin-horizontal"  name="list-outline"></ion-icon>
            <span class="text" >Checklists</span>
          </div>
          <div style="display: inline-block; width: 30%; text-align: right" >
            <span class="text" >{{klant.checklists.length}}</span>
            <ion-icon class="icon ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
          </div>
        </ion-item>
        <ion-item *ngIf="modules['Aanvragen']" class=" ion-margin-vertical" (click)="callPage('klanten/aanvragen')">
          <div style="display: inline-block; width: 70%" >
            <ion-icon class="icon ion-margin-horizontal " name="file-tray-full-outline"></ion-icon>
            <span class="text" >Aanvragen</span>
          </div>
          <div style="display: inline-block; width: 30%; text-align: right" >
            <span class="text" >{{klant.aanvragen.length}}</span>
            <ion-icon class="icon ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
          </div>
        </ion-item>
        <ion-item *ngIf="modules['Offertes']" class=" ion-margin-vertical" (click)="callPage('klanten/offertes')">
          <div style="display: inline-block; width: 70%" >
            <ion-icon class="icon  ion-margin-horizontal" name="clipboard-outline"></ion-icon>
            <span class="text" >Offertes</span>
          </div>
          <div style="display: inline-block; width: 30%; text-align: right" >
            <span class="text" >{{klant.offertes.length}}</span>
            <ion-icon class="icon ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
          </div>
        </ion-item>
        <ion-item *ngIf="modules['Projecten']" class=" ion-margin-vertical" (click)="callPage('klanten/projecten')">
          <div style="display: inline-block; width: 70%" >
            <ion-icon class="icon ion-margin-horizontal" name="folder-open-outline"></ion-icon>
            <span class="text" >Projecten</span>
          </div>
          <div style="display: inline-block; width: 30%; text-align: right" >
            <span class="text" >{{klant.projecten.length}}</span>
            <ion-icon class="icon ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
          </div>
        </ion-item>
        <ion-item *ngIf="modules['Werkbonnen']" class=" ion-margin-vertical" (click)="callPage('klanten/werkbonnen')">
          <div style="display: inline-block; width: 70%" >
            <ion-icon class="icon ion-margin-horizontal" name="document-text-outline"></ion-icon>
            <span class="text" >Werkbonnen</span>
          </div>
          <div style="display: inline-block; width: 30%; text-align: right" >
            <span class="text" >{{klant.werkbonnen.length}}</span>
            <ion-icon class="icon ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
          </div>
        </ion-item>
      </ion-list>
    </ion-card>


    <div class="shadow" >
      <ion-list class="ion-margin-vertical" *ngIf="klant.opmerkingen.length" lines="none">
        <ion-grid >
          <ion-row>
            <ion-col><span class="text my-2">Opmerkingen</span></ion-col>
          </ion-row>
          <ion-row *ngFor="let opmerking of klant.opmerkingen; let i = index;" class="ion-padding-vertical" style="border-top: 1px solid rgba(0,0,0,0.3)" >
            <ion-col size="12" class="ion-text-right" ><ion-text class="ion-margin-horizontal" (click)="confirmOpmerkingDelete(i)" ><ion-icon color="danger" name="trash-bin-outline"></ion-icon></ion-text></ion-col>
            <ion-col size="7" ><ion-text>{{opmerking.user.name}} {{opmerking.user.lastname}}:</ion-text></ion-col>
            <ion-col size="5" class="text-muted ion-text-right" ><ion-text>{{convertDate(opmerking.created_at).date}} {{convertDate(opmerking.created_at).time}}</ion-text></ion-col>
            <ion-col size="12"><ion-text class="ion-text-wrap text-muted " >{{opmerking.opmerking}}</ion-text></ion-col>
          </ion-row>
        </ion-grid>
      </ion-list>
    </div>

    <div class="shadow" >
      <ion-list class="ion-margin-vertical" *ngIf="klant && klant.calls.length" >
        <ion-grid  >
          <ion-row>
            <ion-col><span class="text my-2">Belgeschiedenis</span></ion-col>
          </ion-row>
          <ion-row *ngFor="let call of klant.calls" class="ion-padding-vertical" style="border-top: 1px solid rgba(0,0,0,0.3)" >
            <ion-col size="7" ><ion-text>{{call.user.name}} {{call.user.lastname}}:</ion-text></ion-col>
            <ion-col size="5" class="text-muted ion-text-right" ><ion-text>{{convertDate(call.created_at).date}} {{convertDate(call.created_at).time}}</ion-text></ion-col>
            <ion-col size="12" ><ion-text>
              <ion-text *ngIf="call.response === 'OK'" ><ion-icon color="success" name="chevron-up-outline"></ion-icon></ion-text>
              <ion-text *ngIf="call.response !== 'OK'" ><ion-icon color="danger" name="chevron-down-outline"></ion-icon></ion-text>
              {{call.nummer}}
            </ion-text></ion-col>
            <ion-col size="12"><ion-text class="ion-text-wrap text-muted " *ngIf="call.opmerking" >{{call.opmerking}}</ion-text></ion-col>
          </ion-row>
        </ion-grid>
      </ion-list>
    </div>

    <div class="shadow" >
      <ion-list class="ion-margin-vertical" *ngIf="klant.sms.length" >
        <ion-grid>
          <ion-row>
            <ion-col><span class="text my-2">SMS geschiedenis</span></ion-col>
          </ion-row>
          <ion-row *ngFor="let sms of klant.sms" class="ion-padding-vertical" style="border-top: 1px solid rgba(0,0,0,0.3)" >
            <ion-col size="7" ><ion-text>{{sms.user.name}} {{sms.user.lastname}}:</ion-text></ion-col>
            <ion-col size="5" class="text-muted ion-text-right" ><ion-text>{{convertDate(sms.created_at).date}} {{convertDate(sms.created_at).time}}</ion-text></ion-col>
            <ion-col size="12" ><ion-text>{{sms.number}}</ion-text></ion-col>
            <ion-col size="12"><ion-text class="ion-text-wrap text-muted " >{{sms.content}}</ion-text></ion-col>
          </ion-row>
        </ion-grid>
      </ion-list>
    </div>

    <div class="shadow" >
      <ion-list class="ion-margin-vertical" *ngIf="klant.templatemails.length" >
        <ion-grid>
          <ion-row>
            <ion-col><span class="text my-2">Mail geschiedenis</span></ion-col>
          </ion-row>
          <ion-row *ngFor="let mail of klant.templatemails" class="ion-padding-vertical" style="border-top: 1px solid rgba(0,0,0,0.3)">
            <ion-col size="7" ><ion-text>{{mail.user.name}} {{mail.user.lastname}}:</ion-text></ion-col>
            <ion-col size="5" class="text-muted ion-text-right" ><ion-text>{{convertDate(mail.created_at).date}} {{convertDate(mail.created_at).time}}</ion-text></ion-col>
            <ion-col size="12" class="ion-margin-top" ><ion-text>{{mail.onderwerp}}</ion-text></ion-col>
            <ion-col size="5" ><ion-text>{{mail.afzender}}</ion-text></ion-col>
            <ion-col size="2" class="ion-text-center" ><ion-text><ion-icon name="arrow-forward-outline"></ion-icon></ion-text></ion-col>
            <ion-col size="5" class="ion-text-right"  ><small>{{mail.ontvanger}}</small></ion-col>
            <ion-col size="12"><ion-text class="ion-text-wrap text-muted " [innerHTML]="mail.inhoud" ></ion-text></ion-col>
          </ion-row>
        </ion-grid>
      </ion-list>
    </div>

  </div>
</ion-content>

<ion-content *ngIf="smsModal" >

  <ion-fab vertical="top" horizontal="end" slot="fixed">
    <ion-fab-button (click)="selectSms(false)" class="ion-margin" >
      <ion-icon name="close-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div *ngIf="smsTemplates.length" >
    <ion-list>
      <ion-item *ngFor="let sms of smsTemplates; let i = index" (click)="sendSms(i)" >
        <span class="text">{{sms.name}}</span>
      </ion-item>
    </ion-list>
  </div>

  <div *ngIf="!smsTemplates.length" >
    <h5 class="ion-text-center">Geen templates gevonden</h5>
  </div>
</ion-content>

<ion-content *ngIf="opmerkingModal" >

  <ion-fab vertical="top" horizontal="end" slot="fixed">
    <ion-fab-button (click)="opmerkingModal = false" class="ion-margin" >
      <ion-icon name="close-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <ion-list>
    <ion-item>
      <ion-label position="floating">Opmerking</ion-label>
      <ion-textarea [autoGrow]="true" cols="10" [(ngModel)]="opmerkingValue" ></ion-textarea>
    </ion-item>
  </ion-list>
  <ion-button [disabled]="!submitBtn" (click)="submitOpmerking()" color="success" type="submit" expand="full">Opslaan</ion-button>

</ion-content>

<ion-content *ngIf="emailModal" >

  <ion-fab vertical="top" horizontal="end" slot="fixed">
    <ion-fab-button (click)="email(false)" class="ion-margin" >
      <ion-icon name="close-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab>

  <div *ngIf="!selectedEmail" >
    <div *ngIf="emailTemplates.length" >
      <ion-list>
        <ion-item *ngFor="let email of emailTemplates; let i = index" (click)="selectEmail(i)" >
          <span class="text">{{email.name}}</span>
        </ion-item>
      </ion-list>
    </div>

    <div *ngIf="!emailTemplates.length" >
      <h5 class="ion-text-center">Geen templates gevonden</h5>
    </div>
  </div>

  <div *ngIf="selectedEmail" >
    <ion-list>
      <ion-item>
        <ion-label position="floating">Afzender</ion-label>
        <ion-input [(ngModel)]="selectedEmail.afzender" [value]="selectedEmail.afzender"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="floating">Ontvanger</ion-label>
        <ion-input [(ngModel)]="selectedEmail.ontvanger" [value]="selectedEmail.ontvanger"></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="floating">Onderwerp</ion-label>
        <ion-input [(ngModel)]="selectedEmail.onderwerp" [value]="selectedEmail.onderwerp" ></ion-input>
      </ion-item>
      <ion-item>
        <ion-label position="floating">Inhoud</ion-label>
        <ion-textarea  [autoGrow]="true" [(ngModel)]="selectedEmail.content" [value]="selectedEmail.content" ></ion-textarea>
      </ion-item>
    </ion-list>
    <ion-button [disabled]="!submitBtn" (click)="submitEmail()" color="success" type="submit" expand="full">Versturen</ion-button>

  </div>

</ion-content>
