import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';
import { Router } from '@angular/router';

@Component({
  selector: 'app-edit',
  templateUrl: './edit.page.html',
  styleUrls: ['./edit.page.scss'],
	providers: [Infordb]
})
export class EditPage implements OnInit {
	
	public klant = {
    id: null,
    bv: null,
    naam: null,
    contactpersoon_titel: null,
    contactpersoon_voornaam: null,
    contactpersoon_achternaam: null,
    contactpersoon_functie: null,
    contactpersoon_email: null,
    contactpersoon_telefoon: null,
    contactpersoon_mobiel: null,
    email: null,
    telefoonnummer: null,
    straat: null,
    huisnummer: null,
    toevoeging: null,
    postcode: null,
    plaats: null,
    land: null,
    kvk: null,
    btw_nr: null,
    btw_perc: 21,
    betalingstermijn: 30,
    created_at: null,
    updated_at: null,
    soort_klant: null,
    website: null,
    iban: null,
    bic: null,
    debiteurnummer: null,
    contactpersonen: [],
    locaties: [],
  };

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));

  public button = true;
  public deletePost = false;

  public temp = {
    locatieIndex: null,
  }

  constructor(
    private router: Router,
    private infordb: Infordb,
  ) { }

  getLocation(index?){
    let postcode;
    this.temp.locatieIndex = index;

    if(index !== undefined){
      postcode = this.klant.locaties[index].postcode;
    }
    else{
      postcode = this.klant.postcode;
    }

    if(!postcode || postcode.length < 6){return false;}

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/location`, {
      postcode,
    }, {})
      .then(response => {
				const { status, data } = response.data;
        const { straat, plaats, land } = data;

        if(!status || !data){throw response;}

        if(this.temp.locatieIndex !== undefined){
          this.klant.locaties[this.temp.locatieIndex].straat = straat;
          this.klant.locaties[this.temp.locatieIndex].plaats = plaats;
          this.klant.locaties[this.temp.locatieIndex].land = land;
        }
        else{
          this.klant.straat = straat;
          this.klant.plaats = plaats;
          this.klant.land = land;
        }

      })
      .catch(this.infordb.handleError);

  }

  ngOnInit() {
    if(localStorage.klantEdit){
      this.klant = JSON.parse(localStorage.klantEdit);
      localStorage.removeItem('klantEdit');
    }
  }

  onSubmit(){
    if(!this.klant.email){
      alert('Email kan niet leeg zijn!');
      return false;
    }
    if(!this.klant.bv){
      alert('BV kan niet leeg zijn!');
      return false;
    }

    this.clearKlant();
    this.button = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/store`, {
      klant: JSON.stringify(this.klant),
    })
      .then(response => {
				const { id } = response.data;
        localStorage.klantId = id;

        this.button = true;
				this.infordb.notification({message: 'Klant opgeslagen'});
        this.callPage('klant');
      })
      .catch(err => {
        this.button = true;
        this.infordb.handleError(err);
      });
  }

  delete(){
    if(!confirm('Weet je zeker dat je '+this.klant.contactpersoon_voornaam+' '+this.klant.contactpersoon_achternaam+' wilt verwijderen?')){return false;}

    this.deletePost = true;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/delete`, {
      klant: this.klant.id,
    })
      .then(response => {
        this.callPage('klanten');
      })
      .catch(err => {
        this.deletePost = false;
        this.infordb.handleError(err);
      });
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  clearKlant(){
    this.klant['offertes'] = null;
    this.klant['aanvragen'] = null;
    this.klant['projecten'] = null;
    this.klant['calls'] = null;
    this.klant['werkbonnen'] = null;
    this.klant['rapporten'] = null;
    this.klant['checklists'] = null;
    this.klant['sms'] = null;
    this.klant['opmerkingen'] = null;
    this.klant['templatemails'] = null;
  }

  addLocatie(){
    this.klant.locaties.push({
      id: null,
      klant_id: this.klant.id,
      straat: '',
      huisnummer: '',
      toevoeging: '',
      postcode: '',
      plaats: '',
      land: '',
    });
  }
  removeLocatie(i){
    this.klant.locaties.splice(i, 1);
  }

  addContactpersoon(){
    this.klant.contactpersonen.push({
      id: null,
      klant_id: this.klant.id,
      titel: '',
      voornaam: '',
      achternaam: '',
      functie: '',
      email: '',
      telefoon: '',
      mobiel: '',
    });
  }
  removeContactpersoon(index){
    this.klant.contactpersonen.splice(index, 1);
  }

}
