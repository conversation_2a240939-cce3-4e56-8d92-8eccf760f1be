<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="klant && klant.naam">{{klant.naam}}</ion-title>
    <ion-title *ngIf="klant && !klant.naam">{{klant.contactpersoon_voornaam}} {{klant.contactpersoon_achternaam}}</ion-title>
    <ion-buttons *ngIf="this.klant.id && !deletePost" (click)="delete()" slot="end">
      <ion-title>
        <h2><ion-icon name="trash-outline"></ion-icon></h2>
      </ion-title>
    </ion-buttons>
    <ion-buttons *ngIf="this.klant.id && deletePost" class="opacity-50" slot="end">
      <ion-title>
        <h2><ion-icon name="trash-outline"></ion-icon></h2>
      </ion-title>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div>

    <ion-card>
      <ion-list>

        <ion-item>
          <ion-text><b>Gegevens</b></ion-text>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Soort klant</ion-label>
          <ion-select [value]="klant.soort_klant" [(ngModel)]="klant.soort_klant" >
            <ion-select-option value="Particulier" >Particulier</ion-select-option>
            <ion-select-option value="Zakelijk" >Zakelijk</ion-select-option>
          </ion-select>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Email*</ion-label>
          <ion-input [value]="klant.email" [(ngModel)]="klant.email" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Telefoonnummer</ion-label>
          <ion-input [value]="klant.telefoonnummer" [(ngModel)]="klant.telefoonnummer" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Website</ion-label>
          <ion-input [value]="klant.website" [(ngModel)]="klant.website" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">BV*</ion-label>
          <ion-select [value]="klant.bv" [(ngModel)]="klant.bv" >
            <ion-select-option *ngFor="let bv of user.bvs" value="{{bv.id}}" >{{bv.name}}</ion-select-option>
          </ion-select>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Iban nummer</ion-label>
          <ion-input [value]="klant.iban" [(ngModel)]="klant.iban" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Debiteur nummer</ion-label>
          <ion-input [value]="klant.debiteurnummer" [(ngModel)]="klant.debiteurnummer" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >BTW percentage</ion-label>
          <ion-input [value]="klant.btw_perc" [(ngModel)]="klant.btw_perc" type="number" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Betalingstermijn</ion-label>
          <ion-input [value]="klant.betalingstermijn" [(ngModel)]="klant.betalingstermijn" type="number" ></ion-input>
        </ion-item>

      </ion-list>
    </ion-card>

    <ion-card>
      <ion-list>
        <ion-item>
          <ion-text><b>Locaties</b></ion-text>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Postcode</ion-label>
          <ion-input [value]="klant.postcode" [(ngModel)]="klant.postcode" (change)="getLocation()" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Huisnummer</ion-label>
          <ion-input [value]="klant.huisnummer" [(ngModel)]="klant.huisnummer" type="number" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Toevoeging</ion-label>
          <ion-input [value]="klant.toevoeging" [(ngModel)]="klant.toevoeging" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating"  >Straat</ion-label>
          <ion-input [value]="klant.straat" [(ngModel)]="klant.straat" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Plaats</ion-label>
          <ion-input [value]="klant.plaats" [(ngModel)]="klant.plaats" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Land</ion-label>
          <ion-input [value]="klant.land" [(ngModel)]="klant.land" ></ion-input>
        </ion-item>
      </ion-list>

      <div class="ion-text-center my-2" *ngFor="let locatie of klant.locaties;let locatieIndex = index" >
        <ion-list>
          <ion-item lines="none" >
            <div class="w-100 ion-text-right">
              <ion-button  color="danger" class="m-0" (click)="removeLocatie(locatieIndex)" >
                <ion-icon name="close-outline"></ion-icon>
              </ion-button>
            </div>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Postcode</ion-label>
            <ion-input [value]="locatie.postcode" [(ngModel)]="locatie.postcode" (change)="getLocation(locatieIndex)" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Huisnummer</ion-label>
            <ion-input [value]="locatie.huisnummer" [(ngModel)]="locatie.huisnummer" type="number" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Toevoeging</ion-label>
            <ion-input [value]="locatie.toevoeging" [(ngModel)]="locatie.toevoeging" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating"  >Straat</ion-label>
            <ion-input [value]="locatie.straat" [(ngModel)]="locatie.straat" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Plaats</ion-label>
            <ion-input [value]="locatie.plaats" [(ngModel)]="locatie.plaats" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Land</ion-label>
            <ion-input [value]="locatie.land" [(ngModel)]="locatie.land" ></ion-input>
          </ion-item>
        </ion-list>
      </div>

      <div class="ion-text-center my-2" >
        <ion-button (click)="addLocatie()" >Locatie toeveogen</ion-button>
      </div>

    </ion-card>

    <ion-card>
      <ion-list>
        <ion-item>
          <ion-text><b>Contactpersonen</b></ion-text>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon titel</ion-label>
          <ion-input [value]="klant.contactpersoon_titel" [(ngModel)]="klant.contactpersoon_titel" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon voornaam</ion-label>
          <ion-input [value]="klant.contactpersoon_voornaam" [(ngModel)]="klant.contactpersoon_voornaam" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon achternaam</ion-label>
          <ion-input [value]="klant.contactpersoon_achternaam" [(ngModel)]="klant.contactpersoon_achternaam" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon functie</ion-label>
          <ion-input [value]="klant.contactpersoon_functie" [(ngModel)]="klant.contactpersoon_functie" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon email</ion-label>
          <ion-input [value]="klant.contactpersoon_email" [(ngModel)]="klant.contactpersoon_email" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon telefoon</ion-label>
          <ion-input [value]="klant.contactpersoon_telefoon" [(ngModel)]="klant.contactpersoon_telefoon" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Contactpersoon mobiel</ion-label>
          <ion-input [value]="klant.contactpersoon_mobiel" [(ngModel)]="klant.contactpersoon_mobiel" ></ion-input>
        </ion-item>
      </ion-list>

      <div *ngFor="let contact of klant.contactpersonen; let contactIndex = index" class="my-2" >
        <ion-list>
          <ion-item lines="none" >
            <div class="w-100 ion-text-right">
              <ion-button  color="danger" class="m-0" (click)="removeContactpersoon(contactIndex)" >
                <ion-icon name="close-outline"></ion-icon>
              </ion-button>
            </div>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon titel</ion-label>
            <ion-input [value]="contact.titel" [(ngModel)]="contact.titel" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon voornaam</ion-label>
            <ion-input [value]="contact.voornaam" [(ngModel)]="contact.voornaam" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon achternaam</ion-label>
            <ion-input [value]="contact.achternaam" [(ngModel)]="contact.achternaam" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon functie</ion-label>
            <ion-input [value]="contact.functie" [(ngModel)]="contact.functie" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon email</ion-label>
            <ion-input [value]="contact.email" [(ngModel)]="contact.email" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon telefoon</ion-label>
            <ion-input [value]="contact.telefoon" [(ngModel)]="contact.telefoon" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Contactpersoon mobiel</ion-label>
            <ion-input [value]="contact.mobiel" [(ngModel)]="contact.mobiel" ></ion-input>
          </ion-item>
        </ion-list>
      </div>

      <div class="ion-text-center my-2" >
        <ion-button (click)="addContactpersoon()" >Contactpersoon toevoegen</ion-button>
      </div>

    </ion-card>

    <ion-card *ngIf="klant.soort_klant == 'Zakelijk'" >
      <ion-list>

        <ion-item>
          <ion-text><b>Zakelijke gegevens</b></ion-text>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Bedrijfsnaam</ion-label>
          <ion-input [value]="klant.naam" [(ngModel)]="klant.naam" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >KVK</ion-label>
          <ion-input [value]="klant.kvk" [(ngModel)]="klant.kvk" type="number" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >Bic code</ion-label>
          <ion-input [value]="klant.bic" [(ngModel)]="klant.bic" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating" >BTW nummer</ion-label>
          <ion-input [value]="klant.btw_nr" [(ngModel)]="klant.btw_nr" ></ion-input>
        </ion-item>

      </ion-list>
    </ion-card>

    <div class="mb-3">
      <ion-button expand="full" color="success" [disabled]="!button" (click)="onSubmit()" >Opslaan</ion-button>
    </div>

  </div>

</ion-content>
