import {Component, OnInit} from '@angular/core';
import {Infordb} from 'infordb';
import {Router} from "@angular/router";

@Component({
	selector: 'app-aanvragen',
	templateUrl: './aanvragen.page.html',
	styleUrls: ['./aanvragen.page.scss'],
	providers: [Infordb],
})
export class AanvragenPage implements OnInit {
	
	public subdomain = window.localStorage.getItem('subdomain');
	public user = JSON.parse(window.localStorage.getItem('user'));
	public base = {
		is_loading: false,
	}
	public data = {
		aanvragenLijst: [],
	}
	public aanvragenLijst;
	
	constructor(
		private router: Router,
		private infordb: Infordb,
	) {
	}
	
	ngOnInit() {
		this.getKluisRegels();
	}
	
	getKluisRegels() {
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/wachtwoordkluis/aanvragenOverzicht`, {
			api_token: localStorage.api_token,
		})
			.then(response => {
				if (response.status === 201) {
					this.aanvragenLijst = response.data.aanvragenLijst;
				}
			})
			.catch(this.infordb.handleError);
		
	}
	
	showPassword(wachtwoord, id) {
		const aanvraag = this.aanvragenLijst.find(anv => anv.id == id);
		if (aanvraag._decrypt) {
			aanvraag._decrypt = null;
			return;
		}
		
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/wachtwoordkluis/decrypt`, {
			password: wachtwoord,
		})
			.then(response => {
				if (response.status === 201) {
					aanvraag._decrypt = response.data.decPass;
				}
			})
			.catch(this.infordb.handleError);
		
	}
	
	judgeRequest(id, userId, allow) {
		this.base.is_loading = true;
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/wachtwoordkluis/judgerequest`, {
			api_token: localStorage.api_token,
			allow: allow,
			id: id,
			userId: userId,
		})
			.finally(() => {
				this.base.is_loading = false;
			})
			.then(response => {
				if (response.status === 201) {
					this.ngOnInit()
				}
			})
	}
	
	hasPermission(permissionName: string): boolean {
		var found = false;
		this.user.permissions.forEach(permission => {
			if (permission.permission == permissionName) {
				found = true;
			}
		});
		return found;
	}
	
	callPage(page) {
		try {
			this.router.navigate([`/${page.toLowerCase()}`]);
		} catch (e) {
			alert(e);
		}
	}
}
