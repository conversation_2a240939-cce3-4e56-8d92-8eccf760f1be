<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <!--    <ion-title>Wachtwoordenkluis</ion-title>-->
    <ion-buttons slot="start" *ngIf="hasPermission('Wachtwoorden beheren')">
      <ion-button (click)="callPage('wachtwoordenkluis')">Wachtwoordenkluis</ion-button>
      <ion-button (click)="callPage('wachtwoordenkluis/aanvragen')" *ngIf="hasPermission('Wachtwoorden beheren')">Aanvragen</ion-button>
    </ion-buttons>

    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading" > <ion-spinner></ion-spinner> </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card *ngFor="let aanvraag of aanvragenLijst" >
    <ion-list lines="none">
      <ion-item>
        <ion-label>
          <h1>{{aanvraag.password.naam ?? '[naamloos wachtwoord]'}}</h1>
        </ion-label>
        <ion-text class="ion-text-right ion-align-self-start ion-margin-vertical text-muted" >{{aanvraag.password.categorie}}</ion-text>
      </ion-item>
      <div class="p-2" >
        <div class="my-2" *ngIf="aanvraag.user" >
          <ion-label class="d-block" >Aangevraagd door: </ion-label>
          <ion-text class="font-size-1125 text-dark" >{{aanvraag.user.name + " " + aanvraag.user.lastname ?? ''}}</ion-text>
        </div>
        <div>
          <div class="my-2">
            <ion-label class="d-block" >Gebruikersnaam: </ion-label>
            <ion-text class="font-size-1125 text-dark" *ngIf="aanvraag.password" [innerHTML]="aanvraag.password.gebruikersnaam ?? '-'" ></ion-text>
          </div>
        </div>
        <div>
          <div class="my-2">
            <ion-label class="d-block" >Wachtwoord: </ion-label>
            <ion-text class="font-size-1125 text-dark hiddenPass" [innerText]="aanvraag._decrypt || '********'" ></ion-text>
          </div>
        </div>


        <div class="flex-between overflow-auto">
          <div class="w-100 ion-text-center mr-1">
            <a class="btn btn-inverse-primary w-100" (click)="showPassword(aanvraag.password.wachtwoord, aanvraag.id)"><ion-icon name="eye-outline"></ion-icon></a>
            <small class="text-primary" >inzien</small>
          </div>
          <div class="w-100 ion-text-center mx-1" *ngIf="aanvraag.user" >
            <a class="btn btn-inverse-success w-100"  (click)="judgeRequest(aanvraag.password.id, aanvraag.user.id, 1)"><ion-icon name="checkmark-outline"></ion-icon></a>
            <small class="text-success" >Goedkeuren</small>
          </div>
          <div class="w-100 ion-text-center ml-1" *ngIf="aanvraag.user" >
            <a class="btn btn-inverse-danger w-100" (click)="judgeRequest(aanvraag.password.id, aanvraag.user.id, 0)"><ion-icon name="close-outline"></ion-icon></a>
            <small class="text-danger" >Afkeuren</small>
          </div>
        </div>

      </div>
    </ion-list>
  </ion-card>
</ion-content>
