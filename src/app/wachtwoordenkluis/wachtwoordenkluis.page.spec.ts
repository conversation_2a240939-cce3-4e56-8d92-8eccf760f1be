import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { WachtwoordenkluisPage } from './wachtwoordenkluis.page';

describe('WachtwoordenkluisPage', () => {
  let component: WachtwoordenkluisPage;
  let fixture: ComponentFixture<WachtwoordenkluisPage>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ WachtwoordenkluisPage ],
      imports: [IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(WachtwoordenkluisPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
