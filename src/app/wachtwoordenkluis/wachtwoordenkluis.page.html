<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>

    <ion-buttons  >
      <ion-button slot="start" (click)="callPage('wachtwoordenkluis')">Wachtwoordenkluis</ion-button>
      <ion-button (click)="callPage('wachtwoordenkluis/aanvragen')" *ngIf="hasPermission('Wachtwoorden beheren')">Aanvragen</ion-button>
    </ion-buttons>

    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading" > <ion-spinner></ion-spinner> </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-item>
    <ion-label position="floating">Zoeken</ion-label>
    <ion-input [(ngModel)]="search.searchVal">

    </ion-input>
  </ion-item>
  <ion-card *ngFor="let kluisRegel of data.kluisRegels" >
    <ion-list lines="none" *ngIf="searchRegel(kluisRegel)">
      <ion-item>
        <ion-label>
          <h1>{{kluisRegel.naam ?? '[naamloos wachtwoord]'}}</h1>
        </ion-label>
        <ion-text class="ion-text-right ion-align-self-start ion-margin-vertical text-muted" >{{kluisRegel.categorie}}</ion-text>
      </ion-item>
      <div class="p-2" >
        <div class="my-2" *ngIf="kluisRegel.user" >
          <ion-label class="d-block" >Aangevraagd door: </ion-label>
          <ion-text class="font-size-1125 text-dark" >{{kluisRegel.user.name + " " + kluisRegel.user.lastname ?? ''}}</ion-text>
        </div>
        <div>
          <div class="my-2">
            <ion-label class="d-block" >Gebruikersnaam: </ion-label>
            <ion-text class="font-size-1125 text-dark" *ngIf="kluisRegel" [innerHTML]="kluisRegel.gebruikersnaam ?? '-'" ></ion-text>
          </div>
        </div>
        <div>
          <div class="my-2">
            <ion-label class="d-block" >Wachtwoord: </ion-label>
            <ion-text class="font-size-1125 text-dark hiddenPass" [innerText]="kluisRegel._decrypt || '********'" ></ion-text>
          </div>
        </div>
        <div>
          <div class="my-2">
            <ion-label class="d-block" >Link: </ion-label>
            <ion-text class="font-size-1125 text-dark hiddenPass" >{{kluisRegel.link ?? ''}}</ion-text>
          </div>
        </div>

        <div class="flex-between overflow-auto">
          <div class="w-100 ion-text-center mr-1">
            <a class="btn btn-inverse-primary w-100" (click)="showPassword(kluisRegel.wachtwoord, kluisRegel.id)"><ion-icon name="eye-outline"></ion-icon></a>
            <small class="text-primary" >inzien</small>
          </div>
          <div class="w-100 ion-text-center mr-1">
            <a class="btn btn-inverse-secondary w-100" (click)="copyToClipboard(kluisRegel.wachtwoord)"><ion-icon name="clipboard-outline"></ion-icon></a>
            <small class="text-secondary" >Kopieer</small>
          </div>
        </div>
      </div>
    </ion-list>
  </ion-card>
</ion-content>
