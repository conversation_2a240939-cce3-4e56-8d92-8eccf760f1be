import {Component, OnInit} from '@angular/core';
import {Infordb} from 'infordb';
import {Router} from "@angular/router";

@Component({
	selector: 'app-wachtwoordenkluis',
	templateUrl: './wachtwoordenkluis.page.html',
	styleUrls: ['./wachtwoordenkluis.page.scss'],
	providers: [Infordb],
})
export class WachtwoordenkluisPage implements OnInit {

	public subdomain = window.localStorage.getItem('subdomain');
	public user = JSON.parse(window.localStorage.getItem('user'));
	public base = {
		is_loading: false,
	}
	public search = {
		searchVal: '',
	}
	public data = {
		kluisRegels: [],
	}

	constructor(
		private router: Router,
		private infordb: Infordb,
	) {
	}

	ngOnInit() {
		this.getKluisRegels();
	}

	getKluisRegels() {
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/wachtwoordkluis/get`, {
			app: '1',
		})
			.then(response => {
				if (response.status === 200) {
					this.data.kluisRegels = response.data.kluisRegels;
				}
			})
			.catch(this.infordb.handleError);
	}

	showPassword(wachtwoord, id) {
		const password = this.data.kluisRegels.find(regel => regel.id == id);

		if (password._decrypt) {
			password._decrypt = null;
			return;
		}

		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/wachtwoordkluis/decrypt`, {
			password: wachtwoord,
		})
			.then(response => {
				if (response.status === 201) {
					password._decrypt = response.data.decPass;
				}
			})
			.catch(this.infordb.handleError);

	}

	hasPermission(permissionName: string): boolean {
		var found = false;
		this.user.permissions.forEach(permission => {
			if (permission.permission == permissionName) {
				found = true;
			}
		});
		return found;
	}

	callPage(page) {
		try {
			this.router.navigate([`/${page.toLowerCase()}`]);
		} catch (e) {
			alert(e);
		}
	}

	copyToClipboard(str) {

		let decPass = '';
		//calls decrypt function
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/wachtwoordkluis/decrypt`, {
			password: str
		})
			.then(function (response) {
				if (response.status === 201) {
					decPass = response.data.decPass;

					const el = document.createElement("textarea");
					el.value = decPass;
					el.setAttribute("readonly", "");
					document.body.appendChild(el);

					el.select();
					// Copy text to clipboard
          const successful = document.execCommand("copy");
          if (successful) {
            document.body.removeChild(el);
            return true;
          }
				}
			})
			.catch(this.infordb.handleError);
		return false;
	};

	searchRegel(kluisRegel) {
		const words = this.search.searchVal.toLowerCase().split(' ')
		const {categorie, gebruikersnaam, link, naam} = kluisRegel;
		let concat = `${naam || ''} ${categorie || ''} ${link || ''} ${gebruikersnaam || ''}`;
		concat = concat.toLowerCase()

		for (const word of words) {
			if (!concat.includes(word.toLowerCase())) {
				return false;
			}
		}

		return true;
	}
}
