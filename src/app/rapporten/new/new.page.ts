import { ChangeDetectorRef, Component, ElementRef, OnInit} from '@angular/core';
import { ActionSheetController, LoadingController, Platform, ToastController } from '@ionic/angular';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ModalPage } from '../modal/modal.page';
import { ModalController } from '@ionic/angular';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-new',
  templateUrl: './new.page.html',
  styleUrls: ['./new.page.scss'],
  providers: [FormControl, Infordb, ]
})
export class NewPage implements OnInit {

    public subdomain = window.localStorage.getItem('subdomain');
    public user = JSON.parse(window.localStorage.getItem('user'));
    public klanten;
    public bvs;
    public items;
    public itemsById;
    public button = true;

    public naam;
    public klant: any = {id: null};
    public bv: any = {id: null};

    public order = [];
    public form: FormGroup;
    public images = [];
    public activeRow;

    public template;
    public templatePost = {active: null, post: false};
	
		public modals = {
			search: '',
			bv: false,
			klant: false,
		}


  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private infordb: Infordb,
    private modalController: ModalController,

    // private crop: Crop,
  )
  {
    this.form = this.formBuilder.group({
      naam: this.formBuilder.control(''),
      klant: this.formBuilder.control(''),
      bv: this.formBuilder.control(''),
      template: this.formBuilder.control('0'),
    });
  }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/new`)
      .then(response => {
        if (response.status === 201) {
          const { klanten, bvs, items, itemsById } = response.data;
          this.klanten = klanten;
          this.bvs = bvs;
          this.items = items;
          this.itemsById = itemsById;
        }
      })
      .catch(this.infordb.handleError);

    if(localStorage.getItem('rapportTemplate')){
      const templateId = localStorage.getItem('rapportTemplate');
      localStorage.removeItem('rapportTemplate');
      this.templatePost.active = true;
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/get/rapport`, {
        id: templateId,
      })
        .then(response => {
          this.templatePost.post = true;
          if (response.status === 201) {
            const { rapport } = response.data;
            this.template = rapport;
            this.fillOrder();
          }
        })
        .catch(err => {
          this.templatePost.post = true;
	        this.infordb.handleError(err);
        });
    }

  }

  fillOrder(){
   for(const row of this.template.content){
     if(row.type === 'text' ){
       this.order.push({
         type: 'text',
         align: row.align,
         breedte: 100,
         value: row.value,
         prefill: row.value,
       });
     }
     else if(row.type === 'image'){
       this.order.push({
         type: 'image',
         align: null,
         breedte: row.breedte,
         value: row.value,
         title: row.title,
         cache: this.infordb.randomString(5),
       });
     }
   }
  }

  selectKlant(id){
			console.log(this.klanten.find(row => row.id == id));
    this.klant = this.klanten.find(row => row.id == id);
		this.modals.klant = false;
  }
	searchKlant(klant){
		return this.modals.search ? (klant.naam || `${klant.contactpersoon_voornaam || ''} ${klant.contactpersoon_achternaam}`).toLowerCase().includes(this.modals.search.toLowerCase()) : true;
	}
	
	
	selectBv(id){
			console.log(this.bvs.find(row => row.id == id))
		this.bv = this.bvs.find(row => row.id == id);
			console.log(this.bv);
		this.modals.bv = false;
	}
	searchBv(bv){
		return this.modals.search ? bv.name.toLowerCase().includes(this.modals.search.toLowerCase()) : true;
	}

  addImage(){
    this.order.push({
      type: 'image',
      align: null,
      breedte: 100,
      value: null,
      title: '',
      cache: this.infordb.randomString(5),
    });
  }

  addText(){
    this.order.push({
      type: 'text',
      align: 'left',
      breedte: 100,
      value: '',
      prefill: '',
    });
  }

  addItem(i){
    this.order.push({
      type: 'text',
      align: 'left',
      breedte: null,
      value: this.itemsById[i].value,
      prefill: this.itemsById[i].value,
    });
  }

  removeOrder(i){
    const l = this.order.length;
    this.order.splice(i, 1);
  }

  onSubmit(){
    this.button = false;
    const name = this.form.value.naam;
    const template = this.form.value.template;

    if(!name){
      this.button = true;
      alert('Naam is een verplichte veld!');
      return false;
    }
    if(!this.bv.id){
      this.button = true;
      alert('BV is een verplichte veld!');
      return false;
    }
    if(this.order.length === 0){
	    this.button = true;
	    alert('Rapport kan niet leeg zijn!');
      return false;
    }

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/store`, {
      user: this.user.user_id,
      klant: this.klant.id,
      bv: this.bv.id,
      naam: name,
      template: template,
      order: JSON.stringify(this.order),
    })
      .then(response => {
        if (response.status === 201) {
          this.button = true;
          this.infordb.notification('Rapport opgeslagen!');
          this.callPage('rapporten');
        }
      })
      .catch(err => {
        this.button = true;
        this.infordb.handleError(err);
      });


  }

  callPage(page) {
	  this.router.navigate([`/${page.toLowerCase()}`]);
  }

  async openModal() {
    const modal = await this.modalController.create({
      component: ModalPage,
      componentProps: { items: this.items }
    });
    await modal.present();
    await modal.onDidDismiss().then(data=>{
      if(data.data.index){
        this.addItem(data.data.index);
      }
    });
  }
  async rotateImage(i, degr){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rotate/file`, {
      api_token: localStorage.api_token,
      url: 'verslagen/content/'+this.order[i].value,
      deg: degr,
    }, {})
      .then(response => {
        if (response.status === 201) {
          this.order[i].cache = this.infordb.randomString(5);
        }
      })
      .catch(this.infordb.handleError);
  }
  async selectImage() {
		try{
			const image = await this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/upload`)
			if(!image.status){ throw image.data; }
			this.order[this.activeRow].value = image.data.url;
		}
		catch (e) { this.infordb.handleError(e); }
  }



}
