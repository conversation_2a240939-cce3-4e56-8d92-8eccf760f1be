<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Nieuw rapport</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-card style="padding: 5px; margin: 10px 0;" >
      <form [formGroup]="form" >
        <ion-list>
          <ion-item>
            <ion-label position="floating">Naam</ion-label>
            <ion-input formControlName="naam" [required]="true" ></ion-input>
          </ion-item>
          <ion-item>
            <ion-label>Klant</ion-label>
            <ion-button fill="clear" (click)="modals.klant = true" >{{klant.id ? (klant.naam || (klant.contactpersoon_voornaam || '') + ' ' + (klant.contactpersoon_achternaam || '')) : 'Klant selecteren'}}</ion-button>
          </ion-item>
          <ion-item>
            <ion-label>BV</ion-label>
            <ion-button fill="clear" (click)="modals.bv = true" >{{bv.id ? bv.name : 'BV selecteren'}}</ion-button>
          </ion-item>
          <ion-item>
            <ion-label position="floating" >Opslaan als template</ion-label>
            <ion-select formControlName="template" placeholder="Select One">
              <ion-select-option value="1">Ja</ion-select-option>
              <ion-select-option value="0">Nee</ion-select-option>
            </ion-select>
          </ion-item>
        </ion-list>
      </form>
  </ion-card>

  <div *ngIf="templatePost.active && !templatePost.post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="order.length !== 0" >
  <ion-card style="padding: 5px; margin: 10px 0;" *ngFor="let row of order; let i = index; ">


      <div *ngIf="row.type === 'image'" >
        <ion-buttons class="ion-margin-horizontal" slot="end">
          <ion-button color="danger"  slot="end" (click)="removeOrder(i)"><ion-icon name="trash-bin-outline"></ion-icon></ion-button>
          <ion-button *ngIf="row.value" color="primary" slot="start" (click)="rotateImage(i, 270)">90º</ion-button>
          <ion-button *ngIf="row.value" color="primary" slot="start" (click)="rotateImage(i, 180)">180º</ion-button>
          <ion-button *ngIf="row.value" color="primary" slot="start" (click)="rotateImage(i, 90)">270º</ion-button>
        </ion-buttons>
        <div *ngIf="!row.value" class="focus ion-margin ion-text-center" style="border-radius: 10px;padding: 3.5rem 0;border: 1px solid #dee2e6" (click)="selectImage(); activeRow = i;">
          <b style="font-size: 18px;" >Bestand selecteren</b>
        </div>
        <div  *ngIf="row.value" class="focus ion-margin ion-text-center" (click)="selectImage(); activeRow = i;" style="border-radius: 10px;border: 1px solid #dee2e6; overflow: hidden">
          <ion-img style="height: 100%;" src="https://{{subdomain}}.ikbentessa.nl/api/file/verslagen/content/{{row.value}}?cache={{row.cache}}" ></ion-img>
        </div>
        <ion-list>
          <ion-item>
            <ion-label position="floating">Titel</ion-label>
            <ion-input [(ngModel)]="row.title"  ></ion-input>
          </ion-item>
        </ion-list>
      </div>

      <div *ngIf="row.type === 'text'" >
        <ion-buttons class="ion-margin-horizontal" slot="end" >
          <ion-button color="danger" slot="end" (click)="removeOrder(i)"><ion-icon name="trash-bin-outline"></ion-icon></ion-button>
        </ion-buttons>
        <ion-list>
          <ion-item class="editorParent">
            <textarea [froalaEditor] [(ngModel)]="row.value" [innerHtml]="row.prefill" ></textarea>
          </ion-item>
          <ion-radio-group [(ngModel)]="row.align" value="align">
            <ion-list-header>
              <ion-label>Uitlijning</ion-label>
            </ion-list-header>
            <ion-item>
              <ion-label>Links</ion-label>
              <ion-radio slot="start" value="left"></ion-radio>
            </ion-item>

            <ion-item>
              <ion-label>Midden</ion-label>
              <ion-radio slot="start" value="center"></ion-radio>
            </ion-item>

            <ion-item>
              <ion-label>Rechts</ion-label>
              <ion-radio slot="start" value="right"></ion-radio>
            </ion-item>
          </ion-radio-group>
        </ion-list>
      </div>

  </ion-card>
  </div>

  <ion-button [disabled]="!button" expand="full" color="success" (click)="onSubmit()" >Opslaan</ion-button>

  <ion-fab horizontal="end" vertical="bottom" slot="fixed" class="ion-margin">
    <ion-fab-button color="primary">
      <ion-icon name="caret-back-outline"></ion-icon>
    </ion-fab-button>

    <ion-fab-list side="start">
      <ion-fab-button color="light" (click)="openModal()" >
        <ion-icon name="list-outline"></ion-icon>
      </ion-fab-button>
      <ion-fab-button color="light" (click)="addText()" >
        <ion-icon name="menu-outline"></ion-icon>
      </ion-fab-button>
      <ion-fab-button color="light" (click)="addImage()" >
        <ion-icon name="image-outline"></ion-icon>
      </ion-fab-button>
    </ion-fab-list>

  </ion-fab>

  <!--  Select project-->
  <div class="modal-container" *ngIf="modals.bv" (click)="modals.bv = false;">
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
      <ion-item>
        <ion-label position="floating">Zoeken</ion-label>
        <ion-input [(ngModel)]="modals.search"  class="ion-text-left"></ion-input>
      </ion-item>
      <div class="ion-padding overflow-auto mh-33-vh">
        <div *ngFor="let row of bvs">
          <div class="ion-text-center mb-1" *ngIf="searchBv(row)" >
            <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectBv(row.id)">
              <div class="p-1">
                <span class="d-block">{{row.name || ''}}</span>
              </div>
            </ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

  <!--  Select klant-->
  <div class="modal-container" *ngIf="modals.klant" (click)="modals.klant = false;">
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
      <ion-item>
        <ion-label position="floating">Zoeken</ion-label>
        <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
      </ion-item>
      <div class="ion-padding overflow-auto mh-33-vh">
        <div *ngFor="let row of klanten">
          <div class="ion-text-center mb-1" *ngIf="searchKlant(row)">
            <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="selectKlant(row.id)">
              <div class="p-1">
                <span class="d-block">{{row.naam || (row.contactpersoon_voornaam || '') + ' ' + (row.contactpersoon_achternaam || '')}}</span>
                <small class="opacity-50">{{row.straat}} {{row.huisnummer}}{{row.toevoeging}} {{row.plaats}}</small>
              </div>
            </ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

</ion-content>
