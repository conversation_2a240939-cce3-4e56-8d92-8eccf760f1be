import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';
import {Router} from '@angular/router';

@Component({
  selector: 'app-rapporten',
  templateUrl: './rapporten.page.html',
  styleUrls: ['./rapporten.page.scss'],
	providers: [Infordb]
})
export class RapportenPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public post = false;
  public rapporten = [];
  public permissions = {};

  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
  }

  ionViewWillEnter(){
    for(const per of this.user.permissions){
      this.permissions[per.permission] = true;
    }
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/get`, {
      api_token: localStorage.api_token,
      user: this.user.user_id,
    }, {})
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          this.rapporten = response.data.rapporten;
        }
      })
      .catch(err => {
        this.post = true;
        alert('Er is iets misgegaan, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + JSON.stringify(err.status));
      });
  }

  doRefresh(event) {
    this.ngOnInit();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + date.getFullYear();
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  setRapport(id){
    localStorage.setItem('rapportId', id);
  }


}
