<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Templates</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <ion-card class="ion-padding" *ngFor="let template of templates"  (click)="setTemplate(template.id); callPage('newrapport')" >
    <ion-grid>
      <ion-row>
        <ion-col size="9"><ion-card-title class="ion-margin-horizontal" >{{template.naam}}</ion-card-title></ion-col>
        <ion-col size="3" class="ion-text-right" ><ion-title><ion-icon class="ion-margin-horizontal" name="chevron-forward-outline"></ion-icon></ion-title></ion-col>
      </ion-row>
    </ion-grid>
  </ion-card>
</ion-content>
