import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';


@Component({
  selector: 'app-templates',
  templateUrl: './templates.page.html',
  styleUrls: ['./templates.page.scss'],
	providers: [Infordb]
})
export class TemplatesPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));

  public templates;
  public post = false;



  constructor(
    private router: Router,
    private infordb: Infordb,
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/templates`, {
      api_token: localStorage.api_token,
    }, {})
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          this.templates = response.data.templates;
        }
      })
      .catch(err => {
        this.post = true;
        alert('Er is iets misgegaan, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + JSON.stringify(err.status));
      });

  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  setTemplate(id){
    localStorage.setItem('rapportTemplate', id);
  }



}
