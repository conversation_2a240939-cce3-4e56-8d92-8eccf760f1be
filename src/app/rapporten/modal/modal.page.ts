import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { NavParams } from '@ionic/angular';

@Component({
  selector: 'app-modal',
  templateUrl: './modal.page.html',
  styleUrls: ['./modal.page.scss'],
})
export class ModalPage implements OnInit {

  public items;

  constructor(
    private viewCtrl: ModalController,
    navParams: NavParams
  ) {
    this.items = navParams.get('items');
  }

  ngOnInit() {
  }

  dismiss(i) {
    this.viewCtrl.dismiss({index: i});
  }

}
