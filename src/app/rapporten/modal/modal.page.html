<ion-header>
  <ion-toolbar>
    <ion-buttons (click)="dismiss(null)">
      <ion-title>
        <ion-icon class="ion-margin-horizontal" name="close-outline"></ion-icon>
      </ion-title>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>

  <ion-list>
    <ion-item  *ngFor="let item of items" (click)="dismiss(item.id)"  >
      <ion-text>{{item.naam}}</ion-text>
      <ion-icon slot="end" class="ion-margin-horizontal" name="chevron-forward-outline"></ion-icon>
    </ion-item>
  </ion-list>

</ion-content>
