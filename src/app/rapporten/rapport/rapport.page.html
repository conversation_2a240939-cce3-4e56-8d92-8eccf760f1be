<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="rapport" >{{rapport.naam}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <ion-card class="ion-margin-vertical ion-padding" *ngIf="post && rapport" >
    <ion-card-title>{{rapport.naam}}</ion-card-title>
    <ion-card-subtitle>{{rapport._bv.name}}</ion-card-subtitle>
    <ion-card-subtitle *ngIf="rapport.klant" >{{rapport.klant.title}}</ion-card-subtitle>
    <div class="ion-margin-vertical">
      <ion-card-subtitle>{{convertDate(rapport.datum)}}</ion-card-subtitle>
      <ion-card-subtitle>{{rapport.user.name}} {{rapport.user.lastname}}</ion-card-subtitle>
    </div>
  </ion-card>

  <ion-card class="ion-margin-vertical ion-padding" *ngIf="post && rapport" >
    <div *ngFor="let row of rapport.content" style="display: inline-block; width: calc({{row.breedte}}% - 10px); margin: 0 5px" >
      <div *ngIf="row.type === 'text'" [innerHTML]="row.value" class="ion-margin-vertical" style="text-align: {{row.align}}"></div>
      <div *ngIf="row.type === 'image'" class="ion-margin-vertical">
        <img src="https://{{subdomain}}.ikbentessa.nl/api/file/verslagen/content/{{row.value}}" class="rounded" style="width: 100%">
        <p>{{row.title}}</p>
      </div>
    </div>
  </ion-card>

</ion-content>
