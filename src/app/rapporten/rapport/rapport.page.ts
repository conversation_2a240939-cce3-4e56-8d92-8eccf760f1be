import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';
import {Router} from '@angular/router';

@Component({
  selector: 'app-rapport',
  templateUrl: './rapport.page.html',
  styleUrls: ['./rapport.page.scss'],
	providers: [Infordb]
})
export class RapportPage implements OnInit {

  // public subdomain = window.localStorage.getItem('subdomain');
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public rapportId = localStorage.getItem('rapportId');
  public post = false;
  public rapport;


  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
    localStorage.removeItem('rapportId');
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/rapporten/get/rapport`, {
      id: this.rapportId,
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          this.rapport = response.data.rapport;
        }
      })
      .catch(err => {
        this.post = true;
        alert('Er is iets misgegaan, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + JSON.stringify(err.status));
      });
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() + 1)) + '-' + date.getFullYear();
  }


}
