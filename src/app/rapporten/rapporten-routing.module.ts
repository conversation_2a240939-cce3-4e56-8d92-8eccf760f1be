import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { RapportenPage } from './rapporten.page';

const routes: Routes = [
  {
    path: '',
    component: RapportenPage
  },
  {
    path: 'templates',
    loadChildren: () => import('./templates/templates.module').then( m => m.TemplatesPageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RapportenPageRoutingModule {}
