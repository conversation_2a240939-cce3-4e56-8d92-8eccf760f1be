import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {AccountsService} from "../services/Accounts/accounts.service";


@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
})
export class HomePage implements OnInit {

  public user = JSON.parse(window.localStorage.getItem('user'));
  public subdomain = window.localStorage.getItem('subdomain');
  public modules = this.user.user_modules;
  public permissions;
  public logo = '/assets/logo.png';

  public werkbonnen_templates = this.user.werkbonnen_templates || [];
  public renderList = [];

  public modals = {
    accounts: false,
  }

  constructor(
    public router: Router,
    public accountsService: AccountsService,
  ) {}

  ngOnInit() {
    this.defineLogo();
  }

  ionViewWillEnter(){
    this.user = JSON.parse(window.localStorage.getItem('user'));
    this.modules = this.user.user_modules;
    this.werkbonnen_templates = this.user.werkbonnen_templates;
    this.initModules();
  }
  ionViewDidEnter(){
    this.verifyPasswordReset();
  }

  logOut() {
    window.localStorage.removeItem('user');
    this.router.navigate(['/']);
  }

  initModules() {
    this.renderList = [];

    this.modules.forEach((module) => {
      this.renderList.push({ type: 'module', name: module.name, module });

      // If werkbonnen check for separate templates
      if (module.id == '5') {
        for (const template of this.werkbonnen_templates) {
          if (!!Number(template.menu_item)) {
            this.renderList.push({ type: 'werkbon_template', name: template.naam, template });
          }
        }
      }
    });
  }

  callPage(item: any) {
    if (item.type === 'werkbon_template') {
      localStorage.setItem('preselectedTemplateId', item.template.id);
      this.router.navigate(['/werkbonnen']);
    } else {
      this.router.navigate([`/${item.name.toLowerCase()}`]);
    }
  }

  permissionValue(permission: string): any {
    var p = 0;
    this.user.settings.forEach(setting => {
      if (setting.permission.setting == permission) {
        p =  setting.value;
      }
    });
    return p;
  }
  berekenKleur(n) {
    n++;
    while(n > 3){
      n -= 3;
    }
    return n;
  }
  defineLogo(){
    const date = new Date();
    const year = date.getFullYear();

    const zomer = {start: year+'-07-01', end: year+'-08-31', asset: '/assets/tessa_seasons/tessa_zomer.png'};
    const kerst = {start: year+'-12-20', end: (year+1)+'-01-01', asset: '/assets/tessa_seasons/tessa_kerst.png'};
    const koningsdag = {start: year+'-04-23', end: year+'-04-23', asset: '/assets/tessa_seasons/tessa_koningsdag.png'};
    const pasen = {start: year+'-04-10', end: year+'-04-22', asset: '/assets/tessa_seasons/tessa_pasen.png'};
    const halloween = {start: year+'-10-27', end: year+'-10-31', asset: '/assets/tessa_seasons/tessa_halloween.png'};
    const sinterklaas = {start: year+'-11-29', end: year+'-12-06', asset: '/assets/tessa_seasons/tessa_sinterklaas.png'};
    const valentijnsdag = {start: year+'-02-10', end: year+'-02-14', asset: '/assets/tessa_seasons/tessa_valentijnsdag.png'};

    const seasons = [zomer, kerst, koningsdag, pasen, halloween, sinterklaas, valentijnsdag];

    for(const season of seasons){

      if(this.isDateBetween(season.start, season.end)){
        this.logo = season.asset;
      }
    }
  }
  isDateBetween(a, b){
    const time = new Date().getTime();
    const start = new Date(a).getTime();
    const end = new Date(b).getTime();

    return (time >= start && time <= end);
  }

  verifyPasswordReset(){
    const { role, password_reset_at } = this.user;

    //Reset not requested
    if(!role.passwords_reset_at){ return; }

    //Pass reset on the same day or after the request
    if(password_reset_at && (new Date(password_reset_at)).getTime() >= (new Date(role.passwords_reset_at)).getTime()){ return; }

    this.callPage('reset-password');
  }
}
