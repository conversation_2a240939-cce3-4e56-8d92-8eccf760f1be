import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {Platform} from '@ionic/angular';
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';
import {Router} from '@angular/router';

@Component({
  selector: 'app-iframe',
  templateUrl: './iframe.page.html',
  styleUrls: ['./iframe.page.scss'],
})

export class IframePage implements OnInit{

  @ViewChild('iframe') iframeRef: ElementRef;
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public link: SafeResourceUrl;
  public isAndroid;
  public route = localStorage.iframeRoute;
  public data = localStorage.iframeData;

  constructor(
    private platform: Platform,
    private sanitizer: DomSanitizer,
    private router: Router,
  ) { }

  ngOnInit() {
    this.isAndroid = this.platform.is('android');
    this.createListener();
    this.linkInit();
  }

  createListener(){
    const eventMethod = window.addEventListener ? 'addEventListener' : 'attachEvent';
    const eventer = window[eventMethod];
    const messageEvent = eventMethod === 'attachEvent' ? 'onmessage' : 'message';

    eventer(messageEvent, (e) => {
      const data = JSON.parse(e.data);
      if(data.iframe){ this.post(); }
    });
  }

  linkInit(){
    const link = `https://${this.subdomain}.ikbentessa.nl/force-login/${this.tessUrlEncode(this.route)}/${this.user.password}`;
    this.link = this.sanitizer.bypassSecurityTrustResourceUrl(link);

    window.location.href = link
    this.router.navigate(['/home'], { skipLocationChange: true });
  }

  tessUrlEncode(url){
    url = url.replaceAll('/', '0x736c');
    url = url.replaceAll('?', '0x7175');
    url = url.replaceAll('=', '0x6571');
    return url;
  }

  post(){
    const iframe = this.iframeRef.nativeElement.contentWindow;
    iframe.postMessage(JSON.stringify({
      key: 'app_post_data',
      data: this.data
    }), '*');
  }
}
