import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-memo',
  templateUrl: './memo.page.html',
  styleUrls: ['./memo.page.scss'],
  providers: [Infordb],
})
export class MemoPage implements OnInit {

  memos = [];
  loading = true;
  subdomain = window.localStorage.getItem('subdomain');

  constructor(
    private infordb: Infordb,
  ) { }

  ngOnInit() {
    this.getMemos();
  }

  getMemos() {
    this.infordb.get(`https://${this.subdomain}.ikbentessa.nl/api/memo`)
      .then(res => {
        this.memos = res.data.memos;
        this.loading = false;
      })
      .catch(this.infordb.handleError);
  }

  doRefresh(event){
    this.getMemos();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

}
