<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button></ion-back-button>
    </ion-buttons>
    <ion-title>Memo overzicht</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list style="height: 100%" *ngIf="loading">
    <ion-item>
      <ion-label>Laden...</ion-label>
      <ion-spinner name="lines" ></ion-spinner>
    </ion-item>
  </ion-list>

  <ion-card *ngFor="let memo of memos; let i = index">
    <div *ngIf="memo.image_content"><img src="{{ 'https://' + subdomain + '.ikbentessa.nl/api/file/explorer/files/' + memo.image_content.src }}" alt="" class="w-100"></div>
    <ion-card-header>
      <ion-card-title>{{ memo.title }}</ion-card-title>
    </ion-card-header>

    <ion-card-content class="pre">
      <div class="pre" ng-bind-html="markdown.toHTML(memo.content)" [innerHTML]="memo.content"></div>
      <div *ngFor="let file of memo.files; let i = index">
        <a href="https://{{ subdomain }}.ikbentessa.nl/api/file/explorer/files/{{ file.src }}" class="text-black no">
          <ion-icon name="attach"></ion-icon> {{ file.name }}
        </a>
      </div>
    </ion-card-content>
  </ion-card>

</ion-content>
