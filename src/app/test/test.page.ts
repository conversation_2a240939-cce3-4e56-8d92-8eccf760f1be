import { Component, OnInit } from '@angular/core';
import { Infordb } from "infordb";

@Component({
  selector: 'app-test',
  templateUrl: './test.page.html',
  styleUrls: ['./test.page.scss'],
	providers: [Infordb]
})
export class TestPage implements OnInit {

  public data = {
    fetch: {
        env: localStorage.test_env || 'ikbentessa.nl',
        sub: localStorage.subdomain,
        email: '<EMAIL>',
        pass: '',
        loader: false,
    }
  }

  constructor(
		private infordb: Infordb,
  ) { }

  ngOnInit() {

  }

  userData(){
		localStorage.test_env = this.data.fetch.env;
		
    const {sub, env, email, pass} = this.data.fetch;
    this.data.fetch.loader = true;
    this.infordb.post(`https://${sub}.${env}/api/login`, {
        email: email,
        password: pass,
        token: 'no_token_given',
        brand: 'PC',
        model: 'Debug',
    })
      .then(response => {
        console.log(response)
        if (response.status == 201) {
          var user = response.data;
          const apiToken = user.api_token;
          user = JSON.stringify(user);
          window.localStorage.setItem('subdomain', sub);
          window.localStorage.setItem('user', user);
          window.localStorage.setItem('email', '<EMAIL>');
          window.localStorage.setItem('api_token', apiToken);
          this.data.fetch.loader = false;
        }
      })
      .catch((error) => {
        this.data.fetch.loader = false;
        alert(error)
      });
  }

}
