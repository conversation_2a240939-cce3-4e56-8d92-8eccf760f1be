<ion-content>
  <div class="ion-text-center border bg-inverse-secondary rounded m-3 p-2">
    <h4 class="mt-0 mb-2" >Login Fetch</h4>


        <ion-item>
          <ion-label>Environment</ion-label>
          <ion-select class="ion-text-right" [(ngModel)]="data.fetch.env" >
            <ion-select-option value="ikwiltessa.nl" >ikwiltessa</ion-select-option>
            <ion-select-option value="mijntessa.nl" >mijntessa</ion-select-option>
            <ion-select-option value="ikbentessa.nl" >ikbentessa</ion-select-option>
            <ion-select-option value="ikbentessa.app" >ikbentessa.app</ion-select-option>
          </ion-select>
        </ion-item>

        <ion-item>
          <ion-label>Subdomein</ion-label>
          <ion-input class="ion-text-right" [(ngModel)]="data.fetch.sub" ></ion-input>
        </ion-item>

        <ion-item>
          <ion-label>Email</ion-label>
          <ion-input class="ion-text-right" [(ngModel)]="data.fetch.email" ></ion-input>
        </ion-item>

        <ion-item>
          <ion-label>Wachtwoord</ion-label>
          <ion-input type="password" class="ion-text-right" [(ngModel)]="data.fetch.pass" ></ion-input>
        </ion-item>

    <div class="my-3" >
      <a *ngIf="!this.data.fetch.loader" class="btn btn-inverse-primary" (click)="userData()">User Data</a>
      <ion-spinner *ngIf="this.data.fetch.loader" color="success"></ion-spinner>
    </div>

  </div>
</ion-content>
