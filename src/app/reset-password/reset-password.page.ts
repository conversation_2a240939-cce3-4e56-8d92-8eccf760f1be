import { Component, OnInit } from '@angular/core';
import { Infordb, now } from 'infordb';
import {Router} from "@angular/router";

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.page.html',
  styleUrls: ['./reset-password.page.scss'],
  providers: [
    Infordb,
  ]
})
export class ResetPasswordPage implements OnInit {

  optional: boolean = false;

  user: any = JSON.parse(localStorage.user);
  subdomain: string = localStorage.subdomain;

  posting: boolean = false;
  current_password: string = '';
  password: string = '';
  password_repeat: string = '';

  requirements: { requirement: string, valid: boolean }[] = [];

  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
    this.defineSettings()
  }
  ionViewWillEnter() {
    this.clear();
    this.defineOptional();
  }

  async submit(){
    this.posting = true;

    try{
      await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/users/reset-password`, {
        current_password: this.current_password,
        password: this.password
      });
      await this.infordb.notification({message: 'Wachtwoord succesvol gewijzigd'});

      this.user.password_reset_at = now().date.us
      localStorage.user = JSON.stringify(this.user);

      await this.router.navigate([`/home`]);
      this.posting = false;
    }
    catch (e) {
      this.posting = false;
      this.infordb.handleError(e);
    }

  }
  clear(){
    this.current_password = '';
    this.password = '';
    this.password_repeat = '';
    this.validatePassword();
  }

  defineOptional(){
    this.optional = !!Number(localStorage.getItem('reset_password_optional'))
    localStorage.removeItem('reset_password_optional');
  }

  defineSettings(){
    this.requirements = JSON.parse(this.user.values?.password_reset_requirements?.value || '[]').map((requirement: string) => {
      return {
        requirement: requirement,
        valid: false,
      }
    });

    this.requirements.push({
      requirement: 'repeat',
      valid: false,
    })
  }
  hasRequirement(requirement: string): boolean{
    return !!(this.requirements.find((row) => row.requirement == requirement));
  }
  requirementIsValid(requirement: string): boolean{
    const instance = this.requirements.find((row) => row.requirement == requirement);

    switch (instance.requirement){
      case '12_characters': return this.password.length >= 12;
      case 'uppercase': return /[A-Z]/.test(this.password);
      case 'number': return /[0-9]/.test(this.password);
      case 'repeat': return !!this.password && this.password === this.password_repeat;
    }
  }

  validatePassword() {
    this.requirements.forEach((instance) => {
      instance.valid = this.requirementIsValid(instance.requirement);
    })
  }
  passwordIsValid(){
    return !(this.requirements.find(instance => instance.valid === false)) && this.current_password.length;
  }

}
