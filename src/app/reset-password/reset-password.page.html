<ion-content [fullscreen]="true">

  <ion-header>
    <ion-toolbar>
      <ion-buttons slot="start" *ngIf="optional" >
        <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
      </ion-buttons>
      <ion-title>Reset uw wachtwoord</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="p-4">

    <ion-item>
      <ion-label position="floating">Huidig wachtwoord</ion-label>
      <ion-input
        type="password"
        [(ngModel)]="current_password"
        (ionInput)="validatePassword()"
      >
      </ion-input>
    </ion-item>

    <ion-item>
      <ion-label position="floating">Nieuw wachtwoord</ion-label>
      <ion-input
        type="password"
        [(ngModel)]="password"
        (ionInput)="validatePassword()"
      >
      </ion-input>
    </ion-item>

    <ion-item>
      <ion-label position="floating">Bevestig wachtwoord</ion-label>
      <ion-input
        type="password"
        [(ngModel)]="password_repeat"
        (ionInput)="validatePassword()"
      >
      </ion-input>
    </ion-item>

    <div class="my-3">

      <div class="flex-align font-size-08 text-muted my-2" *ngIf="hasRequirement('12_characters')" >
        <div class="dot-glow mr-2" [ngClass]="requirementIsValid('12_characters') ? 'dot-glow-success' : 'dot-glow-danger'" ></div>
        <div>Minimaal 12 tekens lang</div>
      </div>

      <div class="flex-align font-size-08 text-muted my-2" *ngIf="hasRequirement('uppercase')" >
        <div class="dot-glow mr-2" [ngClass]="requirementIsValid('uppercase') ? 'dot-glow-success' : 'dot-glow-danger'" ></div>
        <div>Minstens een hoofdletter (A-Z)</div>
      </div>

      <div class="flex-align font-size-08 text-muted my-2" *ngIf="hasRequirement('number')" >
        <div class="dot-glow mr-2" [ngClass]="requirementIsValid('number') ? 'dot-glow-success' : 'dot-glow-danger'" ></div>
        <div>Minstens een cijfer (0-9)</div>
      </div>

      <div class="flex-align font-size-08 text-muted my-2" >
        <div class="dot-glow mr-2" [ngClass]="requirementIsValid('repeat') ? 'dot-glow-success' : 'dot-glow-danger'" ></div>
        <div> Wachtwoorden komen overeen</div>
      </div>



    </div>

    <div class="my-4 ion-text-center" *ngIf="passwordIsValid()" >
      <a *ngIf="!posting" (click)="submit()" class="btn btn-success" >Wijzigen</a>
      <ion-spinner color="success" *ngIf="posting" ></ion-spinner>
    </div>

  </div>



</ion-content>
