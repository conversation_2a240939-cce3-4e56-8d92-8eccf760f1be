import { Component, OnInit } from '@angular/core';
import {Router} from "@angular/router";

@Component({
  selector: 'app-overzicht',
  templateUrl: './overzicht.page.html',
  styleUrls: ['./overzicht.page.scss'],
})
export class OverzichtPage implements OnInit {
  public base = {
    is_loading: false,
  }
  constructor(
      private router: Router,
  ) { }

  ngOnInit() {
  }

  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }
}
