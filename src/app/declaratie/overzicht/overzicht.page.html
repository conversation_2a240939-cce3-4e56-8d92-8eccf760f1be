<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>

    <ion-buttons  >
      <ion-button slot="start" (click)="callPage('declaratie')">Aanvragen</ion-button>
      <ion-button (click)="callPage('declaratie/overzicht')">Overzicht</ion-button>
    </ion-buttons>

    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading" > <ion-spinner></ion-spinner> </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

</ion-content>
