<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Declaratie</ion-title>
    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading">
      <ion-spinner></ion-spinner>
    </ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="bg-inverse-secondary">
  <form style="margin-bottom: 25px">
    <ion-card class="m-0 bg-inverse-secondary m-h-100">
      <section>
        <div class="my-2 bg-white">
          <ion-item class="border-bottom pl-2">
            <ion-input type="text" [(ngModel)]="rekening_nr" name="rekening_nr" label="Rekeningnummer" label-placement="floating" class="text-muted" required></ion-input>
          </ion-item>
          <div class="border-bottom">
            <ion-card *ngFor="let declaratie of declaraties; let i = index" [formGroup]="declaratie.form" class="p-2">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <div *ngIf="!declaratie.form.get('bestand')?.value" class="w-50">
                  <ion-button color="primary" (click)="image(declaratie)" expand="block">Bestand</ion-button>
                </div>
                <div *ngIf="declaratie.form.get('bestand')?.value" class="w-50 d-flex">
                  <ion-button color="primary" (click)="image(declaratie)" class="flex-grow-1 mr-1">Wijzig</ion-button>
                  <ion-button color="danger" (click)="delImage(declaratie)" class="flex-grow-1">Verwijder</ion-button>
                </div>
                <ion-spinner *ngIf="declaratie.uploading_image" color="primary"></ion-spinner>
                <ion-button color="danger" (click)="removeDeclaratie(i)" fill="clear">
                  <ion-icon name="trash-outline"></ion-icon>
                </ion-button>
              </div>
             
              <div *ngIf="declaratie.form.get('bestand')?.value" class="mb-2">
                <div class="d-flex align-items-center">
                  <ion-thumbnail slot="start" style="width: 80px; height: 80px; margin-right: 10px;">
                    <ion-img class="rounded" style="object-fit: cover;" [src]="'https://' + subdomain + '.ikbentessa.nl/api/file/declaraties//' + declaratie.form.get('bestand')?.value"></ion-img>
                  </ion-thumbnail>
                </div>
              </div>
             
              <ion-item class="mb-2">
                <ion-textarea formControlName="titel" label="Titel" label-placement="floating" class="text-muted"></ion-textarea>
              </ion-item>
             
              <ion-item class="mb-2">
                <ion-input type="number" formControlName="bedrag" text-right label="Bedrag" label-placement="floating" class="text-muted"></ion-input>
              </ion-item>
             
              <ion-item>
                <ion-textarea formControlName="opmerking" text-right label="Opmerking" label-placement="floating" class="text-muted"></ion-textarea>
              </ion-item>
            </ion-card>
           
            <div class="px-2" style="padding-left: 16px; padding-right: 16px;">
              <ion-button style="margin-top: 15px" expand="full" (click)="addDeclaratie()">Declaratie toevoegen</ion-button>
              <ion-button style="margin-top: 15px" expand="full" color="success" (click)="save()">Declaratie(s) aanvragen</ion-button>
            </div>
          </div>
        </div>
      </section>
    </ion-card>
  </form>
</ion-content>