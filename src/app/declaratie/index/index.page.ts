import { Component, OnInit } from '@angular/core';
import {Router} from "@angular/router";
import { Infordb } from 'infordb';
import { FormBuilder, FormGroup, FormControl, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';

@Component({
  selector: 'app-index',
  templateUrl: './index.page.html',
  styleUrls: ['./index.page.scss'],
  providers: [Infordb]
})
export class IndexPage {

  public subdomain = localStorage.getItem('subdomain');
  public rekening_nr: any;
  public disabled = false;
  public user = JSON.parse(window.localStorage.getItem('user'));
  public declaraties = [];
  

  public base = {
    is_loading: false,
  }
  
  constructor(
      private router: Router,
      private infordb: Infordb,
      public formBuilder: FormBuilder,
  ) {}

  image(declaratie){
    declaratie.uploading_image = true;
    this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/declaraties/upload`)
      .then(response => {
        declaratie.uploading_image = false;
        declaratie.form.get('bestand').setValue(response.data.url)
      })
        .catch(err => {
          declaratie.uploading_image = false;
          alert('Er is iets misgegaan bij het toevoegen van het bestand, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + err);
        });
  }
  
  delImage(declaratie){
    declaratie.form.get('bestand').setValue(null);
  }

  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  addDeclaratie() {
    this.declaraties.push(this.createDeclaratieInstance());
  }

  removeDeclaratie(index) {
    this.declaraties.splice(index, 1);
  }

  createDeclaratieInstance() {
    return {
      form: this.formBuilder.group({
        bestand: new FormControl('', Validators.required),
        titel: new FormControl('', Validators.required),
        bedrag: new FormControl('', Validators.required),
        opmerking: new FormControl(''),
      }),
      uploading_image: false,
    };
  }

  save() {
    const invalidForms = this.declaraties.filter(declaratie => declaratie.form.invalid);

    if (this.declaraties.length === 0) {
      alert('Er zijn geen declaraties om op te slaan.');
      return;
    }

    if (invalidForms.length > 0 || !this.rekening_nr){
      alert('Vul alle verplichte velden in.');
      return;
    }
  
    this.disabled = true;

    const declaratiesData = this.declaraties.map(declaratie => declaratie.form.value);
    const data = {
      app: true,
      rekening_nr: this.rekening_nr,
      declaraties: declaratiesData,
      token: this.user.api_token
    };

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/declaraties/create`, data, {})
      .then(response => {
        if (response.status === 200) {
          this.disabled = false;
          this.infordb.notification('Uw declaraties zijn ingevoerd.');
          this.router.navigate(['/home']);
        }
      })
      .catch(err => {
        this.disabled = false;
        if (err.status === 422) {
          alert("Niet alle verplichte velden zijn ingevoerd.");
        } else {
          alert('Invoeren van de declaraties ging mis. Probeer opnieuw of stuur deze <NAME_EMAIL>. ' + err);
        }
      });
  }  
}
