import { Component, OnInit, AfterViewInit } from '@angular/core';
import { Infordb } from 'infordb';
import { ModalController, NavParams, AlertController, Platform } from '@ionic/angular';

@Component({
  selector: 'app-index',
  templateUrl: './canvas.page.html',
  styleUrls: ['./canvas.page.scss'],
  providers: [Infordb]
})

export class CanvasPage implements OnInit, AfterViewInit {
  public subdomain = localStorage.getItem('subdomain');
  public uploadUrl: string;
  public fileUrl: string;
  public image: string | null;
  public canvasWidth: number | null;
  public canvasHeight: number | null;
  public dataset: number | null;
  public icons: Array<any> = [];
  public selectedIcon: string | null = null;
  private iconImageCache: { [src: string]: HTMLImageElement } = {};

  private ctx: CanvasRenderingContext2D | null = null;
  private previewCtx: CanvasRenderingContext2D | null = null;
  private img: HTMLImageElement = new Image();
  private tempImageData: ImageData | null = null;
  private currentCanvas: HTMLCanvasElement | null = null;
  private dpr: number = window.devicePixelRatio || 1;

  private selectedElement: number | null = null;
  private dragOffsetX: number = 0;
  private dragOffsetY: number = 0;
  private isMoving: boolean = false;

  private initialIconSize: number = 0;
  private initialRectangleSize: number | { width: number, height: number } = 0;
  private initialPinchDistance: number = 0;
  private isPinching: boolean = false;
  private newPosX: number = 0;
  private newPosY: number = 0;
  private totalDragDistance: number = 0;
  private firstDrag: boolean = false;

  private canvasHistory: Array<{
    imageData: ImageData,
    elements: Array<{
      type: string,
      points: any,
      color: string,
      lineWidth: number,
      fillShape?: boolean,
      dashLength?: number,
      src?: string
    }>
  }> = [];
  private currentHistoryIndex = -1;

  private currentStroke: {
    type: 'brush',
    points: Array<{ x: number, y: number }>,
    color: string,
    lineWidth: number
  } | null = null;

  private drawnElements: Array<{
    type: string,
    points: any,
    color: string,
    lineWidth: number,
    fillShape?: boolean,
    dashLength?: number
    src?: string
  }> = [];

  public colorOptions: string[] = [
    '#000000', '#FFFFFF', '#FF0000', '#0000FF',
    '#008000', '#FFA500', '#800080'
  ];

  public toolOptions: Array<{ value: string, icon: string }> = [
    { value: 'selection', icon: 'move-outline' },
    { value: 'grid', icon: 'grid-outline' },
    { value: 'brush', icon: 'brush-outline' },
    { value: 'line', icon: 'remove-outline' },
    { value: 'rectangle', icon: 'square-outline' },
    { value: 'circle', icon: 'ellipse-outline' },
    { value: 'dashed', icon: 'ellipsis-horizontal-outline' },
    { value: 'text', icon: 'text-outline' },
    { value: 'icon', icon: 'image-outline' }
  ];

  public base = {
    is_loading: false,
  };

  public canvas = {
    drawing: false,
    width: 0,
    height: 0,
    saveX: 0,
    saveY: 0,
    lineWidth: 2,
    selectedColor: '#000000',
    scaleFactor: 1,
    selectedTool: 'brush',
    textInput: '',
    dashLength: 5,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    fillShape: false,
    showGrid: false,
    gridSize: 20,
    gridOffsetX: 0,
    gridOffsetY: 0,
    selectedIconIndex: 0,
    currentIcon: null,
  };

  public get canUndo(): boolean {
    return this.currentHistoryIndex > 0;
  }

  constructor(
    private plt: Platform,
    private infordb: Infordb,
    private navParams: NavParams,
    private modalCtrl: ModalController,
    private alertController: AlertController
  ) {
    this.canvas.width = this.plt.width();
    this.canvas.height = this.plt.width() * 0.35;
    this.uploadUrl = this.navParams.get('uploadUrl');
    this.fileUrl = this.navParams.get('fileUrl');
    this.image = this.navParams.get('image') ?? null;
    this.canvasWidth = this.navParams.get('canvasWidth') ?? null;
    this.canvasHeight = this.navParams.get('canvasHeight') ?? null;
    this.dataset = this.navParams.get('dataset') ?? null;
  }

  ngOnInit() {
    this.initializeSizePreview();
    if (this.dataset) {
      this.loadIcons();
    }
  }

  ngAfterViewInit() {
    if (this.image) {
      this.loadImage();
    } else if (this.canvasWidth && this.canvasHeight) {
      this.initBlankCanvas(this.canvasWidth, this.canvasHeight);
    } else {
      this.initCanvas();
    }

    const canvas = document.getElementById('edit-field') as HTMLCanvasElement;
    if (canvas) {
      canvas.addEventListener('touchstart', (event) => {
        if (event.touches.length === 2) {
          event.preventDefault();
          this.handlePinchStart(event);
        }
      }, { passive: false });

      canvas.addEventListener('touchmove', (event) => {
        if (event.touches.length === 2 && this.isPinching) {
          event.preventDefault();
          this.handlePinchMove(event);
        }
      }, { passive: false });

      canvas.addEventListener('touchend', () => {
        if (this.isPinching) {
          this.handlePinchEnd();
        }
      });

      canvas.addEventListener('touchcancel', () => {
        if (this.isPinching) {
          this.handlePinchEnd();
        }
      });
    }
  }

  initializeSizePreview() {
    const previewCanvas = document.getElementById('size-preview') as HTMLCanvasElement;
    if (previewCanvas) {
      this.previewCtx = previewCanvas.getContext('2d');

      previewCanvas.width = previewCanvas.width * this.dpr;
      previewCanvas.height = previewCanvas.height * this.dpr;
      this.previewCtx?.scale(this.dpr, this.dpr);

      this.updateSizePreview();
    }
  }

  loadIcons() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/offertes/datasets/get`, {
      id: this.dataset
    })
      .then(response => {
        if (response.status == 200 && response.data && response.data.dataset) {
          this.icons = [];

          response.data.dataset.items.forEach(item => {
            if (item.files && item.files.length > 0) {
              item.files.forEach(file => {
                const iconSrc = `https://${this.subdomain}.ikbentessa.nl/api/file/explorer/files/${file.src}`;
                this.icons.push({
                  src: iconSrc,
                  name: file.name || `Icon ${this.icons.length + 1}`,
                  item: item,
                  file: file
                });
              });
            }
          });

          if (this.icons.length > 0) {
            this.updateSelectedIcon();
          }
        }
      })
      .catch(error => {
        console.error('Error loading icons:', error);
      });
  }

  updateSelectedIcon() {
    if (this.icons.length > 0 &&
      this.canvas.selectedIconIndex >= 0 &&
      this.canvas.selectedIconIndex < this.icons.length) {
      const selectedIcon = this.icons[this.canvas.selectedIconIndex];

      const img = new Image();
      img.src = selectedIcon.src;

      this.canvas.currentIcon = {
        img: img,
        src: selectedIcon.src,
        name: selectedIcon.name,
        width: this.canvas.lineWidth,
        height: this.canvas.lineWidth
      };
    }
  }

  selectIcon(iconUrl: string) {
    this.selectedIcon = iconUrl;
    this.canvas.selectedTool = 'icon';
  }

  handlePinchStart(event: TouchEvent) {
    if (event.touches.length === 2 && this.selectedElement !== null) {

      if (this.totalDragDistance > 25) {
        return;
      }

      this.isPinching = true;
      this.isMoving = false;

      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      this.initialPinchDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      const element = this.drawnElements[this.selectedElement];
      switch (element.type) {
        case 'icon':
          this.initialIconSize = element.points.width;
          break;
        case 'rectangle':
          this.initialRectangleSize = {
            width: element.points.endX - element.points.startX,
            height: element.points.endY - element.points.startY
          };
          break;
        case 'circle':
          this.initialIconSize = element.points.radius;
          break;
        case 'text':
          this.initialIconSize = element.lineWidth;
          break;
      }

      if (this.ctx && this.currentCanvas) {
        this.tempImageData = this.ctx.getImageData(0, 0, this.currentCanvas.width, this.currentCanvas.height);
      }
    }
  }


  handlePinchMove(event: TouchEvent) {
    if (!this.isPinching || this.selectedElement === null || !this.tempImageData || !this.ctx || this.isMoving) return;

    if (event.touches.length === 2) {
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const currentDistance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      );

      let scaleFactor = currentDistance / this.initialPinchDistance;
      const element = this.drawnElements[this.selectedElement];
      this.ctx.putImageData(this.tempImageData, 0, 0);

      switch (element.type) {
        case 'icon':
          const newSize = Math.max(10, Math.min(200, this.initialIconSize * scaleFactor));
          element.points.width = newSize;
          element.points.height = newSize;
          break;
        case 'rectangle':
          if (typeof this.initialRectangleSize !== 'object') return;
          const centerX = (element.points.startX + element.points.endX) / 2;
          const centerY = (element.points.startY + element.points.endY) / 2;

          const initialWidth = this.initialRectangleSize.width;
          const initialHeight = this.initialRectangleSize.height;

          const newWidth = initialWidth * scaleFactor;
          const newHeight = initialHeight * scaleFactor;

          element.points.startX = centerX - newWidth / 2;
          element.points.endX = centerX + newWidth / 2;
          element.points.startY = centerY - newHeight / 2;
          element.points.endY = centerY + newHeight / 2;
          break;
        case 'circle':
          const newRadius = Math.max(5, Math.min(200, this.initialIconSize * scaleFactor));
          element.points.radius = newRadius;
          break;
        case 'text':
          const newFontSize = Math.max(8, Math.min(72, this.initialIconSize * scaleFactor));
          element.lineWidth = newFontSize;
          break;
      }

      let centerPosX, centerPosY;
      switch (element.type) {
        case 'icon':
          centerPosX = element.points.x + element.points.width / 2;
          centerPosY = element.points.y + element.points.height / 2;
          break;
        case 'rectangle':
          centerPosX = (element.points.startX + element.points.endX) / 2;
          centerPosY = (element.points.startY + element.points.endY) / 2;
          break;
        case 'circle':
          centerPosX = element.points.centerX;
          centerPosY = element.points.centerY;
          break;
        case 'text':
          centerPosX = element.points.x;
          centerPosY = element.points.y - element.lineWidth / 2;
          break;
      }

      this.newPosX = centerPosX;
      this.newPosY = centerPosY;

      this.redrawCanvas();

      this.ctx.font = "16px Arial";
      this.ctx.fillStyle = "black";
      this.ctx.strokeStyle = "white";
      this.ctx.lineWidth = 2;

      const percentDiff = Math.round((scaleFactor - 1) * 100);
      const formatted = (percentDiff > 0 ? "+" : "") + percentDiff + "%";
      this.ctx.strokeText(formatted, centerPosX, centerPosY);
      this.ctx.fillText(formatted, centerPosX, centerPosY);
    }
  }

  handlePinchEnd() {
    if (this.isPinching && this.selectedElement !== null) {
      this.isPinching = false;
      this.saveCanvasState();
    }
  }

  drawIcon(x: number, y: number) {
    if (!this.ctx || !this.selectedIcon) return;

    const iconImg = new Image();
    iconImg.crossOrigin = "anonymous";
    iconImg.onload = () => {
      const iconSize = this.canvas.lineWidth * 10;
      this.ctx!.drawImage(iconImg, x - iconSize / 2, y - iconSize / 2, iconSize, iconSize);

      this.drawnElements.push({
        type: 'icon',
        points: { x: x - iconSize / 2, y: y - iconSize / 2, width: iconSize, height: iconSize },
        src: this.selectedIcon,
        color: '',
        lineWidth: this.canvas.lineWidth
      });

      this.saveCanvasState();
    };

    iconImg.src = this.selectedIcon;
  }

  initCanvas() {
    const canvas = document.getElementById('edit-field') as HTMLCanvasElement;
    this.currentCanvas = canvas;
    this.ctx = canvas.getContext('2d');

    if (!this.ctx) return;

    const maxWidth = this.plt.width() * 0.9;
    const maxHeight = maxWidth * 0.75;

    canvas.style.width = maxWidth + 'px';
    canvas.style.height = maxHeight + 'px';

    canvas.width = maxWidth * this.dpr;
    canvas.height = maxHeight * this.dpr;

    this.ctx.scale(this.dpr, this.dpr);
    this.ctx.fillStyle = "white";
    this.ctx.fillRect(0, 0, canvas.width / this.dpr, canvas.height / this.dpr);

    if (this.canvas.showGrid) {
      this.createGridOverlay();
    }

    this.updateSizePreview();

    this.saveCanvasState();
  }

  initBlankCanvas(width, height) {
    const canvas = document.getElementById('edit-field') as HTMLCanvasElement;
    this.currentCanvas = canvas;
    this.ctx = canvas.getContext('2d');

    if (!this.ctx) return;

    const maxWidth = this.plt.width() * 0.9;
    let canvasWidth, canvasHeight;

    const aspectRatio = width / height;

    if (width > height) {
      canvasWidth = maxWidth;
      canvasHeight = maxWidth / aspectRatio;
    } else {
      canvasHeight = maxWidth;
      canvasWidth = maxWidth * aspectRatio;
    }

    canvas.style.width = canvasWidth + 'px';
    canvas.style.height = canvasHeight + 'px';

    canvas.width = canvasWidth * this.dpr;
    canvas.height = canvasHeight * this.dpr;

    this.ctx.scale(this.dpr, this.dpr);
    this.ctx.fillStyle = "white";
    this.ctx.fillRect(0, 0, canvas.width / this.dpr, canvas.height / this.dpr);

    if (this.canvas.showGrid) {
      this.createGridOverlay();
    }

    this.updateSizePreview();

    this.saveCanvasState();
  }

  clearCanvas() {
    this.selectedElement = null;

    const canvas = document.getElementById('edit-field') as HTMLCanvasElement;
    this.ctx = canvas.getContext('2d');

    if (!this.ctx) return;

    this.drawnElements = [];
    this.selectedElement = null;

    if (this.image) {
      this.loadImage();
    } else if (this.canvasWidth && this.canvasHeight) {
      this.initBlankCanvas(this.canvasWidth, this.canvasHeight);
    } else {
      this.initCanvas();
    }
  }

  redrawCanvas() {
    if (!this.ctx || !this.currentCanvas) return;

    this.ctx.fillStyle = "white";
    this.ctx.fillRect(0, 0, this.currentCanvas.width / this.dpr, this.currentCanvas.height / this.dpr);

    if (this.image) {
      this.ctx.drawImage(this.img, 0, 0, this.currentCanvas.width / this.dpr, this.currentCanvas.height / this.dpr);
    }

    this.ctx.lineCap = 'butt';
    this.ctx.lineJoin = 'miter';

    for (const element of this.drawnElements) {
      switch (element.type) {
        case 'brush':
          this.ctx.beginPath();
          this.ctx.strokeStyle = element.color;
          this.ctx.lineWidth = element.lineWidth;
          this.ctx.lineJoin = 'round';
          this.ctx.lineCap = 'round';
          const points = element.points;
          for (let j = 1; j < points.length; j++) {
            const prev = points[j - 1];
            const curr = points[j];
            this.ctx.moveTo(prev.x, prev.y);
            this.ctx.lineTo(curr.x, curr.y);
          }
          this.ctx.stroke();
          break;

        case 'rectangle':
          this.ctx.beginPath();
          this.ctx.rect(
            element.points.startX,
            element.points.startY,
            element.points.endX - element.points.startX,
            element.points.endY - element.points.startY
          );
          if (element.fillShape) {
            this.ctx.fillStyle = element.color;
            this.ctx.fill();
          }
          this.ctx.strokeStyle = element.color;
          this.ctx.lineWidth = element.lineWidth;
          this.ctx.stroke();
          break;

        case 'circle':
          this.ctx.beginPath();
          this.ctx.arc(
            element.points.centerX,
            element.points.centerY,
            element.points.radius,
            0, 2 * Math.PI
          );
          if (element.fillShape) {
            this.ctx.fillStyle = element.color;
            this.ctx.fill();
          }
          this.ctx.strokeStyle = element.color;
          this.ctx.lineWidth = element.lineWidth;
          this.ctx.stroke();
          break;

        case 'line':
          this.ctx.beginPath();
          this.ctx.setLineDash([]);
          this.ctx.moveTo(element.points.startX, element.points.startY);
          this.ctx.lineTo(element.points.endX, element.points.endY);
          this.ctx.strokeStyle = element.color;
          this.ctx.lineWidth = element.lineWidth;
          this.ctx.stroke();
          break;

        case 'dashed':
          this.ctx.beginPath();
          this.ctx.setLineDash([element.dashLength, element.dashLength]);
          this.ctx.moveTo(element.points.startX, element.points.startY);
          this.ctx.lineTo(element.points.endX, element.points.endY);
          this.ctx.strokeStyle = element.color;
          this.ctx.lineWidth = element.lineWidth;
          this.ctx.stroke();
          this.ctx.setLineDash([]);
          break;

        case 'text':
          this.ctx.font = `${element.lineWidth}px Arial`;
          this.ctx.fillStyle = element.color;
          this.ctx.fillText(element.points.text, element.points.x, element.points.y);
          break;

        case 'icon':
          const cachedImg = this.iconImageCache[element.src];
          if (cachedImg) {
            this.ctx.drawImage(
              cachedImg,
              element.points.x,
              element.points.y,
              element.points.width,
              element.points.height
            );
          } else {
            const iconImg = new Image();
            iconImg.crossOrigin = "anonymous";
            iconImg.onload = () => {
              this.iconImageCache[element.src] = iconImg;
              this.ctx!.drawImage(
                iconImg,
                element.points.x,
                element.points.y,
                element.points.width,
                element.points.height
              );
            };
            iconImg.src = element.src;
          }
          break;
      }
    }

    if (this.selectedElement !== null && !this.isMoving) {
      this.drawSelectionOutline(this.drawnElements[this.selectedElement]);
    }
  }

  saveCanvasState() {
    if (!this.currentCanvas || !this.ctx) return;

    const previousSelection = this.selectedElement;

    this.selectedElement = null;
    this.redrawCanvas();

    const imageData = this.ctx.getImageData(0, 0, this.currentCanvas.width, this.currentCanvas.height);
    const elementsCopy = JSON.parse(JSON.stringify(this.drawnElements));

    this.selectedElement = previousSelection;

    if (this.selectedElement !== null) {
      this.drawSelectionOutline(this.drawnElements[this.selectedElement]);
    }

    if (this.currentHistoryIndex < this.canvasHistory.length - 1) {
      this.canvasHistory = this.canvasHistory.slice(0, this.currentHistoryIndex + 1);
    }

    if (this.currentHistoryIndex >= 0) {
      const lastState = this.canvasHistory[this.currentHistoryIndex];

      if (elementsCopy.length === lastState.elements.length &&
        JSON.stringify(elementsCopy) === JSON.stringify(lastState.elements)) {
        return;
      }
    }

    this.canvasHistory.push({
      imageData: imageData,
      elements: elementsCopy,
    });

    this.currentHistoryIndex = this.canvasHistory.length - 1;

    if (this.canvasHistory.length > 50) {
      this.canvasHistory.shift();
      this.currentHistoryIndex--;
    }
  }

  undoLastAction() {
    if (!this.ctx || !this.currentCanvas || this.currentHistoryIndex <= 0) return;

    this.currentHistoryIndex--;
    const prevState = this.canvasHistory[this.currentHistoryIndex];

    if (prevState) {
      this.ctx.putImageData(prevState.imageData, 0, 0);

      this.drawnElements = JSON.parse(JSON.stringify(prevState.elements));

      this.selectedElement = null;
      this.isMoving = false;
      this.tempImageData = null;
    }
  }

  redoAction() {
    if (!this.ctx || !this.currentCanvas || this.currentHistoryIndex >= this.canvasHistory.length - 1) return;

    this.currentHistoryIndex++;
    const nextState = this.canvasHistory[this.currentHistoryIndex];

    if (nextState) {
      this.ctx.putImageData(nextState.imageData, 0, 0);

      this.drawnElements = JSON.parse(JSON.stringify(nextState.elements));

      this.selectedElement = null;
      this.isMoving = false;
      this.tempImageData = null;
    }
  }

  public get canRedo(): boolean {
    return this.currentHistoryIndex < this.canvasHistory.length - 1;
  }


  loadImage() {
    this.drawnElements = [];
    this.selectedElement = null;

    if (!this.image) {
      this.initCanvas();
      return;
    }

    fetch(this.fileUrl + this.image, { mode: 'cors' })
      .then(response => response.blob())
      .then(blob => {
        const objectURL = URL.createObjectURL(blob);
        const canvas = document.getElementById('edit-field') as HTMLCanvasElement;
        this.currentCanvas = canvas;
        this.ctx = canvas.getContext('2d');

        if (!this.ctx) return;

        this.img = new Image();
        this.img.onload = () => {
          const imgWidth = this.img.naturalWidth;
          const imgHeight = this.img.naturalHeight;
          const aspectRatio = imgWidth / imgHeight;
          const maxWidth = this.plt.width() * 0.9;

          canvas.style.width = maxWidth + 'px';
          canvas.style.height = (maxWidth / aspectRatio) + 'px';

          canvas.width = maxWidth * this.dpr;
          canvas.height = (maxWidth / aspectRatio) * this.dpr;

          this.ctx?.scale(this.dpr, this.dpr);

          this.ctx?.clearRect(0, 0, canvas.width / this.dpr, canvas.height / this.dpr);
          this.ctx!.fillStyle = "white";
          this.ctx?.fillRect(0, 0, canvas.width / this.dpr, canvas.height / this.dpr);
          this.ctx?.drawImage(this.img, 0, 0, canvas.width / this.dpr, canvas.height / this.dpr);

          if (this.canvas.showGrid) {
            this.createGridOverlay();
          }

          this.updateSizePreview();

          this.saveCanvasState();
        };

        this.img.src = objectURL;
      })
      .catch((err) => {
        alert('Er is iets misgegaan bij het inladen van het bestand, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + err);
      });
  }

  selectTool(tool) {
    this.canvas.selectedTool = tool;
    this.selectedElement = null;
    this.redrawCanvas();
  }

  selectColor(color) {
    this.canvas.selectedColor = color;
    this.updateSizePreview();
  }

  updateLineWidth(event: any) {
    this.canvas.lineWidth = event.detail.value;
    this.updateSizePreview();
  }

  updateDashLength(event) {
    this.canvas.dashLength = event.detail.value;
  }

  updateSizePreview() {
    if (!this.previewCtx) return;

    const canvas = document.getElementById('size-preview') as HTMLCanvasElement;
    this.previewCtx.clearRect(0, 0, canvas.width / this.dpr, canvas.height / this.dpr);

    const centerX = (canvas.width / this.dpr) / 2;
    const centerY = (canvas.height / this.dpr) / 2;
    const scaledWidth = this.canvas.lineWidth * (this.canvas.scaleFactor || 1);

    this.previewCtx.beginPath();
    this.previewCtx.arc(centerX, centerY, scaledWidth / 2, 0, 2 * Math.PI);
    this.previewCtx.fillStyle = this.canvas.selectedColor;
    this.previewCtx.fill();
  }

  toggleGrid() {
    if (this.canvas.showGrid) {
      this.createGridOverlay();
    }
  }

  updateGridSize(event) {
    this.canvas.gridSize = event.detail.value;
    if (this.canvas.showGrid) {
      this.createGridOverlay();
    }
  }

  createGridOverlay() {
    const gridOverlay = document.getElementById('grid-overlay');
    if (!gridOverlay || !this.currentCanvas) return;

    while (gridOverlay.firstChild) {
      gridOverlay.removeChild(gridOverlay.firstChild);
    }

    const canvas = this.currentCanvas;
    const gridSize = this.canvas.gridSize;
    const canvasRect = canvas.getBoundingClientRect();

    gridOverlay.style.width = canvasRect.width + 'px';
    gridOverlay.style.height = canvasRect.height + 'px';

    const svgNS = "http://www.w3.org/2000/svg";
    const svg = document.createElementNS(svgNS, "svg");
    svg.setAttribute("width", "100%");
    svg.setAttribute("height", "100%");
    svg.style.position = "absolute";
    svg.style.top = "0";
    svg.style.left = "0";

    const totalWidth = canvasRect.width;
    const totalHeight = canvasRect.height;

    const offsetX = this.canvas.gridOffsetX % gridSize;
    const offsetY = this.canvas.gridOffsetY % gridSize;

    for (let i = offsetX; i <= totalWidth + gridSize; i += gridSize) {
      const line = document.createElementNS(svgNS, "line");
      line.setAttribute("x1", i.toString());
      line.setAttribute("y1", "0");
      line.setAttribute("x2", i.toString());
      line.setAttribute("y2", totalHeight.toString());
      line.setAttribute("stroke", "#cccccc");
      line.setAttribute("stroke-width", "0.5");
      svg.appendChild(line);
    }

    for (let i = offsetY; i <= totalHeight + gridSize; i += gridSize) {
      const line = document.createElementNS(svgNS, "line");
      line.setAttribute("x1", "0");
      line.setAttribute("y1", i.toString());
      line.setAttribute("x2", totalWidth.toString());
      line.setAttribute("y2", i.toString());
      line.setAttribute("stroke", "#cccccc");
      line.setAttribute("stroke-width", "0.5");
      svg.appendChild(line);
    }

    gridOverlay.appendChild(svg);
  }

  startDrawing(ev) {
    ev.preventDefault();
    if (this.isPinching || (ev.touches && ev.touches.length > 1)) return;
    if (!this.ctx) return;

    const canvas = ev.target as HTMLCanvasElement;
    this.currentCanvas = canvas;
    const rect = canvas.getBoundingClientRect();

    const clientX = 'touches' in ev ? ev.touches[0].clientX : ev.clientX;
    const clientY = 'touches' in ev ? ev.touches[0].clientY : ev.clientY;

    let x = (clientX - rect.left);
    let y = (clientY - rect.top);

    if (
      this.canvas.showGrid &&
      !['brush', 'text', 'selection', 'grid'].includes(this.canvas.selectedTool)
    ) {
      x = Math.round((x - this.canvas.gridOffsetX) / this.canvas.gridSize) * this.canvas.gridSize + this.canvas.gridOffsetX;
      y = Math.round((y - this.canvas.gridOffsetY) / this.canvas.gridSize) * this.canvas.gridSize + this.canvas.gridOffsetY;
    }
    

    if (this.canvas.selectedTool === 'icon' && this.selectedIcon) {
      this.drawIcon(x, y);
      return;
    }

    if (this.canvas.selectedTool === 'grid') {
      this.canvas.drawing = true;
      this.canvas.saveX = x;
      this.canvas.saveY = y;
      return;
    }

    if (this.canvas.selectedTool === 'selection') {
      this.handleSelection(x, y);
      return;
    }

    if (this.canvas.selectedTool === 'text') {
      this.promptForText(x, y);
      return;
    }

    this.canvas.drawing = true;
    this.canvas.saveX = x;
    this.canvas.saveY = y;
    this.canvas.startX = x;
    this.canvas.startY = y;
    this.canvas.currentX = x;
    this.canvas.currentY = y;

    const scaledWidth = this.canvas.lineWidth * (this.canvas.scaleFactor || 1);

    if (this.canvas.selectedTool === 'brush') {
      this.currentStroke = {
        type: 'brush',
        points: [{ x, y }],
        color: this.canvas.selectedColor,
        lineWidth: scaledWidth
      };
    }

    if (this.canvas.selectedTool !== 'brush') {
      this.tempImageData = this.ctx.getImageData(0, 0, canvas.width, canvas.height);
    }
  }

  async promptForText(x: number, y: number) {
    const alert = await this.alertController.create({
      header: 'Tekst invoeren',
      inputs: [
        {
          name: 'text',
          type: 'text',
          placeholder: 'Voer tekst in...'
        }
      ],
      buttons: [
        {
          text: 'Annuleren',
          role: 'cancel'
        },
        {
          text: 'Plaatsen',
          handler: (data) => {
            if (data.text && data.text.trim() !== '') {
              this.placeTextOnCanvas(x, y, data.text);
            }
          }
        }
      ]
    });

    await alert.present();
  }

  placeTextOnCanvas(x: number, y: number, text: string) {
    if (!this.ctx) return;

    const scaledWidth = this.canvas.lineWidth * (this.canvas.scaleFactor || 1);
    this.ctx.font = `${scaledWidth * 2}px Arial`;
    this.ctx.fillStyle = this.canvas.selectedColor;
    this.ctx.fillText(text, x, y);

    this.drawnElements.push({
      type: 'text',
      points: { x: x, y: y, text: text },
      color: this.canvas.selectedColor,
      lineWidth: scaledWidth * 2
    });

    this.saveCanvasState();
  }

  handleSelection(x: number, y: number) {
    if (!this.ctx || !this.currentCanvas || this.isPinching) return;

    this.firstDrag = false;

    if (this.selectedElement !== null) {
      const element = this.drawnElements[this.selectedElement];
      let deleteX = 0;
      let deleteY = 0;

      switch (element.type) {
        case 'rectangle':
          deleteX = element.points.startX - 15 + 10;
          deleteY = element.points.startY - 15 + 10;
          break;
        case 'circle':
          deleteX = element.points.centerX - element.points.radius - 15 + 10;
          deleteY = element.points.centerY - element.points.radius - 15 + 10;
          break;
        case 'line':
        case 'dashed':
          deleteX = element.points.startX - 15 + 10;
          deleteY = element.points.startY - 15 + 10;
          break;
        case 'text':
          deleteX = element.points.x - 15 + 10;
          deleteY = element.points.y - element.lineWidth - 15 + 10;
          break;
        case 'icon':
          deleteX = element.points.x - 15 + 10;
          deleteY = element.points.y - 15 + 10;
          break;
      }

      const distance = Math.sqrt(Math.pow(x - deleteX, 2) + Math.pow(y - deleteY, 2));
      if (distance <= 10) {
        this.deleteSelectedElement();
        return;
      }
    }

    if (this.selectedElement !== null && !this.isPinching) {
      const element = this.drawnElements[this.selectedElement];
      if (this.isPointInElement(x, y, element)) {
        this.isMoving = true;
        this.canvas.drawing = true;
        this.totalDragDistance = 0;

        this.redrawCanvas();

        this.tempImageData = this.ctx.getImageData(
          0, 0, this.currentCanvas.width, this.currentCanvas.height
        );

        this.drawSelectionOutline(element);

        switch (element.type) {
          case 'rectangle':
            this.dragOffsetX = x - element.points.startX;
            this.dragOffsetY = y - element.points.startY;
            break;
          case 'circle':
            this.dragOffsetX = x - element.points.centerX;
            this.dragOffsetY = y - element.points.centerY;
            break;
          case 'line':
          case 'dashed':
            this.dragOffsetX = x - element.points.startX;
            this.dragOffsetY = y - element.points.startY;
            break;
          case 'text':
            this.dragOffsetX = x - element.points.x;
            this.dragOffsetY = y - element.points.y;
            break;
          case 'icon':
            this.dragOffsetX = x - element.points.x;
            this.dragOffsetY = y - element.points.y;
            break;
        }

        return;
      }
    }

    let found = false;
    for (let i = this.drawnElements.length - 1; i >= 0; i--) {
      if (this.isPointInElement(x, y, this.drawnElements[i])) {
        this.selectedElement = i;
        found = true;
        break;
      }
    }

    if (!found) {
      this.selectedElement = null;
    }

    this.redrawCanvas();

    if (this.selectedElement !== null) {
      this.drawSelectionOutline(this.drawnElements[this.selectedElement]);
    }
  }


  deleteSelectedElement() {
    if (this.selectedElement === null || !this.ctx || !this.currentCanvas) return;

    this.drawnElements.splice(this.selectedElement, 1);
    this.selectedElement = null;

    this.redrawCanvas();
    this.saveCanvasState();
  }

  moved(ev) {
    ev.preventDefault();
    if (this.isPinching) return;
    if (!this.canvas.drawing || !this.ctx || !this.currentCanvas) return;

    const canvas = this.currentCanvas;
    const rect = canvas.getBoundingClientRect();

    const clientX = 'touches' in ev ? ev.touches[0].clientX : ev.clientX;
    const clientY = 'touches' in ev ? ev.touches[0].clientY : ev.clientY;

    let currentX = (clientX - rect.left);
    let currentY = (clientY - rect.top);

    if (this.canvas.showGrid && this.canvas.selectedTool !== 'brush' && this.canvas.selectedTool !== 'text' && this.canvas.selectedTool !== 'selection' && this.canvas.selectedTool !== 'grid') {
      currentX = Math.round((currentX - this.canvas.gridOffsetX % this.canvas.gridSize) / this.canvas.gridSize) * this.canvas.gridSize + (this.canvas.gridOffsetX % this.canvas.gridSize);
      currentY = Math.round((currentY - this.canvas.gridOffsetY) / this.canvas.gridSize) * this.canvas.gridSize + this.canvas.gridOffsetY;
    }

    this.canvas.currentX = currentX;
    this.canvas.currentY = currentY;

    if (this.canvas.selectedTool === 'grid') {
      const deltaX = currentX - this.canvas.saveX;
      const deltaY = currentY - this.canvas.saveY;

      this.canvas.gridOffsetX += deltaX;
      this.canvas.gridOffsetY += deltaY;

      this.canvas.saveX = currentX;
      this.canvas.saveY = currentY;

      this.createGridOverlay();
      return;
    }

    if (this.canvas.selectedTool === 'selection' && this.isMoving && this.selectedElement !== null && !this.isPinching) {
      this.totalDragDistance += Math.sqrt(
        Math.pow(currentX - this.canvas.saveX, 2) +
        Math.pow(currentY - this.canvas.saveY, 2)
      );

      if (!this.firstDrag) {
        this.ctx.putImageData(this.tempImageData, 0, 0);
        this.firstDrag = true;
      } else {
        this.ctx.putImageData(this.tempImageData, 0, 0);
      }

      this.moveSelectedElement(currentX, currentY);

      this.canvas.saveX = currentX;
      this.canvas.saveY = currentY;
      return;
    }

    const scaledWidth = this.canvas.lineWidth * (this.canvas.scaleFactor || 1);

    if (this.canvas.selectedTool === 'brush' && this.currentStroke) {
      const lastPoint = this.currentStroke.points[this.currentStroke.points.length - 1];
      this.currentStroke.points.push({ x: currentX, y: currentY });

      this.ctx.beginPath();
      this.ctx.strokeStyle = this.currentStroke.color;
      this.ctx.lineWidth = this.currentStroke.lineWidth;
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
      this.ctx.moveTo(lastPoint.x, lastPoint.y);
      this.ctx.lineTo(currentX, currentY);
      this.ctx.stroke();
    } else if (['line', 'rectangle', 'circle', 'dashed'].includes(this.canvas.selectedTool)) {
      if (this.tempImageData) {
        this.ctx.putImageData(this.tempImageData, 0, 0);

        this.ctx.strokeStyle = this.canvas.selectedColor;
        this.ctx.lineWidth = scaledWidth;
        this.ctx.lineCap = 'butt';
        this.ctx.lineJoin = 'miter';

        if (this.canvas.selectedTool === 'line') {
          this.drawLine(this.canvas.startX, this.canvas.startY, currentX, currentY, false);
        } else if (this.canvas.selectedTool === 'dashed') {
          this.drawLine(this.canvas.startX, this.canvas.startY, currentX, currentY, true);
        } else if (this.canvas.selectedTool === 'rectangle') {
          this.drawRectangle(this.canvas.startX, this.canvas.startY, currentX, currentY);
        } else if (this.canvas.selectedTool === 'circle') {
          this.drawCircle(this.canvas.startX, this.canvas.startY, currentX, currentY);
        }
      }
    }

    this.canvas.saveX = currentX;
    this.canvas.saveY = currentY;
  }

  moveSelectedElement(x: number, y: number) {
    if (!this.ctx || this.selectedElement === null || !this.tempImageData) return;
    const element = this.drawnElements[this.selectedElement];
    this.redrawCanvas();
    let newX = x - this.dragOffsetX;
    let newY = y - this.dragOffsetY;
    if (this.canvas.showGrid) {
      newX = Math.round((newX - this.canvas.gridOffsetX % this.canvas.gridSize) / this.canvas.gridSize) * this.canvas.gridSize + (this.canvas.gridOffsetX % this.canvas.gridSize);
      newY = Math.round((newY - this.canvas.gridOffsetY % this.canvas.gridSize) / this.canvas.gridSize) * this.canvas.gridSize + (this.canvas.gridOffsetY % this.canvas.gridSize);
    }
    this.ctx.lineCap = 'butt';
    this.ctx.lineJoin = 'miter';

    let newElement = { ...element };
    this.ctx.globalAlpha = 0.3;
    switch (element.type) {
      case 'rectangle':
        const width = element.points.endX - element.points.startX;
        const height = element.points.endY - element.points.startY;
        newElement = {
          ...element,
          points: {
            startX: newX,
            startY: newY,
            endX: newX + width,
            endY: newY + height
          }
        };
        this.ctx.beginPath();
        this.ctx.strokeStyle = element.color;
        this.ctx.lineWidth = element.lineWidth;
        this.ctx.rect(newX, newY, width, height);
        if (element.fillShape) {
          this.ctx.fillStyle = element.color;
          this.ctx.fill();
        }
        this.ctx.stroke();
        break;
      case 'circle':
        newElement = {
          ...element,
          points: {
            ...element.points,
            centerX: newX,
            centerY: newY
          }
        };
        this.ctx.beginPath();
        this.ctx.strokeStyle = element.color;
        this.ctx.lineWidth = element.lineWidth;
        this.ctx.arc(newX, newY, element.points.radius, 0, 2 * Math.PI);
        if (element.fillShape) {
          this.ctx.fillStyle = element.color;
          this.ctx.fill();
        }
        this.ctx.stroke();
        break;
      case 'line':
      case 'dashed':
        const dx = element.points.endX - element.points.startX;
        const dy = element.points.endY - element.points.startY;
        const newEndX = newX + dx;
        const newEndY = newY + dy;
        newElement = {
          ...element,
          points: {
            startX: newX,
            startY: newY,
            endX: newEndX,
            endY: newEndY
          }
        };
        this.ctx.beginPath();
        this.ctx.strokeStyle = element.color;
        this.ctx.lineWidth = element.lineWidth;
        if (element.type === 'dashed') {
          this.ctx.setLineDash([element.dashLength, element.dashLength]);
        } else {
          this.ctx.setLineDash([]);
        }
        this.ctx.moveTo(newX, newY);
        this.ctx.lineTo(newEndX, newEndY);
        this.ctx.stroke();
        this.ctx.setLineDash([]);
        break;
      case 'text':
        newElement = {
          ...element,
          points: {
            ...element.points,
            x: newX,
            y: newY
          }
        };
        this.ctx.font = `${element.lineWidth}px Arial`;
        this.ctx.fillStyle = element.color;
        this.ctx.fillText(element.points.text, newX, newY);
        break;
      case 'icon':
        newElement = {
          ...element,
          points: {
            ...element.points,
            x: newX,
            y: newY
          }
        };
        this.newPosX = newX;
        this.newPosY = newY;
        const cachedImg = this.iconImageCache[element.src];
        if (cachedImg) {
          this.ctx.drawImage(cachedImg, newX, newY, element.points.width, element.points.height);
        } else if (!this.iconImageCache[element.src]) {
          const iconImg = new Image();
          iconImg.crossOrigin = "anonymous";
          iconImg.onload = () => {
            this.iconImageCache[element.src] = iconImg;
            this.ctx!.drawImage(iconImg, newX, newY, element.points.width, element.points.height);
          };
          iconImg.src = element.src;
        }
        break;
    }
    this.ctx.globalAlpha = 1;
    this.drawSelectionOutline(newElement, newX + this.dragOffsetX, newY + this.dragOffsetY);
  }

  isPointInElement(x: number, y: number, element: any): boolean {
    const buffer = 5;

    switch (element.type) {
      case 'rectangle':
        const minX = Math.min(element.points.startX, element.points.endX) - buffer;
        const maxX = Math.max(element.points.startX, element.points.endX) + buffer;
        const minY = Math.min(element.points.startY, element.points.endY) - buffer;
        const maxY = Math.max(element.points.startY, element.points.endY) + buffer;
        return x >= minX && x <= maxX && y >= minY && y <= maxY;

      case 'circle':
        const distance = Math.sqrt(
          Math.pow(x - element.points.centerX, 2) +
          Math.pow(y - element.points.centerY, 2)
        );
        return distance <= element.points.radius + buffer;

      case 'line':
      case 'dashed':
        const dx = element.points.endX - element.points.startX;
        const dy = element.points.endY - element.points.startY;
        const length = Math.sqrt(dx * dx + dy * dy);
        const nx = dx / length;
        const ny = dy / length;

        const tx = x - element.points.startX;
        const ty = y - element.points.startY;

        const projection = tx * nx + ty * ny;

        if (projection < 0 || projection > length) {
          return false;
        }

        const projX = element.points.startX + projection * nx;
        const projY = element.points.startY + projection * ny;

        const distance2 = Math.sqrt(
          Math.pow(x - projX, 2) + Math.pow(y - projY, 2)
        );

        return distance2 <= element.lineWidth + buffer;

      case 'text':
        return x >= element.points.x - buffer &&
          x <= element.points.x + 100 + buffer &&
          y >= element.points.y - element.lineWidth - buffer &&
          y <= element.points.y + buffer;

      case 'icon':
        return x >= element.points.x - buffer &&
          x <= element.points.x + element.points.width + buffer &&
          y >= element.points.y - buffer &&
          y <= element.points.y + element.points.height + buffer;

      default:
        return false;
    }
  }

  drawSelectionOutline(element: any, currentX: number = 0, currentY: number = 0) {
    if (!this.ctx || !this.currentCanvas || this.selectedElement === null) return;

    this.ctx.save();
    this.ctx.strokeStyle = '#1e88e5';
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([5, 5]);

    switch (element.type) {
      case 'rectangle':
        const startX = element.points.startX;
        const startY = element.points.startY;
        const width = element.points.endX - element.points.startX;
        const height = element.points.endY - element.points.startY;
        const padding = Math.max(5, element.lineWidth)/2;

        this.ctx.strokeRect(startX - padding, startY - padding, width + padding * 2, height + padding * 2);
        this.drawDeleteButton(startX - padding - 10, startY - padding - 10);
        break;

      case 'circle':
        const centerX = element.points.centerX;
        const centerY = element.points.centerY;
        const radius = element.points.radius;
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius + 5, 0, 2 * Math.PI);
        this.ctx.stroke();
        this.drawDeleteButton(centerX - radius - 15, centerY - radius - 15);
        break;

      case 'line':
      case 'dashed':
        const dStartX = element.points.startX;
        const dStartY = element.points.startY;
        const dEndX = element.points.endX;
        const dEndY = element.points.endY;

        this.ctx.beginPath();
        this.ctx.moveTo(dStartX, dStartY);
        this.ctx.lineTo(dEndX, dEndY);
        this.ctx.stroke();

        this.ctx.fillStyle = '#1e88e5';
        this.ctx.beginPath();
        this.ctx.arc(dStartX, dStartY, 5, 0, 2 * Math.PI);
        this.ctx.arc(dEndX, dEndY, 5, 0, 2 * Math.PI);
        this.ctx.fill();

        this.drawDeleteButton(dStartX - 15, dStartY - 15);
        break;

      case 'text':
        const textX = element.points.x;
        const textY = element.points.y;
        const approxWidth = element.points.text.length * (element.lineWidth * 0.6);
        this.ctx.strokeRect(textX - 5, textY - element.lineWidth - 5, approxWidth + 10, element.lineWidth + 10);

        this.drawDeleteButton(textX - 15, textY - element.lineWidth - 15);
        break;

      case 'icon':
        const iconX = element.points.x;
        const iconY = element.points.y;
        const iconWidth = element.points.width;
        const iconHeight = element.points.height;
        this.ctx.strokeRect(iconX - 5, iconY - 5, iconWidth + 10, iconHeight + 10);

        this.drawDeleteButton(iconX - 15, iconY - 15);
        break;
    }

    this.ctx.restore();
  }
  drawDeleteButton(x: number, y: number) {
    if (!this.ctx) return;

    this.ctx.save();
    this.ctx.setLineDash([]);
    this.ctx.fillStyle = 'red';
    this.ctx.beginPath();
    this.ctx.arc(x + 10, y + 10, 10, 0, 2 * Math.PI);
    this.ctx.fill();

    this.ctx.strokeStyle = 'white';
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    this.ctx.moveTo(x + 6, y + 6);
    this.ctx.lineTo(x + 14, y + 14);
    this.ctx.moveTo(x + 14, y + 6);
    this.ctx.lineTo(x + 6, y + 14);
    this.ctx.stroke();
    this.ctx.restore();
  }

  drawBrush(currentX, currentY, width) {
    if (!this.ctx) return;

    this.ctx.lineJoin = 'round';
    this.ctx.strokeStyle = this.canvas.selectedColor;
    this.ctx.lineWidth = width;
    this.ctx.beginPath();
    this.ctx.moveTo(this.canvas.saveX, this.canvas.saveY);
    this.ctx.lineTo(currentX, currentY);
    this.ctx.closePath();
    this.ctx.stroke();

    this.drawnElements.push({
      type: 'brush',
      points: {
        startX: this.canvas.saveX,
        startY: this.canvas.saveY,
        endX: currentX,
        endY: currentY
      },
      color: this.canvas.selectedColor,
      lineWidth: width
    });
  }

  drawLine(startX, startY, endX, endY, dashed = false) {
    if (!this.ctx) return;

    this.ctx.lineCap = 'butt';
    this.ctx.lineJoin = 'miter';

    if (dashed) {
      this.ctx.setLineDash([this.canvas.dashLength, this.canvas.dashLength]);
    } else {
      this.ctx.setLineDash([]);
    }

    this.ctx.beginPath();
    this.ctx.moveTo(startX, startY);
    this.ctx.lineTo(endX, endY);
    this.ctx.stroke();

    this.ctx.setLineDash([]);
  }

  drawRectangle(startX, startY, endX, endY) {
    if (!this.ctx) return;

    const width = endX - startX;
    const height = endY - startY;

    this.ctx.beginPath();
    this.ctx.rect(startX, startY, width, height);

    if (this.canvas.fillShape) {
      this.ctx.fillStyle = this.canvas.selectedColor;
      this.ctx.fill();
    }

    this.ctx.stroke();
  }

  drawCircle(startX, startY, endX, endY) {
    if (!this.ctx) return;

    const radius = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));

    this.ctx.beginPath();
    this.ctx.arc(startX, startY, radius, 0, 2 * Math.PI);

    if (this.canvas.fillShape) {
      this.ctx.fillStyle = this.canvas.selectedColor;
      this.ctx.fill();
    }

    this.ctx.stroke();
  }

  endDrawing() {
    if (!this.canvas.drawing || !this.ctx) return;

    if (this.canvas.selectedTool === 'grid') {
      this.canvas.drawing = false;
      return;
    }

    if (this.canvas.selectedTool === 'selection' && this.isMoving && this.selectedElement !== null && !this.isPinching) {
      const element = this.drawnElements[this.selectedElement];

      if (this.canvas.currentX !== this.canvas.startX || this.canvas.currentY !== this.canvas.startY) {
        let newX = this.canvas.currentX - this.dragOffsetX;
        let newY = this.canvas.currentY - this.dragOffsetY;

        if (this.canvas.showGrid) {
          newX = Math.round((newX - this.canvas.gridOffsetX % this.canvas.gridSize) / this.canvas.gridSize) * this.canvas.gridSize + (this.canvas.gridOffsetX % this.canvas.gridSize);
          newY = Math.round((newY - this.canvas.gridOffsetY % this.canvas.gridSize) / this.canvas.gridSize) * this.canvas.gridSize + (this.canvas.gridOffsetY % this.canvas.gridSize);
        }

        let dx = 0, dy = 0;
        switch (element.type) {
          case 'rectangle':
          case 'line':
          case 'dashed':
            dx = newX - element.points.startX;
            dy = newY - element.points.startY;
            break;
          case 'circle':
            dx = newX - element.points.centerX;
            dy = newY - element.points.centerY;
            break;
          case 'text':
            dx = newX - element.points.x;
            dy = newY - element.points.y;
            break;
          case 'icon':
            dx = newX - element.points.x;
            dy = newY - element.points.y;
            break;
        }

        switch (element.type) {
          case 'rectangle':
            element.points.startX += dx;
            element.points.startY += dy;
            element.points.endX += dx;
            element.points.endY += dy;
            break;
          case 'circle':
            element.points.centerX += dx;
            element.points.centerY += dy;
            break;
          case 'line':
          case 'dashed':
            element.points.startX += dx;
            element.points.startY += dy;
            element.points.endX += dx;
            element.points.endY += dy;
            break;
          case 'text':
            element.points.x += dx;
            element.points.y += dy;
            break;
          case 'icon':
            element.points.x += dx;
            element.points.y += dy;
            break;
        }

        this.redrawCanvas();
        this.saveCanvasState();
      }

      this.isMoving = false;
      this.canvas.drawing = false;
      this.tempImageData = null;
      this.firstDrag = false;
      return;
    }

    if (this.canvas.selectedTool !== 'brush' && this.canvas.selectedTool !== 'text' && this.canvas.selectedTool !== 'selection') {
      const scaledWidth = this.canvas.lineWidth * (this.canvas.scaleFactor || 1);

      if (this.tempImageData != null) {
        this.ctx.putImageData(this.tempImageData, 0, 0);

        this.ctx.strokeStyle = this.canvas.selectedColor;
        this.ctx.lineWidth = scaledWidth;
        this.ctx.lineJoin = 'round';

        if (this.canvas.selectedTool === 'line') {
          this.drawLine(this.canvas.startX, this.canvas.startY, this.canvas.currentX, this.canvas.currentY, false);
          this.drawnElements.push({
            type: 'line',
            points: {
              startX: this.canvas.startX,
              startY: this.canvas.startY,
              endX: this.canvas.currentX,
              endY: this.canvas.currentY
            },
            color: this.canvas.selectedColor,
            lineWidth: scaledWidth
          });
          this.saveCanvasState();
        } else if (this.canvas.selectedTool === 'dashed') {
          this.drawLine(this.canvas.startX, this.canvas.startY, this.canvas.currentX, this.canvas.currentY, true);
          this.drawnElements.push({
            type: 'dashed',
            points: {
              startX: this.canvas.startX,
              startY: this.canvas.startY,
              endX: this.canvas.currentX,
              endY: this.canvas.currentY
            },
            color: this.canvas.selectedColor,
            lineWidth: scaledWidth,
            dashLength: this.canvas.dashLength
          });
          this.saveCanvasState();
        } else if (this.canvas.selectedTool === 'rectangle') {
          this.ctx.lineCap = 'butt';
          this.ctx.lineJoin = 'miter';
          this.drawRectangle(this.canvas.startX, this.canvas.startY, this.canvas.currentX, this.canvas.currentY);
          this.drawnElements.push({
            type: 'rectangle',
            points: {
              startX: this.canvas.startX,
              startY: this.canvas.startY,
              endX: this.canvas.currentX,
              endY: this.canvas.currentY
            },
            color: this.canvas.selectedColor,
            lineWidth: scaledWidth,
            fillShape: this.canvas.fillShape
          });
          this.saveCanvasState();
        } else if (this.canvas.selectedTool === 'circle') {
          this.drawCircle(this.canvas.startX, this.canvas.startY, this.canvas.currentX, this.canvas.currentY);
          const radius = Math.sqrt(
            Math.pow(this.canvas.currentX - this.canvas.startX, 2) +
            Math.pow(this.canvas.currentY - this.canvas.startY, 2)
          );
          this.drawnElements.push({
            type: 'circle',
            points: {
              centerX: this.canvas.startX,
              centerY: this.canvas.startY,
              radius: radius
            },
            color: this.canvas.selectedColor,
            lineWidth: scaledWidth,
            fillShape: this.canvas.fillShape
          });
          this.saveCanvasState();
        }
      }
    } else if (this.canvas.selectedTool === 'brush' && this.currentStroke) {
      this.drawnElements.push(this.currentStroke);
      this.currentStroke = null;
      this.saveCanvasState();
    }

    this.canvas.drawing = false;
    this.tempImageData = null;
    this.firstDrag = false;
  }

  preloadIcons(): Promise<void> {
    if (!this.drawnElements || this.drawnElements.length === 0) {
      return Promise.resolve();
    }

    const iconLoadPromises: Promise<void>[] = [];
    this.drawnElements.forEach(element => {
      if (element.type === 'icon' && element.src) {
        if (!this.iconImageCache[element.src]) {
          const loadPromise = new Promise<void>((resolve) => {
            const iconImg = new Image();
            iconImg.crossOrigin = "anonymous";
            iconImg.onload = () => {
              this.iconImageCache[element.src] = iconImg;
              resolve();
            };
            iconImg.onerror = () => {
              console.error('Failed to load icon:', element.src);
              resolve();
            };
            iconImg.src = element.src;
          });
          iconLoadPromises.push(loadPromise);
        }
      }
    });

    return Promise.all(iconLoadPromises).then(() => { });
  }

  saveDrawing() {
    this.selectedElement = null;
    this.base.is_loading = true;

    this.preloadIcons().then(() => {
      this.redrawCanvas();

      const canvas = document.getElementById('edit-field') as HTMLCanvasElement;
      const finalImage = canvas.toDataURL('image/png', 1.0);

      const base64ToBlob = (base64: string) => {
        const byteCharacters = atob(base64.split(',')[1]);
        const byteArray = new Uint8Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteArray[i] = byteCharacters.charCodeAt(i);
        }
        return new Blob([byteArray], { type: 'image/png' });
      };

      const formData = new FormData();
      formData.append('api_token', localStorage.api_token);
      formData.append('file', base64ToBlob(finalImage), 'image.png');
      formData.append('path', '/Canvas/temp');

      this.infordb.post(this.uploadUrl, formData)
        .then((response) => {
          this.base.is_loading = false;
          if (response.status == 201 || response.status == 200) {
            this.modalCtrl.dismiss(response.data);
          }
        })
        .catch((err) => {
          this.base.is_loading = false;
          alert('Er is iets misgegaan bij het toevoegen van het bestand, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + err);
        });
    });
  }

  close() {
    this.modalCtrl.dismiss();
  }
}