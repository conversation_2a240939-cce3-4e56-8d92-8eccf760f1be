<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="close()">
        <ion-icon name="arrow-back"></ion-icon>
        Terug
      </ion-button>
    </ion-buttons>
    <ion-title>Foto Bewerken</ion-title>
    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading"> 
      <ion-spinner></ion-spinner> 
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="bg-inverse-secondary">
  <div class="d-flex justify-content-end gap-2 px-2 pt-2">
    <ion-button color="danger" fill="clear" (click)="undoLastAction()" [disabled]="!canUndo">
      <ion-icon name="arrow-undo-outline"></ion-icon>
    </ion-button>
    <ion-button color="primary" fill="clear" (click)="clearCanvas()">
      <ion-icon name="refresh-outline"></ion-icon>
    </ion-button>
    <ion-button color="success" fill="solid" (click)="saveDrawing()">
      <ion-icon name="save-outline"></ion-icon>
    </ion-button>
  </div>
  <ion-card class="bg-white p-1">
    <div class="d-flex justify-content-center p-1 position-relative">
      <canvas id="edit-field"
        [width]="canvas.width" [height]="canvas.height"
        class="border rounded bg-transparent w-100"
        (mousedown)="startDrawing($event)" (touchstart)="startDrawing($event)"
        (touchmove)="moved($event)" (mousemove)="moved($event)"
        (mouseup)="endDrawing()" (touchend)="endDrawing()"
        (mouseleave)="endDrawing()">
      </canvas>
      
      <div id="grid-overlay" 
        class="position-absolute top-0 start-0 w-100 h-100"
        [ngClass]="{
          'd-none': !canvas.showGrid,
          'pointer-events-none': canvas.selectedTool !== 'grid'
        }">
      </div>
    </div>
    
    <div class="border-top pt-3 pb-2 px-2">
      <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center">
          <div *ngFor="let tool of toolOptions" 
              class="d-flex justify-content-center align-items-center mx-1 p-1 tool-option border border-1 rounded"
              [ngClass]="{'border-3 border-dark shadow': canvas.selectedTool === tool.value}"
              style="width: 40px; height: 40px; cursor: pointer; display: flex; align-items: center; justify-content: center;"
              (click)="selectTool(tool.value)">
            <ion-icon [name]="tool.icon" size="small"></ion-icon>
          </div>
        </div>
      </div>

      <div *ngIf="canvas.selectedTool === 'icon'" class="mb-3">
        <div class="d-flex flex-wrap justify-content-start gap-2">
          <div *ngFor="let icon of icons"
               (click)="selectIcon(icon.src)"
               class="p-1 border rounded"
               [ngClass]="{ 'border-3 border-primary shadow': selectedIcon === icon.src }"
               style="width: 50px; height: 50px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
            <img [src]="icon.src" alt="icon" style="max-width: 100%; max-height: 100%;" />
          </div>
        </div>
      </div>
      
      <div class="mb-3">
        <div class="d-flex justify-content-between">
          <div *ngFor="let color of colorOptions" 
                class="rounded-circle p-0 mx-1 color-option border border-1 border-black"
                [ngClass]="{'border-3 border-dark shadow': canvas.selectedColor === color}"
                [ngStyle]="{'background-color': color, 'width': '40px', 'height': '40px'}"
                (click)="selectColor(color)">
          </div>
        </div>
      </div>
      
      <div class="d-flex align-items-center mb-3">
        <div class="me-3 border rounded-circle d-flex justify-content-center align-items-center bg-light" style="width: 50px; height: 50px;">
          <canvas id="size-preview" width="50" height="50"></canvas>
        </div>
        <div class="w-100">
          <ion-item lines="none">
            <ion-range color="primary" 
                      min="1" max="20" step="1" 
                      [value]="canvas.lineWidth"
                      (ionChange)="updateLineWidth($event)">
              <ion-label slot="start">1</ion-label>
              <ion-label slot="end">20</ion-label>
            </ion-range>
          </ion-item>
        </div>
      </div>

      <div *ngIf="canvas.selectedTool === 'rectangle' || canvas.selectedTool === 'circle'" class="mb-3">
        <ion-item lines="none">
          <ion-label>Vormen opvullen</ion-label>
          <ion-toggle [(ngModel)]="canvas.fillShape" slot="end"></ion-toggle>
        </ion-item>
      </div>

      <div *ngIf="canvas.selectedTool === 'dashed'" class="mb-3">
        <ion-item lines="none">
          <ion-label class="ion-text-wrap" style="min-width: 130px; white-space: nowrap;">Stippelafstand</ion-label>
          <ion-range color="primary" 
                    min="1" max="20" step="1" 
                    [value]="canvas.dashLength"
                    (ionChange)="updateDashLength($event)">
            <ion-label slot="start">1</ion-label>
            <ion-label slot="end">20</ion-label>
          </ion-range>
        </ion-item>
      </div>

      <div class="mb-3">
        <ion-item lines="none">
          <ion-label>Toon grid</ion-label>
          <ion-toggle [(ngModel)]="canvas.showGrid" (ionChange)="toggleGrid()" slot="end"></ion-toggle>
        </ion-item>
      </div>

      <div *ngIf="canvas.showGrid" class="mb-3">
        <ion-item lines="none">
          <ion-label class="ion-text-wrap" style="min-width: 130px; white-space: nowrap;">Gridgrootte</ion-label>
          <ion-range color="primary" 
                    min="5" max="50" step="5" 
                    [value]="canvas.gridSize"
                    (ionChange)="updateGridSize($event)">
            <ion-label slot="start">1</ion-label>
            <ion-label slot="end">10</ion-label>
          </ion-range>
        </ion-item>
      </div>
    </div>
  </ion-card>
</ion-content>
