export class Account {

    subdomain: string;
    user_id: number;
    first_name: string;
    last_name: string;
    email: string;
    password: string;

    constructor(user: any) {
        this.subdomain = user.subdomain;
        this.user_id = user.user_id;
        this.first_name = user.first_name;
        this.last_name = user.last_name;
        this.email = user.email;
        this.password = user.password;
    }

    isLoggedIn() {
        const current_subdomain = localStorage.subdomain;
        const current_user = JSON.parse(localStorage.user);

        return current_subdomain === this.subdomain && current_user.user_id === this.user_id;
    }


}
