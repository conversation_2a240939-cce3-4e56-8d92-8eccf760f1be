import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {Router} from "@angular/router";

@Component({
  selector: 'app-whatsapp',
  templateUrl: './whatsapp.page.html',
  styleUrls: ['./whatsapp.page.scss'],
})
export class WhatsappPage implements OnInit {

  @ViewChild('iframe') iframeRef: ElementRef;

  public link: SafeResourceUrl;
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));


  constructor(
      private router: Router,
      private sanitizer: DomSanitizer,
  ) { }

  ngOnInit() {
    this.defineLink();
    this.defineListener();
  }

  defineLink(){
    const link = `https://${this.subdomain}.ikbentessa.nl/iframe/whatsapp/app?api_token=${this.user.api_token}`;
    this.link = this.sanitizer.bypassSecurityTrustResourceUrl(link);
  }
  defineListener(){
    const eventMethod = window.addEventListener ? 'addEventListener' : 'attachEvent';
    const eventer = window[eventMethod];
    const messageEvent = eventMethod === 'attachEvent' ? 'onmessage' : 'message';

    eventer(messageEvent, (e) => {
      if(e?.data?.close_whatsapp_iframe){
        this.router.navigate(['/home'])
      }
    });
  }


}
