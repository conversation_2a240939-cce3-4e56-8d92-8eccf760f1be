import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DomSanitizer } from "@angular/platform-browser";

@Component({
  selector: 'app-offertes',
  templateUrl: './offertes.page.html',
  styleUrls: ['./offertes.page.scss'],
})
export class OffertesPage implements OnInit {
	
	public subdomain = window.localStorage.getItem('subdomain');
	public user = JSON.parse(window.localStorage.getItem('user'));
	public link;
	
  constructor(
	  private sanitizer: DomSanitizer,
	  private router: Router,
  ) { }

	ngOnInit() {
		const route = this.tessUrlEncode('/iframe/offertes');
		const link = `https://${this.subdomain}.ikbentessa.nl/force-login/${route}/${this.user.password}`;
		this.link = this.sanitizer.bypassSecurityTrustResourceUrl(link);
		
		window.location.href = link;
		this.router.navigate(['/home'], { skipLocationChange: true });
	}
	
	tessUrlEncode(url){
		url = url.replaceAll('/', '0x736c');
		url = url.replaceAll('?', '0x7175');
		url = url.replaceAll('=', '0x6571');
		return url;
	}
	
}
