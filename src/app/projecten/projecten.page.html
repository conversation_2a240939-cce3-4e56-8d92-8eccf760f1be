<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>

    <ion-title>Projecten</ion-title>
    <span class="pr-4" slot="end" *ngIf="request.loaded && request.total" >{{request.loaded}} / {{request.total}}</span>

  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >

    <ion-card>
      <ion-item>
        <ion-label position="floating">Zoeken</ion-label>
        <ion-input [(ngModel)]="request.search_value" (ionInput)="searchProjecten()" ></ion-input>
      </ion-item>
    </ion-card>

    <ion-card class="my-2">
      <ion-item>
        <ion-label>Status</ion-label>
        <ion-select [(ngModel)]="filter.status" multiple="true" cancelText="Annuleren" okText="Ok" (ionChange)="initProjecten({empty: true})" >
          <ion-select-option *ngFor="let status of statussen" [value]="status" >{{status}}</ion-select-option>
        </ion-select>
      </ion-item>
    </ion-card>

<!--    <ion-card class="my-2" *ngIf="this.user.values['app_project_date_filter']">-->
    <ion-card class="my-2" *ngIf="true">
      <ion-item>
        <ion-label>Datum</ion-label>
        <ion-select [(ngModel)]="filter.datum.type" cancelText="Annuleren" okText="Ok">
          <ion-select-option *ngFor="let type of datumTypes" [value]="type" >{{type == 'created_at' ? 'Aanmaak' : 'Afgerond'}}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item *ngIf="filter.datum.type">
        <ion-label>Van</ion-label>
        <div class="px-2 py-1 hover-shadow rounded" slot="end" (click)="dateSelect.select(filter.datum, 'van')">
          {{filter.datum.van ? convertDate(filter.datum.van) : 'Datum selecteren'}}</div>
      </ion-item>
      <ion-item *ngIf="filter.datum.type">
        <ion-label>Tot</ion-label>
        <div class="px-2 py-1 hover-shadow rounded" slot="end" (click)="dateSelect.select(filter.datum, 'tot')">
          {{filter.datum.tot ? convertDate(filter.datum.tot) : 'Datum selecteren'}}</div>
      </ion-item>
<!--      button to filter on dates-->
      <ion-item *ngIf="filter.datum.type">
        <ion-button fill="clear" class="mx-2" slot="end" (click)="initProjecten({empty: true})">Datums filteren</ion-button>
      </ion-item>
    </ion-card>

    <div *ngIf="!post" class="ion-text-center ion-margin">
      <ion-spinner></ion-spinner>
    </div>

    <div *ngIf="post && !projecten.length" class="ion-text-center ion-margin" >
      <h4>Geen projecten gevonden</h4>
    </div>

    <div *ngIf="projecten.length" >
      <div *ngFor="let row of projecten;let index = index;">
        <ion-card class="bg-white text-dark font-size-1 p-2 my-2">
          <h5 class="d-block ion-text-center m-0"  >{{row.projectnr}}</h5>
          <div *ngIf="fields['projectnaam']" class="ion-text-center text-muted">{{row.projectnaam}}</div>
          <div *ngIf="fields['opdrachtgever']" class="py-1 flex-between border-bottom">
            <div>
              <b class="d-block">{{row.opdrachtgever || ''}}</b>
              <div *ngIf="row.klant" >
                <div class="d-flex ion-align-items-center text-primary" *ngIf="row.klant.telefoonnummer" (click)="call(index, row.klant.telefoonnummer)">
                  <ion-text>{{row.klant.telefoonnummer}}</ion-text>
                  <ion-icon class="mx-1" name="call-outline"></ion-icon>
                </div>
                <ion-text class="d-block">{{row.klant.email || ''}}</ion-text>
              </div>
            </div>
            <div *ngIf="row.klant" >
              <a class="btn btn-inverse-primary" (click)="klant(index)"><ion-icon name="person-outline"></ion-icon></a>
            </div>
          </div>
          <div *ngIf="fields['adres'] && (row.adres || row.postcode || row.woonplaats)" class="py-1 flex-between border-bottom" >
            <div>
              <b class="d-block" >Adres</b>
              <ion-text class="d-block" >{{row.adres}} {{row.huisnummer}}{{row.toevoeging}}</ion-text>
              <ion-text class="d-block" >{{row.postcode}} {{row.woonplaats}}</ion-text>
            </div>
            <a class="btn btn-inverse-primary" (click)="maps(index)"><ion-icon name="map-outline"></ion-icon></a>
          </div>
          <div *ngIf="row.werkbonnen.length" class="py-1 flex-between border-bottom" >
            <div>
              <b class="d-block" >Werkbonnen</b>
              <ion-text class="d-block" >Aantal: {{getGroupedWerkbonnenCount(row)}}</ion-text>
            </div>
              <div>
                  <ion-icon *ngIf="row.groupedWerkbonnen.length" (click)="row.show_werkbonnen_per_template = !row.show_werkbonnen_per_template;" name="chevron-down-outline" class="transition-02 font-size-15 px-2 text-primary"></ion-icon>
              </div>
          </div>
            <div *ngIf="row.show_werkbonnen_per_template">
                <ng-container *ngFor="let templateGroup of row.groupedWerkbonnen">
                    <div *ngIf="templateGroup.template" class="py-1 flex-between border-bottom">
                        <div>
                            <b>{{templateGroup.template.naam}}</b>
                            <ion-text class="d-block">Aantal: {{templateGroup.werkbonnen.length}}</ion-text>
                        </div>
                        <a
                           (click)="projectWerkbonnen(row.id, templateGroup.template_id)"
                           class="btn btn-inverse-primary">
                            <ion-icon name="document-outline"></ion-icon>
                        </a>
                    </div>
                </ng-container>
            </div>

          <div *ngIf="fields['vestiging'] && row.vestiging" class="py-1 border-bottom" >
            <b class="d-block" >Vestiging</b>
            <ion-text class="d-block" >{{row.vestiging.straat}} {{row.vestiging.huisnummer}}{{row.vestiging.toevoeging}}</ion-text>
            <ion-text class="d-block" >{{row.vestiging.postcode}} {{row.vestiging.plaats}}</ion-text>
          </div>
          <div *ngIf="user.values?.projecten_cusotm_rows?.value" (click)="row.show_custom_rows = !row.show_custom_rows;" class="py-1" >
            <div class="flex-between">
              <b>Aanvullende gegevens</b>
              <ion-icon name="chevron-down-outline" class="transition-02 font-size-15 px-2" [ngClass]="row.show_custom_rows ? 'text-danger rotate-180' : 'text-primary'"></ion-icon>
            </div>
            <table class="mx--1 my-1" *ngIf="row.show_custom_rows">
              <tr *ngIf="fields['code']" >
                <th>Code: </th>
                <td>{{row.code}}</td>
              </tr>
              <tr *ngIf="fields['opdrachtnummer']" >
                <th>Opdrachtnummer: </th>
                <td>{{row.opdrachtnummer}}</td>
              </tr>
              <tr *ngIf="fields['taaknummer']" >
                <th>Taaknummer: </th>
                <td>{{row.taaknummer}}</td>
              </tr>
              <tr *ngIf="fields['projectleider']" >
                <th>Projectleider: </th>
                <td>{{row.projectleider}}</td>
              </tr>
              <tr *ngIf="fields['reistijd']" >
                <th>Reis: </th>
                <td>{{row.reis}}</td>
              </tr>
              <tr *ngFor="let custom of row.custom">
                <th *ngIf="custom.type != 'file'">{{ custom.name }}:</th>
                <td *ngIf="custom.keyword !== 'woocommerce_items' && custom.type != 'user' && custom.type != 'date' && custom.type != 'file'">
                  {{ custom.value }}
                </td>
                <td *ngIf="custom.type == 'date'">
                  {{ convertDate(custom.value) }}
                </td>
                <td *ngIf="custom.type == 'user'">
                  {{ getUserName(custom.value) }}
                </td>
                <td *ngIf="custom.keyword === 'woocommerce_items'">
                  <table>
                    <tr *ngFor="let item of custom.list">
                      <td><span class="bg-inverse-secondary rounded px-1 border">{{ item.quantity }}</span></td>
                      <td>{{ item.name }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </div>
          <div *ngIf="row.omschrijving" (click)="row.show_omschrijving = !row.show_omschrijving;" class="py-1">
            <div class="flex-between">
              <b>Omschrijving</b>
              <ion-icon name="chevron-down-outline" class="transition-02 font-size-15 px-2" [ngClass]="row.show_omschrijving ? 'text-danger rotate-180' : 'text-primary'"></ion-icon>
            </div>
            <div *ngIf="row.show_omschrijving" [innerHTML]="row.omschrijving" ></div>
          </div>
          <div *ngIf="row.taken.length" (click)="row.show_taken = !row.show_taken;" class="py-1">
            <div class="flex-between">
              <b>Taken</b>
              <ion-icon name="chevron-down-outline" class="transition-02 font-size-15 px-2" [ngClass]="row.show_taken ? 'text-danger rotate-180' : 'text-primary'"></ion-icon>
            </div>
            <div *ngIf="row.show_taken" >

              <div *ngFor="let taak of row.taken" class="bg-light-grey rounded border p-2 my-2">
                <div class="flex-between">
                  <ion-text [ngClass]="taak.completed === '1' ? 'text-success' : ''" >
                    <span *ngIf="!settings.hide_taken_name" >{{taak.name}}</span>
                  </ion-text>
                  <ion-icon *ngIf="taak.completed === '1'" name="checkmark-done-outline" class="text-success" ></ion-icon>
                  <ion-icon *ngIf="taak.completed !== '1'" name="hourglass-outline" class="text-warning" ></ion-icon>
                  <a class="btn btn-primary" *ngIf="taak.completed !== '1' && this.user.values['app_planning_edit']" (click)="taakInplannen(taak.id, row.id)"><ion-icon name="calendar-number-outline" class="text-white"></ion-icon></a>
                </div>
                <table class="mx--1 my-1 font-size-085" >
                  <tr *ngFor="let custom of taak.custom" >
                    <th class="nobr" >{{custom.name}} :</th>
                    <td>
                      <span *ngIf="custom.type == 'date' && custom.value" >{{convertDate(custom.value)}}</span>
                      <span *ngIf="custom.type == 'checkbox'" class="font-size-125" >
                        <span *ngIf="custom.value === '1'" class="text-success" > <ion-icon name="checkmark-outline"></ion-icon> </span>
                        <span *ngIf="custom.value === '0'" class="text-danger" > <ion-icon name="close-outline"></ion-icon> </span>
                      </span>
                      <span *ngIf="custom.type == 'user' && custom.user" >{{custom.user.name || ''}} {{custom.user.lastname || ''}}</span>
                      <span *ngIf="settings.plain_taken_types[custom.type]">{{custom.value}}</span>
                    </td>
                  </tr>
                </table>
              </div>

            </div>
          </div>
          <div *ngIf="fields['bijlage'] && row.explorer_files.length" class="py-1">
            <div class="flex-between" (click)="row.show_files = !row.show_files" style="cursor: pointer;">
              <b>Bijlage</b>
              <ion-icon name="chevron-down-outline"
                        class="transition-02 font-size-15 px-2"
                        [ngClass]="row.show_files ? 'text-danger rotate-180' : 'text-primary'">
              </ion-icon>
            </div>

            <div class="my-2" *ngIf="row.show_files" (click)="$event.stopPropagation()">
              <ng-container *ngTemplateOutlet="fileTree; context:{ $implicit: row.explorer_files }"></ng-container>
            </div>
          </div>

          <!-- Recursive template -->
          <ng-template #fileTree let-files>
            <div *ngFor="let file of files" class="mb-1">

              <!-- File -->
              <div *ngIf="file.type === 'file'" class="flex-between border rounded p-1 bg-inverse-secondary">
                <div class="d-flex align-items-center">
                  <img [src]="'https://' + subdomain + '.ikbentessa.nl/client/public/img/explorer/files/' + file.icon" height="35" alt="file icon">
                  <div class="ms-2">
                    <div *ngIf="file.view_name">{{ file.view_name }}</div>
                    <div *ngIf="!file.view_name" class="font-size-09">{{ file.name }}</div>
                    <div class="text-muted font-size-085">{{ file.path }}</div>
                  </div>
                </div>
                <ion-button fill="clear" (click)="download(file.src)">
                  <ion-icon name="cloud-download-outline"></ion-icon>
                </ion-button>
              </div>

              <!-- Folder -->
              <div *ngIf="file.type === 'folder'" class="border rounded p-1 bg-inverse-secondary">
                <div class="flex-between" (click)="file.showChildren = !file.showChildren" style="cursor: pointer;">
                  <div class="d-flex align-items-center">
                    <img [src]="'https://' + subdomain + '.ikbentessa.nl/client/public/img/explorer/folder_empty.png'" height="35" alt="folder icon">
                    <div class="ms-2">
                      <div class="font-size-09">{{ file.view_name || file.name }}</div>
                      <div class="text-muted font-size-085">{{ file.path }}</div>
                    </div>
                  </div>
                  <ion-icon name="chevron-down-outline"
                            class="transition-02 font-size-15 px-2"
                            [ngClass]="file.showChildren ? 'text-danger rotate-180' : 'text-primary'">
                  </ion-icon>
                </div>

                <div *ngIf="file.showChildren && file.children?.length" class="pl-3 mt-2">
                  <ng-container *ngTemplateOutlet="fileTree; context:{ $implicit: file.children }"></ng-container>
                </div>
              </div>

            </div>
          </ng-template>
          <div class="ion-text-center py-1">
            <a (click)="edit(index)" class="btn btn-inverse-primary mx-2" *ngIf="hasPermission('Projecten toevoegen')">Wijzigen</a>
            <a (click)="projectChecklists(row.id)" class="btn btn-inverse-primary mx-2" *ngIf="hasPermission('Checklists vanuit projecten aanmaken') && this.user.values['checklist_custom_project_flow']">Checklists</a>
          </div>
        </ion-card>
      </div>

      <ion-infinite-scroll (ionInfinite)="loadProjecten($event)">
        <ion-infinite-scroll-content></ion-infinite-scroll-content>
      </ion-infinite-scroll>

    </div>

  </ion-card>

  <ion-fab *ngIf="user.user_permissions['Projecten toevoegen']" vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button (click)="callPage('projecten/create')" color="primary" class="ion-margin" >
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>

</ion-content>

<div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;">
  <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()">

    <div class="flex-between my-1 mx--2">
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)"><ion-icon
        name="chevron-back-outline"></ion-icon></ion-button>
      <span class="mx-2">{{dateSelect.vars.date.year}}</span>
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)"><ion-icon
        name="chevron-forward-outline"></ion-icon></ion-button>
    </div>

    <div class="flex-between my-1 mx--2">
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)"><ion-icon
        name="chevron-back-outline"></ion-icon></ion-button>
      <span class="mx-2">{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)"><ion-icon
        name="chevron-forward-outline"></ion-icon></ion-button>
    </div>

    <div class="flex-between custom-date-picker-days">
      <div>ma</div>
      <div>di</div>
      <div>wo</div>
      <div>do</div>
      <div>vr</div>
      <div>za</div>
      <div>zo</div>
    </div>
    <div class="custom-date-picker">
      <div *ngFor="let day of dateSelect.vars.completeDays" [ngClass]="{
          'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
          'prev-month': day.type === 'prev',
          'current-month': day.type === 'current',
          'next-month': day.type === 'next'
        }" (click)="dateSelect.confirmDate(day)">
        {{ day.day }}
      </div>
    </div>

  </ion-card>
</div>
