import { Component } from '@angular/core';
import { Router } from '@angular/router';
import {dateSelect, Infordb} from 'infordb';
import axios from "axios";

@Component({
  selector: 'app-projecten',
  templateUrl: './projecten.page.html',
  styleUrls: ['./projecten.page.scss'],
  providers: [Infordb]
})
export class ProjectenPage{

  public dateSelect = dateSelect

  public user = JSON.parse(localStorage.user);
  public users = [];
  public subdomain = localStorage.subdomain;

  public post = false;
  public werkbonnenTemplates = [];
  public projecten = [];
  public statussen = ['Acquisitie', 'Opdracht', 'Afgerond'];
  public datumTypes = ['created_at', 'completion_date'];
  public fields = this.user.projecten.fields;
  public settings = {
    hide_taken_name: false,
    plain_taken_types: {
      text: true,
      number: true,
      time: true,
      select: true,
    }
  }
	public request = {
		page: 1,
		loaded: null,
		total: null,
		search_ids: [],
		search_value: '',
	}
	public cancel_tokens = {
		get: null,
		search: null,
	}
  public filter = {
    status: null,
    datum: {
      type: null,
      van: null,
      tot: null,
    }
  }

  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  async ionViewWillEnter(){
    this.post = false;
    this.initSettings();
    await this.getWerkbonnenTemplates();
    this.initProjecten({empty: true});

    this.getUsers();
  }

  initSettings(){
    if(this.user.values['projecten_taken_hide_name'] && this.user.values['projecten_taken_hide_name'].value == 'ja'){
      this.settings.hide_taken_name = true;
    }
  }
	async initProjecten(options: any = {}){
		try{
      if(options?.empty){
        this.projecten = [];
        this.request.page = 1;
      }
			this.post = false;

      const excludedStatussen = JSON.parse(this.user.values['projecten_app_exclude_statussen']?.value || '[]');
      this.filter.status = this.filter.status ?? this.statussen.filter(status => !excludedStatussen.includes(status));

			//Set post data
			const data: any = {
				user: this.user.user_id.toString(),
				status: this.filter.status,
				paginate: 25,
				page: this.request.page,
                sort_type: this.user.values['project_app_sort_type']?.value || 'asc',
                relations: [ 'checklists.template', 'taken', 'contactpersoon', 'custom', 'vestiging', 'werkbonnen', 'planning', 'klant', ],
			};
			if(this.request.search_ids.length) {
				data.ids = this.request.search_ids;
			}
      if(this.filter.datum.type === 'created_at'){
        data.start = this.filter.datum.van ? this.filter.datum.van : null;
        data.end = this.filter.datum.tot ? this.filter.datum.tot : null;
      }else if(this.filter.datum.type === 'completion_date'){
        data.completed_start = this.filter.datum.van ? this.filter.datum.van : null;
        data.completed_end = this.filter.datum.tot ? this.filter.datum.tot : null;
      }

			//Generate cancel token
			if(this.cancel_tokens.get){
				this.cancel_tokens.get.cancel();
			}
			this.cancel_tokens.get = axios.CancelToken.source();

			//Request
			const response = await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/projecten/get`, data, { cancelToken: this.cancel_tokens.get.token});
			this.cancel_tokens.get = null;

			if(response.status !== 201){ throw response; }

			let { all_ids, projecten } = response.data;

            const projectsWithGroupedWerkbonnen = projecten.map(project => {
                const werkbonnenByTemplateId = {};

                (project.werkbonnen || []).forEach(werkbon => {
                    const { template_id } = werkbon;
                    if (!werkbonnenByTemplateId[template_id]) {
                        werkbonnenByTemplateId[template_id] = [];
                    }
                    werkbonnenByTemplateId[template_id].push(werkbon);
                });

                const groupedAsArray = this.werkbonnenTemplates.map(template => {
                    const template_id = template.id;
                    return {
                        template_id,
                        werkbonnen: werkbonnenByTemplateId[template_id] || [],
                        template
                    };
                });

                return {
                    ...project,
                    groupedWerkbonnen: groupedAsArray,
                    show_werkbonnen_per_template: false
                };
            });

            this.projecten = this.projecten.concat(projectsWithGroupedWerkbonnen);
            this.post = true;
			this.request.loaded = this.projecten.length;
			this.request.total = all_ids.length;
			this.afterPost();

			if(options?.event){ options.event.target.complete(); }
		}
		catch (e) {
			this.post = false;
			this.infordb.handleError(e);

			if(options?.event){ options.event.target.complete(); }
		}
	}
    async getWerkbonnenTemplates(){
        try {
            const response = await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/werkbonnen/gettemplates`, {
                options: {
                    where: { menu_item: false }
                }
            });
            this.werkbonnenTemplates = response.data.werkbonnenTemplates;
        }catch (e){this.infordb.handleError(e)}
    }
    getGroupedWerkbonnenCount(row){
        return (row.groupedWerkbonnen || [])
            .filter(group => group.template && group.werkbonnen?.length)
            .reduce((sum, group) => sum + group.werkbonnen.length, 0);
    }

  async getUsers(){
    try {
      const response = await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/users/get`)
      this.users = response.data.users;
    }catch (e){this.infordb.handleError(e)}
  }

	loadProjecten(event){
		this.request.page++;
		this.initProjecten({event: event});
	}
	async searchProjecten(){
		try{
			this.post = false;
			this.projecten = [];
			this.request.search_ids = [];

			//Cancel ongoing requests
			if(this.cancel_tokens.get){
				this.cancel_tokens.get.cancel();
			}
			if(this.cancel_tokens.search){
				this.cancel_tokens.search.cancel();
			}

			if(this.request.search_value){

				//Generate cancel token
				this.cancel_tokens.search = axios.CancelToken.source();

				const { data } = await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/projecten/search`, {search: this.request.search_value}, { cancelToken: this.cancel_tokens.search.token });
				this.cancel_tokens.search = null;
				this.request.search_ids = data.map(project => project.id);
			}

			this.request.page = 1;
      this.projecten = [];
			this.initProjecten({empty: true});
		}
		catch (e) {
			this.infordb.handleError(e);
		}
	}

  afterPost(){
    for(const project of this.projecten){
			if(project._loaded){ continue; }

			const custom = [];

      project.files = JSON.parse(project.files || '[]');
      for(const r in project.custom){
        const row = project.custom[r];
        if(row.type == 'data'){project.custom[r].value = this.convertDate(row.value);}
        if(row.keyword == 'woocommerce_items'){project.custom[r].list = JSON.parse(row.value || '[]');}
				custom.push(row);
      }

	    project._loaded = true;
			project.custom = custom;
    }
  }

  edit(index){
    localStorage.project_edit = JSON.stringify(this.projecten[index]);
    this.callPage('projecten/create');
  }
  maps(i){
    const project = this.projecten[i];
    const adres = `${project.adres || ''} ${project.huisnummer || ''}${project.toevoeging || ''} ${project.postcode || ''} ${project.woonplaats || ''}`;

	  window.location.href = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(adres)}`;
  }
  call(index, nummer){
    const project = this.projecten[index];
    const name = project.klant.naam || (project.klant.contactpersoon_voornaam || '')+' '+(project.klant.contactpersoon_achternaam || '')
    if(confirm(`Wilt u ${name} bellen?`)){
			window.location.href = `tel: ${nummer}`;
		}
  }
  klant(index){
    const project = this.projecten[index];
    localStorage.klantId = project.klant_id;
    this.callPage('klant');
  }

  download(src){
    const link = 'https://' + this.subdomain + '.ikbentessa.nl/api/file/explorer/files/' + src;
    window.open(link);
  }
  callPage(page){
    this.router.navigate([`/${page.toLowerCase()}`]);
  }
  convertDate(date){
    const datum = new Date(date);
    return ('0' + datum.getDate()).slice(-2) + '-' + ('0' + (datum.getMonth() + 1)).slice(-2) + '-' + datum.getFullYear();
  }

  hasPermission(permissionName: string): boolean {
    var found = false;
    this.user.permissions.forEach(permission => {
      if (permission.permission == permissionName) {
        found = true;
      }
    });
    return found;
  }

  projectWerkbonnen(project_id, template_id = null){
    localStorage.setItem('selectedProject', project_id);
    localStorage.setItem('selectedProjectTemplateId', template_id);
    localStorage.setItem('preselectedTemplateId', template_id);

    this.callPage('werkbonnen');
  }

  getUserName(userId) {
    const user = this.users.find(user => user.id == userId);
    return user ? `${user.name} ${user.lastname}` : '';
  }

  taakInplannen(taak_id, project_id){
    const today = new Date();
    const date = `${today.getFullYear()}/${today.getMonth() + 1}-${today.getDate()}`;
    localStorage.iframeRoute = `iframe/planning/overzicht/dag/${date}?taakinplannen=${taak_id}&project=${project_id}`;
    this.callPage('iframe');
  }


  projectChecklists(project_id, template_id = null){
    localStorage.setItem('checklistProject', project_id);
    localStorage.setItem('checklistProjectTemplateId', template_id);
    this.callPage('checklists');
  }
}
