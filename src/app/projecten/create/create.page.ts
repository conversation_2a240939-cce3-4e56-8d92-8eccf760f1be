import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { timeSelect, dateSelect, Infordb } from 'infordb';

@Component({
  selector: 'app-create',
  templateUrl: './create.page.html',
  styleUrls: ['./create.page.scss'],
	providers: [Infordb]

})
export class CreatePage implements OnInit {

	public timeSelect = timeSelect
	public dateSelect = dateSelect

	public posting = false;

  public user = JSON.parse(localStorage.user);
  public subdomain = localStorage.subdomain;
  public fields = this.user.projecten.fields;
  public searchValue = '';
  public hiddenCustom = {
    'woocommerce_items': true,
  }

  public klanten = [];

  public klantenModal = {
    show: false,
    search: '',
    posting: false,
  };

  public project = {
    id: null,
    bv: '',
    code: '',
    active: '',
    klant: null,
    opdrachtgever: '',
    klant_id: '',
    contactpersoon_id: null,
    vestiging_id: null,
    vestiging: null,
    manager_id: null,
    projectnr: '',
    projectnaam: '',
    adres: '',
    huisnummer: '',
    toevoeging: '',
    postcode: '',
    woonplaats: '',
    projectleider_id: null,
    reis: 0,
    omschrijving: '',
    opdrachtnummer: '',
    taaknummer: '',
    files: [],
    custom: [],
    gefactureerd: "0",
    planning_color: null,

    aanvraagnummer: '',
    auto_nummer: false,
  };

  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
    this.projectInit();
    this.settingsInit()
  }

  onSubmit(){
    if(!this.project.bv){
      alert('BV is verplicht');
      return;
    }
    if(!this.project.auto_nummer && !this.project.projectnr){
      alert('Projectnummer is verplicht');
      return;
    }

    this.posting = true;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/projecten/store`, {
	    project: JSON.stringify(this.project),
    })
      .then(response => {
        this.posting = false;
        this.infordb.notification({message: 'Project opgeslagen!'});
        this.callPage('projecten');
      })
      .catch(err => {
        this.posting = false;
        this.infordb.handleError(err);
      });
  }
  settingsInit(){
    if(this.user.values['automatisch_projectnummer'] && this.user.values['automatisch_projectnummer'].value == 'Ja'){
      this.project.auto_nummer = true;
    }
  }

  projectInit(){
    if(localStorage.project_edit){
      const project = JSON.parse(localStorage.project_edit);
      project.taken = null;
      project.completed_taken = null;
      project.auto_nummer = false;
      this.project = project;
      localStorage.removeItem('project_edit');
      console.log(this.project);
    }

    if(this.user.values['projecten_cusotm_rows']){
      const custom: any = Object.values(JSON.parse(this.user.values['projecten_cusotm_rows'].value || '[]'))

      //Loop through custom rows from settings and check if not added to the project yet
      sLoop: for(const row of custom){
        for(const c of this.project.custom){
          if(c.keyword === row.keyword){ continue sLoop; }
        }
        this.project.custom.push(row);
      }

    }

    if(localStorage.project_aanvraag){
      const {aanvraagnummer, vestiging_id, bv, omschrijving, klant, contactpersoon, user, values, locatie} = JSON.parse(localStorage.project_aanvraag);
      console.log(klant);
      this.project.projectnr = aanvraagnummer;
      this.project.aanvraagnummer = aanvraagnummer;
      this.project.vestiging_id = vestiging_id;
      this.project.bv = bv;


      if(omschrijving){
        this.project.omschrijving = this.htmlToText(omschrijving || '');
      }
      if(klant){
        const {naam, contactpersoon_voornaam, contactpersoon_achternaam, id} = klant
        this.project.klant = klant;
        this.project.klant_id = id;
        this.project.opdrachtgever = naam ? naam : contactpersoon_voornaam+' '+contactpersoon_achternaam;

        if(contactpersoon){
          this.project.contactpersoon_id = `${contactpersoon.id}`;
        }
        if(user){
          this.project.projectleider_id = user.id;
        }

        const {straat, huisnummer, toevoeging, postcode, plaats} = locatie ? locatie : klant;
        this.project.adres = straat;
        this.project.huisnummer = huisnummer || '';
        this.project.toevoeging = toevoeging || '';
        this.project.postcode = postcode || '';
        this.project.woonplaats = plaats || '';
      }

      valuesLoop: for(const v of values){
        for(const i in this.project.custom){
          const c = this.project.custom[i]
          if(v.naam == c.name){
            let value = v.value;

            if(c.type == 'time'){ value = this.timeToIso(v.value); }
            if(c.type == 'date'){ value = this.dateToIso(v.value); }
            this.project.custom[i].value = value;
            continue valuesLoop;
          }
        }
      }
      localStorage.removeItem('project_aanvraag');
    }

  }

  setVestiging(){
    if(!this.project.vestiging_id){return};

    this.project.manager_id = null;
    this.project.vestiging = this.user.vestigingen.find(row => row.id == this.project.vestiging_id);
  }

  selectKlant(index){
    const klant = this.klanten[index];
    const {id, naam, contactpersoon_voornaam, contactpersoon_achternaam} = klant;

    this.project.klant_id = id;
    this.project.opdrachtgever = naam || `${contactpersoon_voornaam || ''} ${contactpersoon_achternaam || ''}`;
    this.project.klant = klant;

    this.klantenModal.show = false;
  }
  findKlanten(){
    this.klantenModal.posting = true;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/search`, {
	    search: this.klantenModal.search,
	    hash: this.user.password,
    })
      .then((response) => {
        this.klantenModal.posting = false;
        if (response.status === 200) {
          this.klanten = response.data;
          for(const klant of this.klanten){
            klant.title = (klant.naam ? klant.naam+', ' : '') + (klant.contactpersoon_voornaam || '')+' '+(klant.contactpersoon_achternaam || '');
          }
        }
      })
      .catch(err => {
        this.klantenModal.posting = false;
        this.infordb.handleError(err);
      });

  }

  timeToIso(time: string) {
    const d = new Date();
    const t = time.split(':');
    const dateString = d.getFullYear() + '-' + (`0${d.getMonth() + 1}`).slice(-2) + '-' + (`0${d.getDate()}`).slice(-2);
    return `${dateString}T${t[0]}:${t[1]}:00.000+02:00`;
  }
  dateToIso(date: string) {
    return `${date}T00:00:00.000+02:00`;
  }
  htmlToText(html){
    const tempDivElement = document.createElement('div');
    tempDivElement.innerHTML = html;
    return tempDivElement.textContent || tempDivElement.innerText || '';
  }
  getLocation(){
    if(!this.project.postcode || this.project.postcode.length < 6){return false;}

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/location`, {
	    postcode: this.project.postcode.toString(),
    })
      .then(response => {
				const { data, status } = response.data;

        if(!status || !data){throw response;}

        this.project.adres = data.straat;
        this.project.woonplaats = data.plaats;
      });
  }
  hasModule(mod){
    for(const module of this.user.user_modules){
      if(module.name === mod){return true;}
    }
    return false;
  }
  callPage(page){
    this.router.navigate([`/${page.toLowerCase()}`]);
  }

}
