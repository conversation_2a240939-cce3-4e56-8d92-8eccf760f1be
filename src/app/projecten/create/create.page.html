<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>{{project.projectnr}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="bg-inverse-secondary m-h-100 py-2" >

    <section class="mb-2" >

      <ion-item>
        <ion-label position="floating" >BV</ion-label>
        <ion-select [(ngModel)]="project.bv" >
          <ion-select-option *ngFor="let bv of user.bvs" value="{{bv.id}}" >{{bv.name}}</ion-select-option>
        </ion-select>
      </ion-item>
      <div class="d-flex bg-white">
        <ion-item class="w-100" >
          <ion-label position="floating" >{{project.auto_nummer ? 'Automatisch projectnummer' : 'Projectnummer'}}</ion-label>
          <ion-input [disabled]="project.auto_nummer" [(ngModel)]="project.projectnr" ></ion-input>
        </ion-item>
        <div class="border-bottom flex-between pr-2" >
          <ion-button (click)="project.auto_nummer = !project.auto_nummer" fill="clear" [ngClass]="project.auto_nummer ? 'text-danger': 'text-success'" ><ion-icon name="menu-outline"></ion-icon></ion-button>
        </div>
      </div>
      <div *ngIf="fields['opdrachtgever']" class="bg-white" >
        <ion-item *ngIf="!hasModule('Klanten')" >
          <ion-label position="floating" >Opdrachtgever</ion-label>
          <ion-input [(ngModel)]="project.opdrachtgever" ></ion-input>
        </ion-item>
        <div class="px-2 flex-between w-100 border-bottom" *ngIf="hasModule('Klanten')" >
          <ion-text class="text-input">Opdrachtgever</ion-text>
          <ion-text (click)="klantenModal.show = true;" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="!project.opdrachtgever" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="project.opdrachtgever" >{{project.opdrachtgever}}</span>
            </ion-button>
          </ion-text>
        </div>
      </div>
      <ion-item *ngIf="project.klant" >
        <ion-label position="floating" >Contactpersoon</ion-label>
        <ion-select [(ngModel)]="project.contactpersoon_id" [disabled]="(!project.klant)">
          <ion-select-option value="" *ngIf="project.klant.contactpersoon_voornaam || project.klant.contactpersoon_voornaam" >{{project.klant.contactpersoon_voornaam}} {{project.klant.contactpersoon_achternaam}}</ion-select-option>
          <ion-select-option value="{{cp.id}}" *ngFor="let cp of project.klant.contactpersonen" >{{cp.voornaam}} {{cp.achternaam}}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item *ngIf="fields['projectleider']" >
        <ion-label position="floating" >Projectleider</ion-label>
        <ion-select [(ngModel)]="project.projectleider_id" >
          <ion-select-option *ngFor="let user of user.users" value="{{user.id}}" >{{user.name}} {{user.lastname}}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item *ngIf="fields['vestiging']" >
        <ion-label position="floating" >Vestiging</ion-label>
        <ion-select [(ngModel)]="project.vestiging_id" (ionChange)="setVestiging()" >
          <ion-select-option *ngFor="let vestiging of user.vestigingen" value="{{vestiging.id}}" >{{vestiging.plaats}}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item *ngIf="project.vestiging && project.vestiging.managers.length">
        <ion-label position="floating" >Vestigingsmanager</ion-label>
        <ion-select [(ngModel)]="project.manager_id">
          <ion-select-option *ngFor="let manager of project.vestiging.managers" value="{{manager.id}}" >{{manager.name}} {{manager.lastname}}</ion-select-option>
        </ion-select>
      </ion-item>
      <div *ngIf="project.aanvraagnummer" class="bg-white" >
        <div class="p-2 flex-between w-100 border-bottom" *ngIf="hasModule('Klanten')" >
          <ion-text class="text-input">Aanvraag</ion-text>
          <ion-text class="ion-text-right text-primary" >{{project.aanvraagnummer}}</ion-text>
        </div>
      </div>

    </section>

    <section class="mb-2">
      <div *ngIf="fields['adres']" >
        <ion-item>
          <ion-label position="floating" >Postcode</ion-label>
          <ion-input [(ngModel)]="project.postcode" (change)="getLocation()" ></ion-input>
        </ion-item>
        <ion-item  >
          <ion-label position="floating" >Huisnummer</ion-label>
          <ion-input [(ngModel)]="project.huisnummer" ></ion-input>
        </ion-item>
        <ion-item  >
          <ion-label position="floating" >Toevoeging</ion-label>
          <ion-input [(ngModel)]="project.toevoeging" ></ion-input>
        </ion-item>
        <ion-item  >
          <ion-label position="floating" >Straat</ion-label>
          <ion-input [(ngModel)]="project.adres" ></ion-input>
        </ion-item>
        <ion-item  >
          <ion-label position="floating" >Plaats</ion-label>
          <ion-input [(ngModel)]="project.woonplaats" ></ion-input>
        </ion-item>
      </div>
      <ion-item *ngIf="fields['reistijd']" >
        <ion-label position="floating" >Reis</ion-label>
        <ion-input [(ngModel)]="project.reis" ></ion-input>
      </ion-item>
    </section>

    <section class="mb-2" >
      <ion-item *ngIf="fields['code']" >
        <ion-label position="floating" >Code</ion-label>
        <ion-input [(ngModel)]="project.code" ></ion-input>
      </ion-item>
      <ion-item *ngIf="fields['projectnaam']" >
        <ion-label position="floating" >Projectnaam</ion-label>
        <ion-input [(ngModel)]="project.projectnaam" ></ion-input>
      </ion-item>
      <ion-item *ngIf="fields['opdrachtnummer']" >
        <ion-label position="floating" >Opdrachtnummer</ion-label>
        <ion-input [(ngModel)]="project.opdrachtnummer" ></ion-input>
      </ion-item>
      <ion-item *ngIf="fields['taaknummer']" >
        <ion-label position="floating">Taaknummer</ion-label>
        <ion-input [(ngModel)]="project.taaknummer" ></ion-input>
      </ion-item>
      <div *ngFor="let row of this.project.custom" >
        <div [ngClass]="hiddenCustom[row.keyword] ? 'd-none' : ''" >
          <ion-item *ngIf="row.type == 'text' || row.type == 'number'" >
            <ion-label position="floating">{{row.name}}</ion-label>
            <ion-input [type]="row.type" [(ngModel)]="row.value" ></ion-input>
          </ion-item>
          <ion-item *ngIf="row.type == 'date'">
            <ion-label>{{row.name}}</ion-label>
            <ion-button fill="clear" (click)="dateSelect.select(row, 'value')" >{{row.value || 'Datum selecteren'}}</ion-button>
          </ion-item>
          <ion-item *ngIf="row.type == 'time'">
            <ion-label>{{row.name}}</ion-label>
            <ion-button fill="clear" (click)="timeSelect.select(row, 'value')" >{{row.value || 'Tijd selecteren'}}</ion-button>
          </ion-item>
        </div>
      </div>
    </section>

    <section class="mb-2">
      <ion-item>
        <ion-label position="floating" >Omschrijving</ion-label>
        <ion-textarea [autoGrow]="true" [(ngModel)]="project.omschrijving" ></ion-textarea>
      </ion-item>
    </section>

    <div class="my-2 ion-text-center">
      <a class="btn btn-success" (click)="onSubmit()" *ngIf="!posting" >{{project.id ? 'Project wijzigen' : 'Project opslaan'}}</a>
      <ion-spinner color="success" *ngIf="posting" ></ion-spinner>
    </div>

  </div>


  <div class="modal-container" *ngIf="timeSelect.modal" (click)="timeSelect.modal = false;" >
    <ion-card class="p-2 text-dark" style="width: 90vw; max-width: 750px;" (click)="$event.stopPropagation()" >
      <div class="d-flex ion-justify-content-around font-size-125">

        <div class="flex-between w-100" >
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

        <!--      Hours-->
        <div (scroll)="timeSelect.scrollTime('hour')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-hour" >
          <div style="height: 39px" ></div>
          <div class="ion-text-center my-4" id="custom-time-hour-{{h}}" *ngFor="let h of timeSelect.vars.hours" (click)="timeSelect.selectTimeValue('hour', h)" >
            <span class="{{timeSelect.vars.time.hour == h ? 'text-primary' : ''}}" >{{('0'+h).slice(-2)}}</span>
          </div>
          <div style="height: 39px" ></div>
        </div>

        <div class="flex-between w-50" >
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

        <!--      Minutes-->
        <div (scroll)="timeSelect.scrollTime('min')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-min" >
          <div style="height: 39px" ></div>
          <div class="ion-text-center my-4" id="custom-time-min-{{m}}" *ngFor="let m of timeSelect.vars.minutes" (click)="timeSelect.selectTimeValue('min', m)">
            <span class="{{timeSelect.vars.time.min == m ? 'text-primary' : ''}}" >{{('0'+m).slice(-2)}}</span>
          </div>
          <div style="height: 39px" ></div>
        </div>

        <div class="flex-between w-100" >
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

      </div>
      <div class="mt-1 pt-1 ion-text-right border-top" >
        <ion-button fill="clear" (click)="timeSelect.confirmTime()" >OK</ion-button>
      </div>
    </ion-card>
  </div>
  <div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;" >
    <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()" >

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)" ><ion-icon name="chevron-back-outline"></ion-icon></ion-button>
        <span class="mx-2" >{{dateSelect.vars.date.year}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
      </div>

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)" ><ion-icon name="chevron-back-outline"></ion-icon></ion-button>
        <span class="mx-2" >{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
      </div>

      <div class="flex-between custom-date-picker-days">
        <div>ma</div>
        <div>di</div>
        <div>wo</div>
        <div>do</div>
        <div>vr</div>
        <div>za</div>
        <div>zo</div>
      </div>
      <div class="custom-date-picker">
        <div
          *ngFor="let day of dateSelect.vars.completeDays"
          [ngClass]="{
            'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
            'prev-month': day.type === 'prev',
            'current-month': day.type === 'current',
            'next-month': day.type === 'next'
          }"
          (click)="dateSelect.confirmDate(day)">
          {{ day.day }}
        </div>
      </div>

    </ion-card>
  </div>

</ion-content>

<div class="modal-container" *ngIf="klantenModal.show" (click)="klantenModal.show = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-item class="w-100 ml--2" lines="none">
        <ion-label position="floating" >Zoeken</ion-label>
        <ion-input [(ngModel)]="klantenModal.search" class="ion-text-left" ></ion-input>
      </ion-item>
      <ion-button fill="clear" (click)="findKlanten()" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
    </ion-item>

    <div class="ion-padding overflow-auto mh-33-vh" *ngIf="!klantenModal.posting">
      <div *ngFor="let klant of klanten; let index = index">
        <div class="ion-text-center"  >
          <ion-button (click)="selectKlant(index)" fill="clear" >
            <div>
              <div>{{klant.title}}</div>
              <small class="opacity-50">{{klant.email}}</small>
            </div>
          </ion-button>
        </div>
      </div>
    </div>
    <div class="p-2 ion-text-center" *ngIf="klantenModal.posting" >
      <ion-spinner></ion-spinner>
    </div>

  </ion-card>
</div>
