<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button (click)="localStore()" text="Terug" defaultHref="/urenregistratie"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ date }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="bg-inverse-secondary" >
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >

    <div class="m-3 p-2 ion-text-center rounded text-white {{parseInt(correctie.active) ? 'bg-warning' : 'bg-success'}}" *ngIf="correctie" >
      <div class="font-size-11">Correctie {{parseInt(correctie.active) ? 'Aangevraagd' : 'doorgevoerd'}}: </div>
      <div class="mt-2">{{correctie.message}}</div>
    </div>

    <div class="my-4 ion-text-center" *ngIf="(readonly || !edit) && !correctie" >
      <a [href]="'/correctie'" *ngIf="readonly && !edit" class="btn btn-inverse-warning">Correctie aanvragen</a>
      <a (click)="editForm()" *ngIf="edit" class="btn btn-inverse-primary">wijzigen</a>
    </div>


    <!--Loader-->
    <section *ngIf="posting" >
      <div class="my-2 ion-text-center" >
        <ion-spinner></ion-spinner>
      </div>
    </section>

    <!--Content-->
    <section *ngIf="!posting" >
      <div *ngFor="let project of projecten; let index = index" class="my-2 bg-white" >
        <div class="d-flex ion-justify-content-between ion-align-items-center text-dark pl-2 pt-2" >
          <div>
            <b>Blok {{ index + 1 }} </b>
            <span *ngIf="project.label">{{project.label}}</span>
          </div>
          <ion-button *ngIf="!readonly" color="danger" fill="clear" (click)="removeProject(index)"><ion-icon name="close-outline"></ion-icon></ion-button>
        </div>

        <div class="flex-between border-bottom pl-2">
          <ion-text class="text-input" [ngClass]="readonly ? 'opacity-25' : ''" *ngIf="!user.values['urenregistratie_invoeren_projectweergave'] || user.values['urenregistratie_invoeren_projectweergave'].value == 'nummer'">Projectnummer:</ion-text>
          <ion-text class="text-input" [ngClass]="readonly ? 'opacity-25' : ''" *ngIf="user.values['urenregistratie_invoeren_projectweergave'] && user.values['urenregistratie_invoeren_projectweergave'].value == 'naam'">Projectnaam:</ion-text>
          <ion-text (click)="projectModalOpen(index)" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="!project.projectnummer" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="(!user.values['urenregistratie_invoeren_projectweergave'] || user.values['urenregistratie_invoeren_projectweergave'].value == 'nummer') && project.projectnummer" >{{project.projectnummer}}</span>
              <span *ngIf="user.values['urenregistratie_invoeren_projectweergave'] && user.values['urenregistratie_invoeren_projectweergave'].value == 'naam'" >{{project.projectnaam || 'Geen project'}}, {{project.opdrachtgever || '-'}}</span>
            </ion-button>
          </ion-text>
        </div>

        <div *ngIf="user.values['urenregistratie_taken'] && project._taken?.length" class="border-bottom" >
          <div class="p-2 font-size-1 text-black" >
            <div *ngFor="let taak of project._taken" >
              <div class="flex-between my-2" *ngIf="!readonly || taak._checked" >
                <div [ngClass]="readonly ? 'opacity-25' : ''" > <ion-label  [ngClass]="!taak._checked ? 'text-muted' : ''" >{{taak.name}}</ion-label> </div>
                <ion-checkbox class="mx-2" [disabled]="readonly" [(ngModel)]="taak._checked" ></ion-checkbox>
              </div>
            </div>
          </div>
        </div>

        <div *ngIf="user.values['urenregistratie_invoeren_uursoorten'] && (user.values['urenregistratie_invoeren_uursoorten'].value == 'aan' || user.values['urenregistratie_invoeren_uursoorten'].value == 'bv')">
          <div class="flex-between border-bottom pl-2">
            <ion-text class="text-input" [ngClass]="readonly ? 'opacity-25' : ''" >Uursoort:</ion-text>
            <ion-text (click)="uursoortModalOpen(index)" class="ion-text-right" >
              <ion-button fill="clear" >
                <ion-icon *ngIf="!project.uursoort" name="swap-horizontal-outline"></ion-icon>
                <span *ngIf="project.uursoort" >{{project.uursoort.name}}</span>
              </ion-button>
            </ion-text>
          </div>
        </div>

        <div *ngIf="roleCanDo('Hele week invullen') && project.dag">
          <ion-item>
            <ion-label>Dag</ion-label>
            <ion-input type="text" [disabled]="true" [(ngModel)]="project.dag"></ion-input>
          </ion-item>
        </div>

        <div *ngIf="roleCanDo('Hele week invullen') && project.datum">
          <ion-item>
            <ion-label>Datum</ion-label>
            <ion-input type="text" [disabled]="true" [(ngModel)]="project.datum"></ion-input>
          </ion-item>
        </div>

        <div *ngIf="roleCanDo('uren o.b.v. getal')">
          <ion-item>
            <ion-label>Totaal aantal uren</ion-label>
            <ion-input step="0.25" type="number" text-right [disabled]="readonly" (ionChange)="pauze(index)" [(ngModel)]="project.gewerkteuren"></ion-input>
          </ion-item>
        </div>

        <div *ngIf="hasPermission('urenregistratie_heenreis_tijden')">
          <ion-item [ngClass]="readonly ? 'text-disabled' : ''" class="ml--2" >
            <ion-label >Begintijd heenreis</ion-label>
            <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(project, 'begintijd_heenreis', 5)" >{{project.begintijd_heenreis || 'Tijd selecteren'}}</div>
          </ion-item>
          <ion-item [ngClass]="readonly ? 'text-disabled' : ''" class="ml--2" >
            <ion-label >Eindtijd heenreis</ion-label>
            <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(project, 'eindtijd_heenreis', 5)" >{{project.eindtijd_heenreis || 'Tijd selecteren'}}</div>
          </ion-item>
        </div>

        <div *ngIf="!roleCanDo('uren o.b.v. getal')">
          <ion-item *ngIf="hasPermission('urenregistratie_begintijd')" [ngClass]="readonly ? 'text-disabled' : ''" class="ml--2" >
            <ion-label >Begintijd</ion-label>
            <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(project, 'begintijd', 5)" >{{project.begintijd || 'Tijd selecteren'}}</div>
          </ion-item>
          <ion-item *ngIf="hasPermission('urenregistratie_eindtijd')" [ngClass]="readonly ? 'text-disabled' : ''" class="ml--2" >
            <ion-label>Eindtijd</ion-label>
            <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(project, 'eindtijd', 5)" >{{project.eindtijd || 'Tijd selecteren'}}</div>
          </ion-item>
        </div>

        <div class="flex-between border-bottom pl-2" *ngIf="hasPermission('urenregistratie_pauzetijd')">
          <ion-text class="text-input" [ngClass]="readonly ? 'opacity-25' : ''" >Pauze:</ion-text>
          <ion-text (click)="pauzeModalOpen(index)" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="!project.pauze" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="project.pauze" >{{project.pauze}} uur</span>
            </ion-button>
          </ion-text>
        </div>

        <div *ngIf="hasPermission('urenregistratie_terugreis_tijden')">
          <ion-item [ngClass]="readonly ? 'text-disabled' : ''" class="ml--2" >
            <ion-label >Begintijd terugreis</ion-label>
            <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(project, 'begintijd_terugreis', 5)" >{{project.begintijd_terugreis || 'Tijd selecteren'}}</div>
          </ion-item>
          <ion-item [ngClass]="readonly ? 'text-disabled' : ''" class="ml--2" >
            <ion-label >Eindtijd terugreis</ion-label>
            <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(project, 'eindtijd_terugreis', 5)" >{{project.eindtijd_terugreis || 'Tijd selecteren'}}</div>
          </ion-item>
        </div>

        <div *ngIf="user.values['urenregistratie_invoeren_machines'] && user.values['urenregistratie_invoeren_machines'].value == 'aan'" [ngClass]="!readonly || project.machines.length ? 'border-bottom' : ''">
          <div class="flex-between pl-2" *ngIf="!readonly" >
            <div class="text-input" ><b>Machines</b></div>
            <div><ion-button color="success" fill="clear" (click)="addMachine(index)" ><ion-icon name="add-outline" ></ion-icon></ion-button></div>
          </div>
          <div *ngFor="let machine of project.machines;let machineIndex = index;" class="border-top">
            <div class="flex-between pl-2">
              <ion-text class="text-input" [ngClass]="readonly ? 'opacity-25' : ''" >Machine {{machineIndex + 1}}</ion-text>
              <ion-text>
                <ion-button (click)="machineModalOpen(index, machineIndex)" fill="clear">
                  <ion-icon *ngIf="!machine.id" name="swap-horizontal-outline"></ion-icon>
                  <span *ngIf="machine.id" >{{machine.name}}</span>
                </ion-button>
                <ion-button *ngIf="!readonly" color="danger" fill="clear" (click)="removeMachine(index, machineIndex)" ><ion-icon name="close-outline" ></ion-icon></ion-button>
              </ion-text>
            </div>
            <div *ngIf="user.values['planning_machines'].value == 'pp'">
              <ion-item lines="none" [ngClass]="readonly ? 'text-disabled' : ''" >
                <ion-label>Begintijd</ion-label>
                <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(machine, 'begintijd', 5)" >{{machine.begintijd || 'Tijd selecteren'}}</div>
              </ion-item>
              <ion-item lines="none" [ngClass]="readonly ? 'text-disabled' : ''" >
                <ion-label>Eindtijd</ion-label>
                <div class="py-1 hover-shadow rounded" slot="end" (click)="timeSelect.select(machine, 'eindtijd', 5)" >{{machine.eindtijd || 'Tijd selecteren'}}</div>
              </ion-item>
            </div>
          </div>
        </div>


        <ion-item *ngIf="hasPermission('urenregistratie_km')">
          <ion-label>Kilometers</ion-label>
          <ion-input [disabled]="readonly" [(ngModel)]="project.kilometers" disabled="true" class="ion-text-right" ></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_facturabel')">
          <ion-label>Facturabel</ion-label>
          <ion-select *ngIf="!readonly" [(ngModel)]="project.facturabel" placeholder="Maak een keuze">
            <ion-select-option *ngFor="let facturabeloptie of facturabelopties" [value]="facturabeloptie.id">{{ facturabeloptie.optie }}</ion-select-option>
          </ion-select>
          <ion-input *ngIf="readonly" [(ngModel)]="project.facturabel" disabled="true" class="ion-text-right" ></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_ladenlossen')">
          <ion-label>Laden/lossen begintijd</ion-label>
          <ion-button fill="clear" (click)="timeSelect.select(project, 'ladenlossen_begintijd', 5)" >{{project.ladenlossen_begintijd || 'Tijd selecteren'}}</ion-button>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_ladenlossen')">
          <ion-label>Laden/lossen eindtijd</ion-label>
          <ion-button fill="clear" (click)="timeSelect.select(project, 'ladenlossen_eindtijd', 5)" >{{project.ladenlossen_eindtijd || 'Tijd selecteren'}}</ion-button>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_ladenlossen_middag')">
          <ion-label>Laden/lossen begintijd middag</ion-label>
          <ion-button fill="clear" (click)="timeSelect.select(project, 'ladenlossen_middag_begintijd', 5)" >{{project.ladenlossen_middag_begintijd || 'Tijd selecteren'}}</ion-button>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_ladenlossen_middag')">
          <ion-label>Laden/lossen eindtijd middag</ion-label>
          <ion-button fill="clear" (click)="timeSelect.select(project, 'ladenlossen_middag_eindtijd', 5)" >{{project.ladenlossen_middag_eindtijd || 'Tijd selecteren'}}</ion-button>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_heenreis_woonwerk')">
          <ion-label>Heenreis woon/werk</ion-label>
          <ion-input [disabled]="readonly" [(ngModel)]="project.heenreis_woonwerk" class="ion-text-right" type="number"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_heenreis_woonwerk')">
          <ion-label>Terugreis woon/werk</ion-label>
          <ion-input [disabled]="readonly" [(ngModel)]="project.terugreis_woonwerk" class="ion-text-right" type="number"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_reisuren')">
          <ion-label>Reisuren</ion-label>
          <ion-input [disabled]="!hasPermission('urenregistratie_reistijd_invoeren') || readonly" [(ngModel)]="project.reisuren" class="ion-text-right" type="number"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_heenreis_huiswerk')">
          <ion-label>Heenreis huis/project</ion-label>
          <ion-input [disabled]="readonly" [(ngModel)]="project.heenreis_huisproject" class="ion-text-right" type="number"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_terugreis_huiswerk')">
          <ion-label>Terugreis huis/project</ion-label>
          <ion-input [disabled]="readonly" [(ngModel)]="project.terugreis_huisproject" class="ion-text-right" type="number"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_bestuurder')">
          <ion-label>Bestuurder</ion-label>
          <ion-select *ngIf="!readonly" [disabled]="readonly" [(ngModel)]="project.bestuurder" class="ion-text-right" placeholder="Maak een keuze">
            <ion-select-option value="true">Ja</ion-select-option>
            <ion-select-option value="false">Nee</ion-select-option>
          </ion-select>
          <ion-input *ngIf="readonly" [disabled]="true" [(ngModel)]="project.bestuurder" class="ion-text-right"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_bijrijder')">
          <ion-label>Bijrijder</ion-label>
          <ion-input [disabled]="readonly" [(ngModel)]="project.bijrijder" class="ion-text-right" ></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_kenteken')">
          <ion-label>Kenteken</ion-label>
          <ion-select *ngIf="!readonly" [(ngModel)]="project.kenteken_id" placeholder="Maak een keuze">
            <ion-select-option *ngFor="let kenteken of kentekens" [value]="kenteken.id">{{ kenteken.kenteken }} {{ kenteken.opmerking }}</ion-select-option>
          </ion-select>
          <ion-input *ngIf="readonly" [disabled]="true" [(ngModel)]="project.kenteken_id" class="ion-text-right"></ion-input>
        </ion-item>

        <div *ngIf="hasPermission('urenregistratie_naca') && user.naca" class="flex-between border-bottom pl-2" >
          <ion-text class="text-input" [ngClass]="readonly ? 'opacity-25' : ''">Naca-codes</ion-text>
          <ion-text>
            <ion-button (click)="nacaModalOpen(index)" fill="clear">
              <ion-icon *ngIf="!project.naca" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="project.naca" >{{project.naca}}</span>
            </ion-button>

          </ion-text>
        </div>

        <ion-item *ngIf="hasPermission('urenregistratie_tijdvoortijd') && !readonly">
          <ion-label>Tijd voor Tijd</ion-label>
          <ion-toggle [(ngModel)]="project.tijdvoortijd" [disabled]="readonly"></ion-toggle>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_overnachting')">
          <ion-label>Overnachting</ion-label>
          <ion-select [disabled]="readonly" [(ngModel)]="project.overnachting" placeholder="Maak een keuze">
            <ion-select-option value="Nee">Nee</ion-select-option>
            <ion-select-option value="Ja">Ja</ion-select-option>
            <ion-select-option value="Ja enkele reis">Ja, met heen/terug reis</ion-select-option>
          </ion-select>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_verlof')">
          <ion-label>Verlof</ion-label>
          <ion-select *ngIf="!readonly" [(ngModel)]="project.verlof" placeholder="Maak een keuze">
            <ion-select-option value="0"> Geen verlof</ion-select-option>
            <ion-select-option value="8">8 (hele dag)</ion-select-option>
            <ion-select-option value="7.75">7,75</ion-select-option>
            <ion-select-option value="7.5">7,5 uur</ion-select-option>
            <ion-select-option value="7.25">7,25 uur</ion-select-option>
            <ion-select-option value="7">7 uur</ion-select-option>
            <ion-select-option value="6.75">6,75 uur</ion-select-option>
            <ion-select-option value="6.5">6,5 uur</ion-select-option>
            <ion-select-option value="6.25">6,25 uur</ion-select-option>
            <ion-select-option value="6">6 uur</ion-select-option>
            <ion-select-option value="5.75">5,75 uur</ion-select-option>
            <ion-select-option value="5.5">5,5 uur</ion-select-option>
            <ion-select-option value="5.25">5,25 uur</ion-select-option>
            <ion-select-option value="5">5 uur</ion-select-option>
            <ion-select-option value="4.75">4,75 uur</ion-select-option>
            <ion-select-option value="4.5">4,5 uur</ion-select-option>
            <ion-select-option value="4.25">4,25 uur</ion-select-option>
            <ion-select-option value="4">4 uur</ion-select-option>
            <ion-select-option value="3.75">3,75 uur</ion-select-option>
            <ion-select-option value="3.5">3,5 uur</ion-select-option>
            <ion-select-option value="3.25">3,25 uur</ion-select-option>
            <ion-select-option value="3">3 uur</ion-select-option>
            <ion-select-option value="2.75">2,75 uur</ion-select-option>
            <ion-select-option value="2.5">2,5 uur</ion-select-option>
            <ion-select-option value="2.25">2,25 uur</ion-select-option>
            <ion-select-option value="2">2 uur</ion-select-option>
            <ion-select-option value="1.75">1,75 uur</ion-select-option>
            <ion-select-option value="1.5">1,5 uur</ion-select-option>
            <ion-select-option value="1.25">1,25 uur</ion-select-option>
            <ion-select-option value="1">1 uur</ion-select-option>
            <ion-select-option value="0.75">0,75 uur</ion-select-option>
            <ion-select-option value="0.5">0,5 uur</ion-select-option>
            <ion-select-option value="0.25">0,25 uur</ion-select-option>
          </ion-select>
          <ion-input *ngIf="readonly" [disabled]="true" [(ngModel)]="project.verlof" class="ion-text-right"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_verlof')">
          <ion-label>Verlofreden</ion-label>
          <ion-select *ngIf="!readonly" [(ngModel)]="project.verlofreden" placeholder="Maak een keuze">
            <ion-select-option *ngFor="let reden of user.redenen" [value]="reden.id">{{ reden.reden }}</ion-select-option>
          </ion-select>
          <ion-input *ngIf="readonly" [disabled]="true" [(ngModel)]="project.verlofreden" class="ion-text-right"></ion-input>
        </ion-item>

        <ion-item *ngIf="hasPermission('urenregistratie_opmerkingen')">
          <ion-label>Opmerkingen</ion-label>
          <ion-textarea [autoGrow]="true" [disabled]="readonly" [(ngModel)]="project.opmerkingen" class="ion-text-right" placeholder="Typ hier..."></ion-textarea>
        </ion-item>

      </div>
      <div *ngIf="!readonly">
        <div class="my-2 ion-text-center" *ngIf="hasPermission('urenregistratie_projecttoevoegen') && type != 'week'" >
          <a class="btn btn-inverse-primary"  (click)="addProject()">Blok toevoegen</a>
        </div>
        <div class="my-2 ion-text-center flex-between" >
          <a *ngIf="btn && settings.local_btn" class="btn btn-inverse-success w-100 mx-2" (click)="localStore()">Tussentijds opslaan</a>
          <a *ngIf="btn" class="btn btn-success w-100 mx-2" (click)="onSubmit()" >Versturen</a>
          <ion-spinner *ngIf="!btn" color="success" name="crescent" class="spinner-inverse"></ion-spinner>
        </div>

      </div>
    </section>

  </ion-card>
</ion-content>

<!--  Select project-->
<div class="modal-container" *ngIf="projectModal.state" (click)="projectModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="projectModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectProject()" >Geen projectnummer</ion-button>
      </div>
      <div *ngFor="let project of projects" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="projectSearch(project, 0)" fill="clear" (click)="selectProject(project.id, 0)" >
          <div>
            <small class="opacity-50 d-block" >{{project.projectnr}}</small>
            <span>{{project.projectnaam || ''}}, {{project.opdrachtgever || ''}}</span>
          </div>
        </ion-button>
      </div>
      <div *ngFor="let offerte of offertenummers" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="projectSearch(0, offerte)" fill="clear" (click)="selectProject(0, offerte.id)" >
          <div>
            <small class="opacity-50 d-block" >{{offerte.offertenummer}}</small>
            <span>{{offerte.naam || ''}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select naca-->
<div class="modal-container" *ngIf="nacaModal.state" (click)="nacaModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="nacaModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let naca of user.naca" class="ion-text-center">
        <ion-button *ngIf="nacaSearch(naca)" fill="clear" (click)="selectNaca(naca.code)" >
          <div>
            <span class="d-block" >{{naca.code}}</span>
            <small class="opacity-50" >{{naca.omschrijving}}</small>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select machine-->
<div class="modal-container" *ngIf="machineModal.state" (click)="machineModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="machineModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let machine of user.machines" class="ion-text-center">
        <ion-button *ngIf="machineSearch(machine)" fill="clear" (click)="selectMachine(machine)" >
          <div>
            <span class="d-block" >{{machine.name}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select uursoort-->
<div class="modal-container" *ngIf="uursoortModal.state" (click)="uursoortModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="uursoortModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let uursoort of user.uursoorten" class="ion-text-center">
        <ion-button *ngIf="uursoortSearch(uursoort)" fill="clear" (click)="selectUursoort(uursoort)" >
          <div>
            <span class="d-block" >{{uursoort.code}}. {{uursoort.name}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select pauze-->
<div class="modal-container" *ngIf="pauzeModal.state" (click)="pauzeModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <div class="ion-padding overflow-auto mh-33-vh">
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(0)" >Geen pauze</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(0.25)" >15 min</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(0.5)" >30 min</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(0.75)" >45 min</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(1)" >1 uur</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(1.25)" >1 uur 15 min</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(1.5)" >1 uur 30 min</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(1.75)" >1 uur 45 min</ion-button>
      </div>
      <div class="ion-text-center">
        <ion-button fill="clear" (click)="selectPauze(2)" >2 uur</ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--Time-->
<div class="modal-container" *ngIf="timeSelect.modal" (click)="timeSelect.modal = false;" >
  <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()" >
    <div class="d-flex ion-justify-content-around font-size-125">

      <div class="flex-between w-100" >
        <div class="bg-secondary h-1 w-100 px-2"></div>
      </div>

      <!--      Hours-->
      <div (scroll)="timeSelect.scrollTime('hour')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-hour" >
        <div style="height: 39px" ></div>
        <div class="ion-text-center my-4" id="custom-time-hour-{{h}}" *ngFor="let h of timeSelect.vars.hours" (click)="timeSelect.selectTimeValue('hour', h)" >
          <span class="{{timeSelect.vars.time.hour == h ? 'text-primary' : ''}}" >{{('0'+h).slice(-2)}}</span>
        </div>
        <div style="height: 39px" ></div>
      </div>

      <div class="flex-between w-50" >
        <div class="bg-secondary h-1 w-100 px-2"></div>
      </div>

      <!--      Minutes-->
      <div (scroll)="timeSelect.scrollTime('min')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-min" >
        <div style="height: 39px" ></div>
        <div class="ion-text-center my-4" id="custom-time-min-{{m}}" *ngFor="let m of timeSelect.vars.minutes" (click)="timeSelect.selectTimeValue('min', m)">
          <span class="{{timeSelect.vars.time.min == m ? 'text-primary' : ''}}" >{{('0'+m).slice(-2)}}</span>
        </div>
        <div style="height: 39px" ></div>
      </div>

      <div class="flex-between w-100" >
        <div class="bg-secondary h-1 w-100 px-2"></div>
      </div>

    </div>
    <div class="mt-1 pt-1 ion-text-right border-top" >
      <ion-button fill="clear" (click)="timeSelect.confirmTime()" >OK</ion-button>
    </div>
  </ion-card>
</div>
