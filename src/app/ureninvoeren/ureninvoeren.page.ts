import {Component, OnInit} from '@angular/core';
import { Router } from '@angular/router';
import {Platform} from "@ionic/angular";
import { Infordb, timeSelect } from 'infordb';
import {ClientStorageService} from "../services/ClientStorage/client-storage.service";

@Component({
  selector: 'app-ureninvoeren',
  templateUrl: './ureninvoeren.page.html',
  styleUrls: ['./ureninvoeren.page.scss'],
  providers: [Infordb]
})

export class UreninvoerenPage implements OnInit {

  public user = JSON.parse(window.localStorage.getItem('user'));
  public subdomain = window.localStorage.getItem('subdomain');

	public timeSelect = timeSelect

	public date = localStorage.getItem('urenregistratieDate');
  public dateUTC = this.dateToDate(this.date);
  public kentekens = JSON.parse(window.localStorage.getItem('user')).kentekens;
  public facturabelopties = JSON.parse(window.localStorage.getItem('user')).facturabelopties;
  public type = localStorage.getItem('urenregistratieType') || '';
  public day = localStorage.getItem('urenregistratieDay') || '';

  public posting = true;
  public btn = true;
  public readonly = false;
  public edit = false;
  public projects = [];
  public projecten = [];
  public planning = [];
  public werkbonnen = [];
  public offertenummers = [];
  public standaarduren = [];
  public correctie = null;

  public settings = {
    pause_listener: null,
    local_btn: true,
  }
  public projectModal = {
    state: false,
    index: null,
    search: '',
  };
  public nacaModal = {
    state: false,
    index: null,
    search: '',
  };
  public machineModal = {
    state: false,
    index: null,
    machineIndex: null,
    search: '',
  };
  public uursoortModal = {
    state: false,
    index: null,
    search: '',
  };
  public pauzeModal = {
    state: false,
    index: null,
  }
  public heleWeek = {
    project: null,
    offerte: null,
    start: null,
    end: null,
    dates: [],
  }

  constructor(
    private router: Router,
    private plt: Platform,
    private infordb: Infordb,
    private clientStorage: ClientStorageService,
  ) {
    this.localSubscribe();
  }

  ngOnInit() {
    if(this.user.values['urenregistratie_app_hide_local_store'] && this.user.values['urenregistratie_app_hide_local_store'].value == 'Ja'){
      this.settings.local_btn = false;
    }
    timeSelect.onChange = this.setDefaultPauzes.bind(this);
  }

  async ionViewWillEnter() {
    try{
      this.projectsInit();
      this.offertesInit();
      await this.urenInit();
      await this.planningInit();
      await this.werkbonnenInit();
      this.localFetch();
      this.editInit();
    }
    catch (e){ this.infordb.handleError(e); }

    this.posting = false;
  }
  ionViewWillLeave() {
    this.localUnsubscribe()
  }

  localSubscribe(){
    this.settings.pause_listener = this.plt.pause.subscribe(() => {
      this.localUnsubscribe()
      this.localStore();
    });
  }
  localUnsubscribe(){
    this.settings.pause_listener.unsubscribe();
  }
  localStore(){
    this.localUnsubscribe();

    if(this.type != 'ingevoerd' && this.type != 'week'){
      const data = this.clientStorage.get('uren_local_data', {});
      data[this.date] = JSON.stringify(this.projecten);

      this.clientStorage.set('uren_local_data', data);
      this.infordb.notification({message: 'Uw wijzigingen zijn opgeslagen.'});
    }

    this.callPage('urenregistratie');
  }
  localFetch(){
    if(this.type === 'ingevoerd'){return;}

    const data = this.clientStorage.get('uren_local_data', {})

    if(data[this.date]){
      this.projecten = JSON.parse(data[this.date]);
      for(const project of this.projecten){project.label = '( Lokale versie )'}
    }
  }

  async urenInit(){
    if(this.type == 'week'){
      this.weekInit();
      return;
    }
    if (this.type === 'ingevoerd') {
      this.readonly = true;

      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/date`, {
        date: this.date,
        token: this.user.api_token
      })
        .then((res) => {
          const uren = res.data.uren;
          this.correctie = res.data.correctie;
          for (const uur of uren){

            const taken = uur?.project?.taken || [];
            taken_loop: for(const taak of taken){
              for(const uur_taak of uur.projecttaken || []){
                if (uur_taak.id == taak.id){
                  taak._checked = true;
                  continue taken_loop;
                }
              }
            }

            this.projecten.push({
              projectnummer: uur.projectnummer,
              projectnaam: uur.project?.projectnaam ?? 'Geen project',
              opdrachtgever: uur.project?.opdrachtgever ?? '-',
              begintijd: uur.begintijd ? uur.begintijd.slice(0, 5) : uur.begintijd,
              eindtijd: uur.eindtijd ? uur.eindtijd.slice(0, 5) : uur.eindtijd,
              gewerkteuren: uur.gewerkte_uren,
              kilometers: uur.kilometers,
              pauze: uur.pauze,
              facturabel: uur.facturabelopties ? uur.facturabelopties.optie : '',
              ladenlossen_begintijd: uur.ladenlossen_begintijd,
              reisuren: uur.reisuren,
              begintijd_heenreis: uur.begintijd_heenreis,
              eindtijd_heenreis: uur.eindtijd_heenreis,
              begintijd_terugreis: uur.begintijd_terugreis,
              eindtijd_terugreis: uur.eindtijd_terugreis,
              ladenlossen_eindtijd: uur.ladenlossen_eindtijd,
              ladenlossen_middag_begintijd: uur.ladenlossen_middag_begintijd,
              ladenlossen_middag_eindtijd: uur.ladenlossen_middag_eindtijd,
              heenreis_woonwerk: uur.heenreis_woonwerk,
              terugreis_woonwerk: uur.terugreis_woonwerk,
              heenreis_huisproject: uur.heenreis_huisproject,
              terugreis_huisproject: uur.terugreis_huisproject,
              bestuurder: (uur.bestuurder === '1' ? 'Ja' : 'Nee'),
              bijrijder: uur.bijrijder,
              kenteken_id: uur.kenteken_id,
              tijdvoortijd: (uur.tijdvoortijd === '1'),
              verlofreden: uur.verlof_rtl ? uur.verlof_rtl.reden : '',
              verlof: uur.verlof,
              overnachting: uur.overnachting,
              naca: uur.naca ? uur.naca.code : '',
              opmerkingen: uur.opmerkingen,
              machines: uur.machines,
              uursoort_id: uur.uursoort_id,
              uursoort: uur.uursoort,
              _taken: taken,
            });



          }
        })
        .catch(this.infordb.handleError);
      return;
    }

    const verlof = await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/verlof`, {
      date: this.date,
      token: this.user.api_token
    });

    const verlofEntries = (Array.isArray(verlof.data) ? verlof.data : [verlof.data])

    this.user.default.forEach((day) => {
      if (day.dag.toLowerCase() === this.day.toLowerCase()) {
        this.standaarduren = day;

        const standaardBegintijd = this.standaarduren['begintijd'].slice(0, 5);
        const standaardEindtijd = this.standaarduren['eindtijd'].slice(0, 5);
        const [stdStartHour, stdStartMinute] = standaardBegintijd.split(':').map(Number);
        const [stdEndHour, stdEndMinute] = standaardEindtijd.split(':').map(Number);
        const pauze = parseFloat(this.standaarduren['pauze']) || 0;
        let standaardUren = Number(this.standaarduren['standaarduren']);
        standaardUren = standaardUren ? standaardUren : ((stdEndHour + stdEndMinute / 60) - (stdStartHour + stdStartMinute / 60)) - pauze;

        let totalVerlofUren = 0;
        verlofEntries.forEach((verlof) => {
          const begintijd = verlof.van ? verlof.van.slice(0, 5) : standaardBegintijd;
          const eindtijd = verlof.tot ? verlof.tot.slice(0, 5) : standaardEindtijd;
          const [startHour, startMinute] = begintijd.split(':').map(Number);
          const [endHour, endMinute] = eindtijd.split(':').map(Number);
          totalVerlofUren += ((endHour + endMinute / 60) - (startHour + startMinute / 60));
        });

        this.projecten.push({
          projectnummer: null,
          begintijd: standaardBegintijd,
          eindtijd: standaardEindtijd,
          gewerkteuren: standaardUren,
          verlof: totalVerlofUren.toString(),
          kilometers: day.kilometers,
          pauze: day.pauze,
          standaardpauzes: day.standaardpauzes,
          facturabel: day.facturabel,
          ladenlossen_begintijd: day.ladenlossen_begintijd,
          reisuren: day.reisuren,
          ladenlossen_eindtijd: day.ladenlossen_eindtijd,
          ladenlossen_middag_begintijd: day.ladenlossen_middag_begintijd,
          ladenlossen_middag_eindtijd: day.ladenlossen_middag_eindtijd,
          heenreis_woonwerk: day.heenreis_woon_werk,
          terugreis_woonwerk: day.terugreis_woon_werk,
          heenreis_huisproject: day.heenreis_huis_project,
          terugreis_huisproject: day.terugreis_huis_project,
          bestuurder: day.bestuurder,
          bijrijder: day.bijrijder,
          kenteken_id: day.kenteken_id,
          verlofreden: verlofEntries.length > 0 ? this.getVerlofReden(this.type) : null,
          overnachting: day.overnachting,
          naca: day.naca_id,
          opmerkingen: day.opmerkingen,
          machines: [],
          uursoorten: [],
        });

        this.setDefaultPauzes();
      }
    });
  }

  getVerlofReden(type){
    switch (type.toLowerCase()) {
      case 'ziekte': return '1';
      case 'feestdag': return '4';
      case 'verlof': return '2';
      default: return null;
    }
  }

  weekInit(){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/week`, {
      token: this.user.api_token
    }).then((res) => {
      if(res.status != 200){ throw res; }
      if(!res.data){ alert('Er zijn reeds uren in de afgelopen week ingevuld.'); this.router.navigateByUrl('/urenregistratie'); return; }
      this.heleWeek.start = res.data.week_start;
      this.heleWeek.end = res.data.week_end;
      this.heleWeek.dates = res.data.dates;
      this.date = this.formatDate(res.data.week_start)+ ' - ' +this.formatDate(res.data.week_end);
      this.projectModalOpen();
    }).catch(this.infordb.handleError);
  }

  setDefaultPauzes(){
    const standaardpauzes = JSON.parse(this.standaarduren['standaardpauzes'] ?? '{}');

    if (!standaardpauzes || !Array.isArray(standaardpauzes) || standaardpauzes.length === 0) {
      return;
    }

    for(const index in this.projecten){
      const project = this.projecten[index];
      let begintijd = new Date('2021-01-01 ' + project.begintijd);
      let eindtijd = new Date('2021-01-01 ' + project.eindtijd);
      let pauzeduur = 0;
      for (let pauze in standaardpauzes) {
        const pauzetijden = standaardpauzes[pauze];
        const begin = new Date('2021-01-01 ' + pauzetijden.begin);
        const eind = new Date('2021-01-01 ' + pauzetijden.eind);
        if (begin >= begintijd && eind <= eindtijd) {
          pauzeduur += Math.round(Number(this.getDateDiff(begin, eind).hours) * 4) / 4;
          pauzeduur += Math.round(Number(this.getDateDiff(begin, eind).minutes) / 15) / 4;
        }
      }
      this.projecten[index].pauze = pauzeduur;
    }
  }

  editInit(){
    if(this.type != 'ingevoerd' || !this.user.values['urenregistratie_days_to_edit'] || this.user.values['urenregistratie_days_to_edit'].value == 'uit'){ return }

    let explode = this.date.split('-');
    const timeDay = new Date(Number(explode[2]), Number(explode[1]), Number(explode[0])).getTime();

    let dateNow = new Date();
    let timeNow = new Date(dateNow.getFullYear(), dateNow.getMonth() + 1, dateNow.getDate()).getTime();

    const diff = Math.round(Math.abs(timeDay - timeNow) / (1000 * 60 * 60 * 24));
    if(diff < Number(this.user.values['urenregistratie_days_to_edit'].value)){
      this.edit = true;
    }

  }
  editForm(){
    //ion-select bugs when ngif var is changed to true/false

    const projects = JSON.parse(JSON.stringify(this.projecten));

    this.projecten = [];
    this.readonly = false;

    this.projecten = projects;
  }

  onSubmit() {
    if(!this.projecten.length){
      alert('Formulier kan niet leeg zijn!')
      return;
    }
    let totaleUren = 0;
    for(const project of this.projecten){
      try{
        if(this.roleCanDo('uren o.b.v. getal')){
          if(project.gewerkteuren <= 0 || project.gewerkteuren == ''){
            alert('Vul uren in!');
            return;
          }
        }else{
          if(this.getDateDiff(new Date(project.begintijd), new Date(project.eindtijd)).minutes <= 0){
            alert('Begin- en eindtijd niet correct ingevuld');
            return;
          }
        }

        if (Number(project.verlof) && !project.verlofreden) {
          alert('Geef een verlofreden op.');
          return;
        }
        if(!Number(project.verlof) && project.verlofreden) {
          alert('Geef een verlofduur op.');
          return;
        }
        if(((this.getDateDiff(new Date(project.begintijd), new Date(project.eindtijd)).hours - project.pauze - project.verlof) < 0 && project.verlof != 8) || !project.begintijd || !project.eindtijd || project.begintijd > project.eindtijd ) {
          if(!this.roleCanDo('uren o.b.v. getal')) {
            alert('Controleer of alle tijden van project ' + project.projectnummer + ' goed zijn ingevuld. Het resultaat van de uren is momenteel minder dan 0.');
            return;
          }
        }
        if (this.hasPermission('urenregistratie_projectnummer') && !project.projectnummer) {
          alert('Vul een projectnummer in. Selecteer bij niet-werkdagen \'Geen projectnummer\'.');
          return;
        }
        if(this.hasPermission('urenregistratie_naca')){
          if(!this.roleCanDo('uren o.b.v. getal')) {
            if ((this.getDateDiff(new Date(project.begintijd), new Date(project.eindtijd)).hours - project.pauze - project.verlof) > 0 && !project.naca) {
              alert('Vul een naca code in!');
              return;
            }
          }
          else {
            if ((project.gewerkteuren - project.pauze - project.verlof) > 0 && !project.naca) {
              alert('Vul een naca code in!');
              return;
            }
          }
        }

        const taken = [];
        if(project._taken){
          for(const taak of project._taken){
            if(!taak._checked){ continue; }
            taken.push(taak.id);
          }
        }
        project.taken = taken;

        const totalen = this.getDateDiff(new Date('2021-01-01 '+project.begintijd), new Date('2021-01-01 '+project.eindtijd));

        if(!this.roleCanDo('uren o.b.v. getal')){
          totaleUren += totalen.hours;
          totaleUren += totalen.minutes / 60;
        }else{
          totaleUren += Number(project.gewerkteuren);
        }

        totaleUren -= Number(project.pauze) + Number(project.verlof);
      }
      catch (e){ this.infordb.handleError(e); }

    }
    if(totaleUren < 0){
      alert('Totaal aantal uren kan niet negatief zijn!');
      return;
    }
    if(totaleUren > Number(this.standaarduren['standaarduren']) && this.user.values['urenregistratie_overuren_goedkeuren'] && JSON.parse(this.user.values['urenregistratie_overuren_goedkeuren'].value)[this.user.bv_id].active == 'on'){
      if(!confirm('Je totale aantal uren is hoger dan je standaarduren. Weet je zeker dat je ze zo op wil slaan? Als je doorgaat zullen je uren door een leidinggevende gecontroleerd worden.')){return false;}
    }
    if(this.standaarduren['maximale_uren'] && this.standaarduren['maximale_uren'] > 0 && totaleUren > Number(this.standaarduren['maximale_uren'])){

      alert('Totaal aantal uren kan niet hoger zijn dan ' + this.standaarduren['maximale_uren']);
      return;
    }

    if (totaleUren < Number(this.standaarduren['standaarduren'])) {
      if(!confirm('Je ingevulde uren zijn minder dan je standaarduren. Weet je zeker dat je dit wil opslaan?')){return;}
    }

    if (totaleUren > Number(this.standaarduren['standaarduren'])) {
      if(!confirm('Je ingevulde uren zijn meer dan je standaarduren. Weet je zeker dat je dit wil opslaan?')){return;}
    }

    this.btn = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/${this.type == 'week' ? 'storeweek' : 'store'}`, {
      date: this.date,
      token: this.user.api_token,
      projects: JSON.stringify(this.projecten)
    })
      .then((res) => {
        this.router.navigateByUrl('/urenregistratie');
        alert('het formulier is ingediend');
      })
      .catch(this.infordb.handleError);
  }

  projectsInit(){
    let params = {
      api_token: this.user.api_token,
      user: this.user.user_id.toString(),
      origin: 'urenregistratie'
    };
    if(this.user.values['urenregistratie_invoeren_projectnummers'] && this.user.values['urenregistratie_invoeren_projectnummers'].value == 'perbv'){
      params['bv'] = this.user.bv_id.toString();
    }
    if(this.user.values['urenregistratie_invoeren_projectstatussen'] && this.user.values['urenregistratie_invoeren_projectstatussen'].value != ''){
      params['status'] = JSON.parse(this.user.values['urenregistratie_invoeren_projectstatussen'].value)
    }

    this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/projecten/get`, params)
      .then(response => {
        if (response.status == 201) {
          const data = response.data;
          this.projects = data.projecten;
        }
      })
      .catch(this.infordb.handleError);
  }
  offertesInit(){
    if(!this.user.values['urenregistratie_invoeren_offertenummers'] || this.user.values['urenregistratie_invoeren_offertenummers'].value != 'aan'){ return }

    this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/offertes/get`, {
      user: this.user.user_id.toString(),
      type: '1',
      active: '1',
    })
      .then(response => {
        if (response.status != 200) { throw response; }

        const data = response.data;
        this.offertenummers = data.offertes;
      })
      .catch(this.infordb.handleError);

  }

  addProject(): void {
    this.projecten.push(this.createProject());
    this.setDefaultPauzes();
  }
  removeProject(index: number): void {
    this.projecten.splice(index, 1);
  }

  createProject() {
    let eindtijd = null;
    let nieuweeindtijd = null;
    let projectnummer = '';
    let projectnaam = '';
    let opdrachtgever = '';
    let taken = [];
    if(this.user.values['urenregistratie_invoeren_project_overnemen'] && this.projecten[this.projecten.length - 1] && this.user.values['urenregistratie_invoeren_project_overnemen'].value == 'aan'){
      const previous = this.projecten[this.projecten.length - 1];
      projectnummer = previous.projectnummer;
      projectnaam = previous.projectnaam;
      opdrachtgever = previous.opdrachtgever;
      eindtijd = previous.eindtijd ?? null;
      nieuweeindtijd = this.fullTimeToHi(this.standaarduren['eindtijd']) ?? null;

      // Copy taken without refeference
      taken = JSON.parse(JSON.stringify(previous._taken ?? []));
      taken.forEach(row => row._checked = false)
    }
    return {
      dag: null,
      datum: null,
      date: null,
      projectnummer: projectnummer,
      projectnaam: projectnaam,
      opdrachtgever: opdrachtgever,
      begintijd: eindtijd,
      gewerkteuren: '',
      kilometers: 0,
      eindtijd: nieuweeindtijd,
      pauze: '',
      facturabel: '',
      ladenlossen_begintijd: '',
      ladenlossen_eindtijd: '',
      ladenlossen_middag_begintijd: '',
      ladenlossen_middag_eindtijd: '',
      heenreis_woonwerk: '',
      terugreis_woonwerk: '',
      heenreis_huisproject: '',
      reisuren: '',
      terugreis_huisproject: '',
      begintijd_heenreis: '',
      eindtijd_heenreis: '',
      begintijd_terugreis: '',
      eindtijd_terugreis: '',
      bestuurder: '', // boolean
      bijrijder: '',  // string
      kenteken_id: '',
      tijdvoortijd: false,
      verlofreden: '',
      naca: '',
      verlof: '',
      overnachting: '',
      opmerkingen: '',
      label: null,
      machines: [],
      uursoorten: [],
      _taken: taken,
    };
  }
  createMachine(){
    return {
      id: null,
      name: '',
      begintijd: null,
      eindtijd: null,
    }
  }

  uursoortModalOpen(index){
    if(this.readonly){return;}

    this.uursoortModal.state = true;
    this.uursoortModal.index = index;
    this.uursoortModal.search = '';
  }
  uursoortSearch(uursoort) {
    const search = uursoort.name.toLowerCase();
    return search.includes(this.uursoortModal.search.toLowerCase());

  }
  selectUursoort(uursoort) {
    this.projecten[this.uursoortModal.index].uursoort_id = uursoort.id;
    this.projecten[this.uursoortModal.index].uursoort = uursoort;
    this.uursoortModal.state = false;
  }

  addMachine(i){
    const project = this.projecten[i];
    const machine = this.createMachine();

    machine.begintijd = project.begintijd;
    machine.eindtijd = project.eindtijd;

    project.machines.push(machine);
  }
  removeMachine(i, m){
    this.projecten[i].machines.splice(m, 1);
  }
  machineModalOpen(index: number, machineIndex: number) {
    if(this.readonly){return;}

    this.machineModal.state = true;
    this.machineModal.index = index;
    this.machineModal.machineIndex = machineIndex;
    this.machineModal.search = '';
  }
  machineSearch(machine) {
    const search = machine.name.toLowerCase();
    return search.includes(this.machineModal.search.toLowerCase());

  }
  selectMachine(machine) {
    this.projecten[this.machineModal.index].machines[this.machineModal.machineIndex].id = machine.id;
    this.projecten[this.machineModal.index].machines[this.machineModal.machineIndex].name = machine.name;
    this.machineModal.state = false;
  }


  projectModalOpen(index: number = null) {
    if(this.readonly){return;}

    this.projectModal.state = true;
    this.projectModal.index = index;
    this.projectModal.search = '';
  }
  projectSearch(project, offerte) {
    if(offerte == "0"){
      const search = (`${project.projectnr} ${project.projectnaam} ${project.opdrachtgever}`).toLowerCase();
      return search.includes(this.projectModal.search.toLowerCase());
    }else if(project == "0"){
      const search = (`${offerte.offertenummer} ${offerte.naam}`).toLowerCase();
      return search.includes(this.projectModal.search.toLowerCase());
    }
  }
  selectProject(projectid = null, offerteid = null){
    this.projectModal.state = false;
    if(this.type == 'week'){
      this.weekProjectSelect(projectid, offerteid);
      return;
    }
    this.projecten[this.projectModal.index]._taken = [];

    if(!projectid && !offerteid){
      this.projecten[this.projectModal.index].projectnummer = '-';
      this.projecten[this.projectModal.index].projectnaam = 'Geen project';
      this.projecten[this.projectModal.index].opdrachtgever = '';
      this.projecten[this.projectModal.index].reisuren = 0;
      return;
    }

    if(projectid && offerteid == '0'){
      const project = this.projects.find(row => row.id === projectid);
      this.projecten[this.projectModal.index].projectnummer = project.projectnr;
      this.projecten[this.projectModal.index].projectnaam = project.projectnaam;
      this.projecten[this.projectModal.index].opdrachtgever = project.opdrachtgever;
      this.projecten[this.projectModal.index].reisuren = project.reis;
      this.projecten[this.projectModal.index]._taken = project.taken || [];
    }

    if(projectid == '0' && offerteid){
      const offerte = this.offertenummers.find(row => row.id === offerteid);
      this.projecten[this.projectModal.index].projectnummer = offerte.offertenummer;
      this.projecten[this.projectModal.index].projectnaam = offerte.naam;
      this.projecten[this.projectModal.index].opdrachtgever = '';
      this.projecten[this.projectModal.index].reisuren = 0;
    }
  }

  weekProjectSelect(projectid, offerteid){

    if(!projectid && !offerteid){
      this.heleWeek.project = null;
      this.heleWeek.start = null;
      this.heleWeek.end = null;
      return;
    }

    if(projectid && offerteid == '0'){
      const project = this.projects.find(row => row.id === projectid);
      this.heleWeek.project = project;
    }
    if(projectid == '0' && offerteid){
      const offerte = this.offertenummers.find(row => row.id === offerteid);
      this.heleWeek.offerte = offerte;
    }
    this.fillWeek();
  }

  fillWeek(){
    for(const dag in this.heleWeek.dates){
      let datum = this.heleWeek.dates[dag];
      this.standaarduren = this.user.default.find(row => row.dag.toLowerCase() == dag.toLowerCase());
      if(!this.standaarduren || !this.standaarduren['standaarduren']){continue;}

      const project = this.createProject();
      project.dag = dag;
      project.datum = this.formatDate(datum);
      project.date = datum;
      project.projectnummer = this.heleWeek.project ? this.heleWeek.project.projectnr : (this.heleWeek.offerte.offertenummer ?? '-');
      project.projectnaam = this.heleWeek.project ? this.heleWeek.project.projectnaam : (this.heleWeek.offerte.naam ?? '-');
      project.opdrachtgever = this.heleWeek.project ? this.heleWeek.project.opdrachtgever : '';
      project.begintijd = this.standaarduren['begintijd'] ? this.standaarduren['begintijd'].slice(0, 5): this.standaarduren['begintijd'];
      project.gewerkteuren = this.standaarduren['standaarduren'];
      project.kilometers = this.standaarduren['kilometers'];
      project.eindtijd = this.standaarduren['eindtijd'] ? this.standaarduren['eindtijd'].slice(0, 5) : this.standaarduren['eindtijd'];
      project.pauze = this.standaarduren['pauze'];

      this.projecten.push(project);
    }
  }


  nacaModalOpen(index: number) {
    if(this.readonly){return;}

    this.nacaModal.state = true;
    this.nacaModal.index = index;
    this.nacaModal.search = '';
  }
  nacaSearch(naca) {
    const search = (`${naca.code} ${naca.omschrijving}`).toLowerCase();

    return search.includes(this.nacaModal.search.toLowerCase());


  }
  selectNaca(code){
    this.projecten[this.nacaModal.index].naca = code;
    this.nacaModal.state = false;
  }

  pauzeModalOpen(index: number){
    if(this.readonly){return;}

    this.pauzeModal.state = true;
    this.pauzeModal.index = index;

  }
  selectPauze(p){
    this.projecten[this.pauzeModal.index].pauze = p;
    this.pauzeModal.state = false;
  }

  planningInit(){
    if(!this.user.values['urenregistratie_invoeren_planning'] || this.user.values["urenregistratie_invoeren_planning"].value !== 'aan'){ return; }
    if(this.type == 'ingevoerd'){return;}

    return new Promise(resolve => {
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/planning/urenregistratie`, {
        user: this.user.user_id.toString(),
        datum: this.dateUTC.toString(),
      })
        .then(response => {

          const { planning } = response.data;
          this.planning = planning;
          this.planningFill();
          resolve(null);
        })
        .catch(err => {
          this.infordb.handleError(err);
          resolve(null);
        })
    })
  }
  planningFill(){
    if(!this.planning.length){return;}

    this.projecten = [];

    for(const row of this.planning){
      const project = this.createProject();
      project.projectnummer = '-';
      project.begintijd = row.begin ? row.begin.slice(0, 5) : row.begin;
      project.eindtijd = row.eind ? row.eind.slice(0, 5) : row.eind;
      project['planning'] = true;
      project['label'] = '( Planning )';

      if(row.offerte && row.offerte.project){
        project.projectnummer = row.offerte.project.projectnr || '-';
        project.reisuren = row.offerte.project.reis || 0;

        const _project = this.projects.find(row => row.projectnr == project.projectnummer);
        project._taken = _project?.taken || [];
      }
      if(row.project){
        project.projectnummer = row.project.projectnr || '-';
        project.projectnaam = row.project.projectnaam || '-';
        project.opdrachtgever = row.project.opdrachtgever || '-';
        project.reisuren = row.project.reis || 0;


        const taken = row?.project?.taken || [];
        taken_loop: for(const taak of taken){
          for(const planning_taak of row.taken || []){
            if (planning_taak.id == taak.id){
              taak._checked = true;
              continue taken_loop;
            }
          }
        }
        project._taken = taken;
      }

      for(const planMachine of row.machines){
        const machine = this.createMachine();
        machine.id = planMachine.id;
        machine.name = planMachine.name;
        machine.begintijd = project.begintijd;
        machine.eindtijd = project.eindtijd;

        project.machines.push(machine);
      }

      this.projecten.push(project);
    }
  }

  werkbonnenInit(){
    if(!this.user.values['urenregistratie_invoeren_werkbonnen'] || this.user.values["urenregistratie_invoeren_werkbonnen"].value !== 'aan'){ return; }
    if(this.type == 'ingevoerd'){return;}

    return new Promise(resolve => {
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/werkbonnen/urenregistratie`, {
        api_token: localStorage.api_token,
        user: this.user.user_id.toString(),
        date: this.dateUTC.toString(),
      })
        .then(response => {
          if(response.status != 201){ throw response; }
          const { werkbonnen } = response.data;

          this.werkbonnen = werkbonnen;
          this.werkbonnenFill();
          resolve(null);
        })
        .catch(err => {
          this.infordb.handleError(err);
          resolve(null);
        })
    })
  }
  werkbonnenFill(){
    if(!this.werkbonnen.length){ return; }

    this.projecten = [];

    for(const werkbon of this.werkbonnen){
      const project = this.createProject();

      project.projectnummer = '-';
      project.label = `( Werkbon: ${werkbon.werkbonnummer} )`

      if (werkbon.project){
        project.projectnummer = werkbon.project.projectnr;

        const taken = werkbon?.project?.taken || [];
        taken_loop: for(const taak of taken){
          for(const werkbon_taak of werkbon.project_taken || []){
            if (werkbon_taak.id == taak.id){
              taak._checked = true;
              continue taken_loop;
            }
          }
        }
        project._taken = taken;

      }

      for(const item of werkbon.keywords){
        if(item.keyword == 'begintijd' && item.value){ project.begintijd = item.value.slice(0, 5); }
        if(item.keyword == 'eindtijd' && item.value){ project.eindtijd = item.value.slice(0, 5); }
      }


      this.projecten.push(project);
    }
  }

  pauze(index){
    if (this.type === 'ingevoerd') {return;}

    const project = this.projecten[index];

    if(project.gewerkteuren){
      const gewerkteuren = project.gewerkteuren;

      if (this.user.jobtype === 'kantoor') {
        if (gewerkteuren >= this.permissionValue('minimaal_lang_pauze_kantoor')) {
          project.pauze = this.permissionValue('pauze_kantoor_lang');
        }
        else if (gewerkteuren >= this.permissionValue('minimaal_kort_pauze_kantoor')) {
          project.pauze = this.permissionValue('pauze_kantoor_kort');
        }
        else {
          project.paue = 0;
        }
      }
      else if (this.user.jobtype === 'buitendienst') {
        if(gewerkteuren >= this.permissionValue('minimaal_lang_pauze_buiten')) {
          project.pauze = this.permissionValue('pauze_buiten_lang');
          return;
        }
        else if(gewerkteuren >= this.permissionValue('minimaal_kort_pauze_buiten')) {
          project.pauze = this.permissionValue('pauze_buiten_kort');
        }
        else {
          project.pauze = 0;
        }
      }
      return;
    }

    const start = new Date(project.begintijd).getTime();
    const end = new Date(project.eindtijd).getTime();

    if(!start || !end) {
      return;
    }
    if(end < start) {
      alert('De eindtijd ligt voor de begintijd.');
      return;
    }

    const diff = (end - start) / 3600 / 1000;

    if (this.user.jobtype ===' "kantoor"') {
      if (diff >= this.permissionValue('minimaal_lang_pauze_kantoor')) {
        project.pauze = this.permissionValue('pauze_kantoor_lang');
      }
      else if (diff >= this.permissionValue('minimaal_kort_pauze_kantoor')) {
        project.pauze = this.permissionValue('pauze_kantoor_kort');
      }
      else {
        project.pauze = 0;
      }
    }
    else if (this.user.jobtype === 'buitendienst') {
      if(diff >= this.permissionValue('minimaal_lang_pauze_buiten')) {
        project.pauze = this.permissionValue('pauze_buiten_lang');
      }
      else if(diff >= this.permissionValue('minimaal_kort_pauze_buiten')) {
        project.pauze = this.permissionValue('pauze_buiten_kort');
      }
      else {
        project.pauze = 0;
      }
    }

  }
  order(){
    for(const i in this.projecten){
      for(let x = 0; x < this.projecten.length; x++){
        if(!this.projecten[x + 1]){continue;}
        const thisTime = new Date(this.projecten[x].begintijd).getTime();
        const nextTime = new Date(this.projecten[x + 1].begintijd).getTime();

        if(thisTime > nextTime){
          const temp = this.projecten[x];
          this.projecten[x] = this.projecten[x + 1];
          this.projecten[x + 1] = temp;
        }

      }
    }
  }

  hasPermission(permission: string): boolean {
    let found = false;
    this.user.settings.forEach(setting => {
      if (setting.permission.setting == permission) { found = true; }
    });
    return found;
  }
  permissionValue(permission: string): any {
    let p = 0;
    this.user.settings.forEach(setting => {
      if (setting.permission.setting == permission) {
        p = setting.value;
      }
    });
    return p;
  }
  roleCanDo(permission: string): boolean {
    let found = false;
    this.user.permissions.forEach(perm => {
      if (perm.permission == permission) {
        found = true;
      }
    });

    return found;
  }

  dateToDate(d): string {
    const arr = d.split('-');
    const date = new Date(Number(arr[2]), Number(arr[1]) - 1, Number(arr[0]));
    return date.getFullYear() + '-' + (`0${date.getMonth() + 1}`).slice(-2) + '-' + (`0${date.getDate()}`).slice(-2);
  }
  formatDate(d): string {
    const arr = d.split('-');
    return (`0${arr[2]}`).slice(-2) + '-' + (`0${arr[1]}`).slice(-2) + '-' + arr[0];
  }
  timeToIso(time: string) {
    const d = new Date();
    const t = time.split(':');
    const dateString = d.getFullYear() + '-' + (`0${d.getMonth() + 1}`).slice(-2) + '-' + (`0${d.getDate()}`).slice(-2);
    return `${dateString}T${t[0]}:${t[1]}:00.000+02:00`;
  }
  fullTimeToHi(time: string) {
    const t = (time ?? '00:00:00').split(':');
    return t[0] + ':' + t[1];
  }
  getDateDiff(startDate, endDate) {
    const diff = endDate.getTime() - startDate.getTime();
    const days = Math.floor(diff / (60 * 60 * 24 * 1000));
    const hours = Math.floor(diff / (60 * 60 * 1000)) - (days * 24);
    const minutes = Math.floor(diff / (60 * 1000)) - ((days * 24 * 60) + (hours * 60));
    return { hours, minutes };
  }
  callPage(page){
    this.router.navigate([page]);
  }

  protected readonly parseInt = parseInt;
}
