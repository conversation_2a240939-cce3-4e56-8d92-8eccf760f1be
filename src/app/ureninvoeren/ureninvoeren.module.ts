import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { UreninvoerenPage } from './ureninvoeren.page';
// import { ProjectListPage } from '../project-list/project-list.page';
// import { NacalistPage } from '../nacalist/nacalist.page';

const routes: Routes = [
  {
    path: '',
    component: UreninvoerenPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    RouterModule.forChild(routes),
  ],
  declarations: [
    UreninvoerenPage,
    // ProjectListPage,
    // NacalistPage
  ]
})
export class UreninvoerenPageModule {}
