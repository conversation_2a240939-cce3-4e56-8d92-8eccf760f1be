import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UreninvoerenPage } from './ureninvoeren.page';

describe('UreninvoerenPage', () => {
  let component: UreninvoerenPage;
  let fixture: ComponentFixture<UreninvoerenPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ UreninvoerenPage ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UreninvoerenPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
