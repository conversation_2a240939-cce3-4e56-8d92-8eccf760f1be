<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Aanvragen</ion-title>

    <ion-buttons slot="end" *ngIf="user.user_permissions['Aanvragen beheren']" >
      <ion-button (click)="formulieren.modal = true" color="primary" class="mx-2" >
        <ion-icon class="font-size-15" name="add"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)" >
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post && aanvragen.length === 0" class="ion-text-center ion-margin" >
    <h4>Geen aanvragen gevonden</h4>
  </div>

  <div *ngIf="post" >

    <ion-card>
      <ion-list>
        <ion-item lines="none" >
          <ion-label position="floating" >Zoeken</ion-label>
          <ion-input [(ngModel)]="search" ></ion-input>
        </ion-item>
      </ion-list>
    </ion-card>

    <div *ngIf="aanvragen && aanvragen.length !== 0" >
      <div *ngFor="let aanvraag of aanvragen" >
        <ion-card class="ion-padding" *ngIf="!search || aanvraag.search.includes(search)" >
          <div class="d-flex ion-justify-content-between ion-align-items-start">
            <div class="w-100" >
              <ion-card-title class="text" >{{aanvraag.aanvraagnummer}}</ion-card-title>
              <ion-card-subtitle *ngIf="aanvraag.active === '0'" class="text-warning opacity-75 d-flex ion-align-items-center" >
                <ion-icon name="alert-circle-outline"></ion-icon>
                <span class="mx-1">Aanvraag inactief</span>
              </ion-card-subtitle>
              <div class="my-1">
                <span>Status</span>
                <h6 class="m-0 text">{{aanvraag.status_intern}}</h6>
              </div>
              <div class="my-1">
                <span>Datum</span>
                <h6 class="m-0 text">{{convertDate(aanvraag.datum)}}</h6>
              </div>
              <div class="my-1">
                <span>Methode</span>
                <h6 class="m-0 text">{{aanvraag.methode}}</h6>
              </div>
              <div *ngIf="aanvraag.klant" >
                <div class="my-1">
                  <span>Klant</span>
                  <h6 class="m-0 text" *ngIf="aanvraag.klant.naam">{{aanvraag.klant.naam}}</h6>
                  <h6 class="m-0 text" *ngIf="!aanvraag.klant.naam">{{aanvraag.klant.contactpersoon_voornaam}} {{aanvraag.klant.contactpersoon_achternaam}}</h6>
                </div>
                <div class="my-1" *ngIf="aanvraag.klant.straat || aanvraag.klant.postcode || aanvraag.klant.plaats">
                  <span>Adres</span>
                  <div *ngIf="aanvraag.locatie" >
                    <h6 class="m-0 text">{{aanvraag.locatie.straat}} {{aanvraag.locatie.huisnummer}}{{aanvraag.locatie.toevoeging}},</h6>
                    <h6 class="m-0 text">{{aanvraag.locatie.postcode}} {{aanvraag.locatie.plaats}}</h6>
                  </div>
                  <div *ngIf="!aanvraag.locatie" >
                    <h6 class="m-0 text">{{aanvraag.klant.straat}} {{aanvraag.klant.huisnummer}}{{aanvraag.klant.toevoeging}},</h6>
                    <h6 class="m-0 text">{{aanvraag.klant.postcode}} {{aanvraag.klant.plaats}}</h6>
                  </div>
                </div>
              </div>
              <div class="my-1" *ngIf="aanvraag.vestiging">
                <span>Vestiging</span>
                <div>
                  <h6 class="m-0 text">{{aanvraag.vestiging.straat}} {{aanvraag.vestiging.huisnummer}}{{aanvraag.vestiging.toevoeging}},</h6>
                  <h6 class="m-0 text">{{aanvraag.vestiging.postcode}} {{aanvraag.vestiging.plaats}}</h6>
                </div>
              </div>
              <div *ngIf="user.user_permissions['Aanvragen beheren'] && aanvraag.users.length" >
                <span>Medewerkers</span>
                <h6 class="m-0 text" *ngFor="let user of aanvraag.users" ><b>-&nbsp;</b>{{user.name}} {{user.lastname}}</h6>
              </div>
            </div>
            <div>

             <div *ngIf="aanvraag.klant" >
               <ion-button class="d-block" (click)="klant(aanvraag.klant_id)" >
                 <ion-icon name="person-outline"></ion-icon>
               </ion-button>
               <small class="button-footer" >Klant</small>
             </div>
              <div>
                <ion-button class="d-block m-1" (click)="inzien(aanvraag.id)" >
                  <ion-icon name="eye-outline"></ion-icon>
                </ion-button>
                <small class="button-footer" >Inzien</small>
              </div>
              <div>
                <ion-button class="d-block m-1" (click)="status(aanvraag.id)" >
                  <ion-icon name="bookmark-outline"></ion-icon>
                </ion-button>
                <small class="button-footer" >Status</small>
              </div>
              <div *ngIf="modules['Checklists'] && user.user_permissions['Checklists aanmaken']">
                <ion-button class="d-block m-1"  (click)="selectChecklist(aanvraag.id)" >
                  <ion-icon name="file-tray-full-outline"></ion-icon>
                </ion-button>
                <small class="button-footer">Checklist</small>
              </div>
              <div *ngIf="modules['Offertes'] && user.user_permissions['Offertes aanmaken']" >
                <ion-button class="d-block m-1"  (click)="offerte(aanvraag.id)" >
                  <ion-icon name="clipboard-outline"></ion-icon>
                </ion-button>
                <small class="button-footer">Offerte</small>
              </div>
              <div *ngIf="modules['Projecten'] && user.user_permissions['Projecten toevoegen']" >
                <ion-button class="d-block m-1"  (click)="project(aanvraag.id)" >
                  <ion-icon name="folder-open-outline"></ion-icon>
                </ion-button>
                <small class="button-footer">Project</small>
              </div>

            </div>
          </div>
        </ion-card>
      </div>
    </div>
  </div>

  <div class="modal-container" *ngIf="checklistModal" (click)="checklistModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" color="none" >
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div class="ion-padding overflow-auto mh-25-vh">
        <div *ngFor="let template of checklistTemplates">
          <div class="ion-text-center"  >
            <ion-button (click)="checklist(template.id)" class="text" fill="clear" ><ion-text class="text">{{template.name}}</ion-text></ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

  <div class="modal-container" *ngIf="statusModal" (click)="statusModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <div class="ion-padding overflow-auto mh-25-vh">
        <div *ngFor="let status of statussen">
          <div class="ion-text-center"  >
            <ion-button *ngIf="statusAanvraag.status_intern != status.name" (click)="selectStatus(statusAanvraag.id, status.name)" class="text" fill="clear" ><ion-text class="text">{{status.name}}</ion-text></ion-button>
            <ion-button *ngIf="statusAanvraag.status_intern == status.name" class="text opacity-50" fill="clear" ><ion-text class="text">{{status.name}}</ion-text></ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

  <div class="modal-container" *ngIf="formulieren.modal" (click)="formulieren.modal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <div class="ion-padding overflow-auto mh-25-vh">
        <div *ngFor="let form of formulieren.formulieren;let index = index">
          <div class="ion-text-center"  >
            <ion-button (click)="newAanvraag(index)" class="text" fill="clear" ><ion-text class="text">{{form.name}}</ion-text></ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

</ion-content>
