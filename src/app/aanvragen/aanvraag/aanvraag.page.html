<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="aanvraag">{{aanvraag.aanvraagnummer}}</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <ion-card>

  </ion-card>

  <div *ngIf="aanvraag" >
    <ion-card *ngIf="aanvraag">
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag.vestiging" >
        <ion-label position="floating" >Vestiging</ion-label>
        <ion-input [value]="aanvraag.vestiging.name" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" >
        <ion-label position="floating" >Methode</ion-label>
        <ion-input [value]="aanvraag.methode" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag._bv" >
        <ion-label position="floating" >BV</ion-label>
        <ion-input [value]="aanvraag._bv.name" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" >
        <ion-label position="floating" >Status</ion-label>
        <ion-input [value]="aanvraag.status_intern" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" >
        <ion-label position="floating" >Datum</ion-label>
        <ion-input [value]="convertDate(aanvraag.datum)" readonly="true"></ion-input>
      </ion-item>
    </ion-card>

    <ion-card *ngIf="aanvraag.klant" >
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag.klant.naam" >
        <ion-label position="floating" >Bedrijfsnaam</ion-label>
        <ion-input [value]="aanvraag.klant.naam" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag.klant.naam" >
        <ion-label position="floating" >Contactpersoon</ion-label>
        <ion-input [value]="aanvraag.contactpersoon ? (aanvraag.contactpersoon.voornaam || '')+' '+(aanvraag.contactpersoon.achternaam || '') : (aanvraag.klant.contactpersoon_voornaam || '')+' '+(aanvraag.klant.contactpersoon_achternaam || '')" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag.klant.naam" >
        <ion-label position="floating" >Email</ion-label>
        <ion-input [value]="aanvraag.klant.email || ''" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag.klant.naam" >
        <ion-label position="floating" >Telefoonnummer</ion-label>
        <ion-input [value]="aanvraag.klant.telefoonnummer" readonly="true"></ion-input>
      </ion-item>
      <ion-item color="none" lines="none" class="border-bottom" *ngIf="aanvraag.klant.naam" >
        <ion-label position="floating" >Werkadres</ion-label>
        <ion-input [value]="aanvraag.locatie ? (aanvraag.locatie.straat || '')+' '+(aanvraag.locatie.huisnummer || '')+(aanvraag.locatie.toevoeging || '')+', '+(aanvraag.locatie.postcode || '')+' '+(aanvraag.locatie.plaats || '')
                                             : (aanvraag.klant.straat || '')+' '+(aanvraag.klant.huisnummer || '')+(aanvraag.klant.toevoeging || '')+', '+(aanvraag.klant.postcode || '')+' '+(aanvraag.klant.plaats || '')" readonly="true"></ion-input>
      </ion-item>
    </ion-card>

    <ion-card *ngIf="aanvraag.omschrijving" class="p-2 text-black" >
      <span>Omschrijving</span>
      <div class="font-size-1" [innerHTML]="aanvraag.omschrijving" ></div>
    </ion-card>

    <ion-card>
      <div *ngFor="let value of aanvraag.teksten" >

        <ion-item color="none" lines="none" class="border-bottom" *ngIf="value.type == 'Datum'" >
          <ion-label position="floating" >{{value.naam}}</ion-label>
          <ion-input [value]="convertDate(value.value)" readonly="true"></ion-input>
        </ion-item>
        <div class="p-2 text-black border-bottom" *ngIf="value.type == 'Tekst editor'" >
          <span>{{value.naam}}</span>
          <div class="font-size-1" [innerHTML]="value.value" ></div>
        </div>
        <div class="p-2 text-black border-bottom" *ngIf="value.type == 'Bestand'" >
          <span>{{value.naam}}</span>
          <div><a class="btn btn-inverse-primary w-100" (click)="download(value.value)" >DOWNLOAD</a></div>
        </div>

        <ion-item color="none" lines="none" class="border-bottom" *ngIf="value.type != 'Datum' && value.type != 'Tekst editor' && value.type != 'Bestand'" >
          <ion-label position="floating" >{{value.naam}}</ion-label>
          <ion-input [value]="value.value" readonly="true"></ion-input>
        </ion-item>
      </div>

    </ion-card>
  </div>

</ion-content>
