import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';
import { Router } from '@angular/router';
import {NavController} from '@ionic/angular';

@Component({
  selector: 'app-aanvraag',
  templateUrl: './aanvraag.page.html',
  styleUrls: ['./aanvraag.page.scss'],
	providers: [Infordb]
})
export class AanvraagPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public aanvraagId = window.localStorage.getItem('aanvraagId');
  public aanvraag;
  public post = false;

  constructor(
      private infordb: Infordb,
      public router: Router,
      public nav: NavController
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/aanvragen/show`, {
      api_token: localStorage.api_token,
      id: this.aanvraagId,
    }, {})
      .then(response => {
          this.post = true;
          if (response.status == 201) {
            this.aanvraag = response.data.aanvraag;
          }
        })
      .catch(err => {
        this.post = true;
        alert('Er is iets misgegaan, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + JSON.stringify(err.status));
      });

  }
  download(src){
    const link = 'https://' + this.subdomain + '.ikbentessa.nl/api/file/' + src;
    window.open(link);
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + date.getFullYear();
  }

}
