import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';
import {Router} from '@angular/router';

@Component({
  selector: 'app-new',
  templateUrl: './new.page.html',
  styleUrls: ['./new.page.scss'],
	providers: [Infordb]
})
export class NewPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));

  public post = false;
  public btn = true;

  public form;
  public klanten = [];
  public contactpersonen = [];
  public locaties = [];
  public vestigingen = [];

  public userModal = {
    state: false,
    search: '',
  };
  public vestigingModal = {
    state: false,
    search: '',
  };
  public klantenModal = {
    state: false,
    search: '',
  };
  public cpModal = {
    state: false,
    search: '',
  };
  public locatieModal = {
    state: false,
    search: '',
  };

  public aanvraag = {
    user_id: null,
    vestiging_id: null,
    klant_id: null,
    klant: '',
    contactpersoon_id: null,
    locatie_id: null,
    methode: 'App',
    omschrijving: '',
    inputs: [],
    temp: {
      user: '',
      klant: '',
      vestiging: '',
      contactpersoon: '',
      locatie: '',
    }
  };

  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/aanvragen/new`, {
      form: localStorage.new_aanvraag,
    }, {})
      .then(response => {
        this.post = true;
        if (response.status == 201) {
          const { form, klanten, vestigingen } = response.data;
          this.form = form;
          this.klanten = klanten;
          this.vestigingen = vestigingen;
          this.post = true;
					console.log(form.inputs);
        }
      })
      .catch(this.infordb.handleError);
  }

  onSubmit(){
    this.aanvraag.inputs = this.form.inputs;

    if(!this.aanvraag.klant_id){
      alert('Klant kan niet leeg zijn!');
      return;
    }
    for(const input of this.aanvraag.inputs){
      if(!Number(input.required)){continue;}

      if(!input.value){
        alert(`${input.naam} is een verplichte veld`);
        return;
      }
    }

    this.btn = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/aanvragen/store`, {
      aanvraag: JSON.stringify(this.aanvraag),
    })
      .then(response => {
        this.btn = true;
        alert('Aanvraag succesvol opgeslagen');
        this.callPage('aanvragen');
      })
      .catch(err => {
        this.btn = true;
        this.infordb.handleError(err);
      });

  }

  userSearch(user){
    const search = (`${user.name || ''} ${user.lastname || ''} `).toLowerCase();
    return search.includes(this.userModal.search.toLowerCase());
  }
  selectUser(user){
    this.aanvraag.user_id = user.id;
    this.aanvraag.temp.user = `${user.name || ''} ${user.lastname || ''}`;
    this.userModal.search = '';
    this.userModal.state = false;
  }

  vestigingSearch(vestiging){
    const search = (`${vestiging.straat || ''} ${vestiging.huisnummer || ''}${vestiging.toevoeging || ''} ${vestiging.postcode || ''} ${vestiging.plaats || ''} `).toLowerCase();
    return search.includes(this.klantenModal.search.toLowerCase());
  }
  selectVestiging(vestiging){
    this.aanvraag.vestiging_id = vestiging.id;
    this.aanvraag.temp.vestiging = (`${vestiging.straat || ''} ${vestiging.huisnummer || ''}${vestiging.toevoeging || ''} ${vestiging.postcode || ''} ${vestiging.plaats || ''} `);
    this.vestigingModal.state = false;
    this.vestigingModal.search = '';
  }

  klantenSearch(klant){
    const search = (`${klant.contactpersoon_voornaam || ''} ${klant.contactpersoon_achternaam || ''} ${klant.naam || ''}`).toLowerCase();

    return search.includes(this.klantenModal.search.toLowerCase());
  }
  selectKlant(klant){
    this.aanvraag.klant_id = klant.id;
    this.aanvraag.temp.klant = klant.naam || (klant.contactpersoon_voornaam || '')+' '+(klant.contactpersoon_achternaam || '');

    this.contactpersonen = [];
    if(klant.contactpersoon_voornaam || klant.contactpersoon_achternaam){
      this.contactpersonen.push({
        name: (klant.contactpersoon_voornaam || '')+' '+(klant.contactpersoon_achternaam || ''),
        value: 0,
      });
    }
    for(const cp of klant.contactpersonen){
      this.contactpersonen.push({
        name: (cp.voornaam || '')+' '+(cp.achternaam || ''),
        value: cp.id,
      });
    }

    this.locaties = [];
    if(klant.straat || klant.postcode || klant.plaats){
      this.locaties.push({
        name: `${klant.straat || ''} ${klant.huisnummer || ''}${klant.toeveoging || ''}, ${klant.plaats}`,
        value: 0
      });
    }
    for(const locatie of klant.locaties){
      this.locaties.push({
        name: `${locatie.straat || ''} ${locatie.huisnummer || ''}${locatie.toeveoging || ''}, ${locatie.plaats}`,
        value: locatie.id
      });
    }

    this.klantenModal.state = false;
    this.klantenModal.search = '';
  }

  cpSearch(cp){
    const search = cp.name.toLowerCase();
    return search.includes(this.cpModal.search.toLowerCase());
  }
  selectCp(cp){
    this.aanvraag.contactpersoon_id = cp.value;
    this.aanvraag.temp.contactpersoon = cp.name;
    this.cpModal.state = false;
    this.cpModal.search = '';
  }

  locatieSearch(locatie){
    const search = locatie.name.toLowerCase();
    return search.includes(this.locatieModal.search.toLowerCase());
  }
  selectLocatie(locatie){
    this.aanvraag.locatie_id = locatie.value;
    this.aanvraag.temp.locatie = locatie.name;
    this.locatieModal.state = false;
    this.locatieModal.search = '';
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

}
