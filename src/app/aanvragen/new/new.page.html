<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Aanvragen</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

    <div *ngIf="!post" class="ion-text-center p-2" >
      <ion-spinner></ion-spinner>
    </div>

    <div *ngIf="post" >
      <ion-card>
        <div class="flex-between border-bottom pl-2">
          <ion-text class="text-input">Projectleider:</ion-text>
          <ion-text (click)="userModal.state = true;" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="!aanvraag.user_id" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="aanvraag.user_id" >{{aanvraag.temp.user}}</span>
            </ion-button>
          </ion-text>
        </div>
        <div class="flex-between border-bottom pl-2">
          <ion-text class="text-input">Vestiging:</ion-text>
          <ion-text (click)="vestigingModal.state = true;" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="!aanvraag.vestiging_id" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="aanvraag.vestiging_id" >{{aanvraag.temp.vestiging}}</span>
            </ion-button>
          </ion-text>
        </div>
        <div class="flex-between border-bottom pl-2">
          <ion-text class="text-input">Klant*:</ion-text>
          <ion-text (click)="klantenModal.state = true;" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="!aanvraag.klant_id" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="aanvraag.klant_id" >{{aanvraag.temp.klant}}</span>
            </ion-button>
          </ion-text>
        </div>
        <div class="flex-between border-bottom pl-2" [ngClass]="!aanvraag.klant_id ? 'opacity-50 pointer-event-none' : ''">
          <ion-text class="text-input">Contactpersoon:</ion-text>
          <ion-text (click)="cpModal.state = true;" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="aanvraag.contactpersoon_id === null" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="aanvraag.contactpersoon_id !== null" >{{aanvraag.temp.contactpersoon}}</span>
            </ion-button>
          </ion-text>
        </div>
        <div class="flex-between border-bottom pl-2" [ngClass]="!aanvraag.klant_id ? 'opacity-50 pointer-event-none' : ''">
          <ion-text class="text-input">Werkadres:</ion-text>
          <ion-text (click)="locatieModal.state = true;" class="ion-text-right" >
            <ion-button fill="clear" >
              <ion-icon *ngIf="aanvraag.locatie_id === null" name="swap-horizontal-outline"></ion-icon>
              <span *ngIf="aanvraag.locatie_id !== null" >{{aanvraag.temp.locatie}}</span>
            </ion-button>
          </ion-text>
        </div>
      </ion-card>

      <ion-card>
        <div *ngFor="let input of form.inputs" class="content-bg-unset" >

          <ion-item *ngIf="input.type == 'Tekst'" lines="none" color="none" class="border-bottom" >
            <ion-label>{{input.naam}}<span *ngIf="input.required === '1'">*</span></ion-label>
            <ion-input  class="ion-text-right" [(ngModel)]="input.value"></ion-input>
          </ion-item>
          <ion-item *ngIf="input.type == 'Getal'" lines="none" color="none" class="border-bottom" >
            <ion-label>{{input.naam}}<span *ngIf="input.required === '1'">*</span></ion-label>
            <ion-input type="number" class="ion-text-right" [(ngModel)]="input.value"></ion-input>
          </ion-item>
          <ion-item *ngIf="input.type == 'Datum'" lines="none" color="none" class="border-bottom" >
            <ion-label>{{input.naam}}<span *ngIf="input.required === '1'">*</span></ion-label>
            <ion-datetime displayFormat="DD-MM-YYYY" pickerFormat="YYYY-MM-DD" max="2030-12-31" class="ion-text-right" [(ngModel)]="input.value"></ion-datetime>
          </ion-item>
          <ion-item *ngIf="input.type == 'Tijd'" lines="none" color="none" class="border-bottom" >
            <ion-label>{{input.naam}}<span *ngIf="input.required === '1'">*</span></ion-label>
            <ion-datetime displayFormat="HH:mm" pickerFormat="HH:mm" class="ion-text-right" [(ngModel)]="input.value"></ion-datetime>
          </ion-item>
          <div *ngIf="input.type == 'Tekst editor'" class="border-bottom py-2" >
            <span class="p-2 font-size-1 text-black" >{{input.naam}}<span *ngIf="input.required === '1'">*</span></span>
            <div class="p-2" >
              <textarea [froalaEditor] [(ngModel)]="input.value"></textarea>
            </div>
          </div>
        </div>
      </ion-card>

      <ion-card>
        <div  class="border-bottom py-2" >
          <span class="p-2 font-size-1 text-black" >Omschrijving</span>
          <div class="p-2" >
            <textarea [froalaEditor] [(ngModel)]="aanvraag.omschrijving"></textarea>
          </div>
        </div>
      </ion-card>

      <div class="p-1 ion-text-center">
        <a class="btn btn-success w-100" *ngIf="btn" (click)="onSubmit();" >Opslaan</a>
        <ion-spinner *ngIf="!btn" color="success"></ion-spinner>
      </div>
    </div>

</ion-content>

<!--  Select user-->
<div class="modal-container" *ngIf="userModal.state" (click)="userModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="userModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let row of user.users" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="userSearch(row)" fill="clear" (click)="selectUser(row)" >
          <div class="py-1" >
            <span class="d-block" >{{row.name || ''}} {{row.lastname || ''}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select vestiging-->
<div class="modal-container" *ngIf="vestigingModal.state" (click)="vestigingModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="vestigingModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let vestiging of vestigingen" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="vestigingSearch(vestiging)" fill="clear" (click)="selectVestiging(vestiging)" >
          <div class="py-1" >
            <span class="d-block" >{{vestiging.straat || ''}} {{vestiging.huisnummer || ''}}{{vestiging.toevoeging || ''}}, {{vestiging.plaats}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select klant-->
<div class="modal-container" *ngIf="klantenModal.state" (click)="klantenModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="klantenModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let klant of klanten" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="klantenSearch(klant)" fill="clear" (click)="selectKlant(klant)" >
          <div class="py-1" >
            <span class="d-block" >{{klant.naam || (klant.contactpersoon_voornaam || '')+' '+(klant.contactpersoon_achternaam || '')}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select contactpersoon-->
<div class="modal-container" *ngIf="cpModal.state" (click)="cpModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="cpModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let cp of contactpersonen" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="cpSearch(cp)" fill="clear" (click)="selectCp(cp)" >
          <div class="py-1" >
            <span class="d-block" >{{cp.name}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Select locatie-->
<div class="modal-container" *ngIf="locatieModal.state" (click)="locatieModal.state = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item>
      <ion-label position="floating" >Zoeken</ion-label>
      <ion-input [(ngModel)]="locatieModal.search" class="ion-text-left" ></ion-input>
    </ion-item>
    <div class="ion-padding overflow-auto mh-33-vh">
      <div *ngFor="let locatie of locaties" class="ion-text-center mb-1">
        <ion-button class="h-auto ion-text-wrap" *ngIf="locatieSearch(locatie)" fill="clear" (click)="selectLocatie(locatie)" >
          <div class="py-1" >
            <span class="d-block" >{{locatie.name}}</span>
          </div>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>
