import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { AanvragenPage } from './aanvragen.page';

const routes: Routes = [
  {
    path: '',
    component: AanvragenPage
  },
  {
    path: 'cdn',
    loadChildren: () => import('./cdn/cdn.module').then( m => m.CdnPageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AanvragenPageRoutingModule {}
