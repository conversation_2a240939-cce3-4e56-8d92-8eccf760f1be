import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from "infordb";

@Component({
  selector: 'app-aanvragen',
  templateUrl: './aanvragen.page.html',
  styleUrls: ['./aanvragen.page.scss'],
	providers: [Infordb]
})
export class AanvragenPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public post = false;
  public search: string;
  public aanvragen;
  public statussen = [];
  public modules = {};

  public formulieren = {
    modal: false,
    formulieren: [],
  };


  public statusAanvraag;
  public statusModal = false;

  public checklistAanvraag;
  public checklistTemplates;
  public checklistModal = false;

  constructor(
    private infordb: Infordb,
    public router: Router,
  ) { }

  ngOnInit() {
    for(const module of this.user.modules){
      this.modules[module.module.name] = true;
    }
  }
  ionViewWillEnter(){
    this.post = false;
    this.aanvragen = [];

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/aanvragen/get`, {
      user: this.user.user_id.toString(),
    })
      .then(response => {
        this.post = true;
        if (response.status == 201) {
          const {aanvragen, statussen, checklistTemplates, formulieren} = response.data;
          this.aanvragen = aanvragen;
          this.statussen = statussen;
          this.checklistTemplates = checklistTemplates;
          this.formulieren.formulieren = formulieren;
          this.setSearch();
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }

  doRefresh(event) {
    this.ngOnInit();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  inzien(id){
    window.localStorage.setItem('aanvraagId', id);
    this.callPage('aanvraag');
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() +1)).slice(-2) + '-' + date.getFullYear();
  }

  klant(id){
    localStorage.setItem('klantId', id);
    try {
      this.router.navigate([`/klant`]);
    } catch (e) {
      alert(e);
    }
  }
  offerte(id){
    localStorage.iframeRoute = `iframe_offertes_select`;
    localStorage.iframeData = JSON.stringify({
      aanvraag: id,
    });
    this.callPage('iframe');
  }
  project(id){console.log(this.aanvragen.find(a => a.id === id))
    localStorage.project_aanvraag = JSON.stringify(this.aanvragen.find(a => a.id === id));
    this.callPage('projecten/create');
  }

  setSearch(){
    for(const aanvraag of this.aanvragen){
      let search = '';
      search = aanvraag.aanvraagnummer + (aanvraag.status_intern || '') + aanvraag.methode + this.convertDate(aanvraag.datum);

      if(!aanvraag.klant){
        aanvraag.search = search.toLowerCase();
        continue;
      }

      search += aanvraag.klant.naam || '';
      search += aanvraag.klant.contactpersoon_voornaam || '';
      search += aanvraag.klant.contactpersoon_achternaam || '';
      search += aanvraag.klant.straat || '';
      search += aanvraag.klant.huisnummer || '';
      search += aanvraag.klant.toevoeging || '';
      search += aanvraag.klant.postcode || '';
      search += aanvraag.klant.plaats || '';

      if(!this.user.user_permissions['Aanvragen beheren']){
        aanvraag.search = search.toLowerCase();
        continue;
      }

      for(const user of aanvraag.users){
        aanvraag.search += user.name;
        aanvraag.search += user.lastname;
      }

      aanvraag.search = search.toLowerCase();
    }
  }

  selectChecklist(id){
    this.checklistAanvraag = id;
    this.checklistModal = true;
  }
  checklist(template){
    localStorage.checklistTemplateId = template;
    localStorage.checklistAanvraagId = this.checklistAanvraag;
    this.callPage('checklists/new');
    this.checklistModal = false;
  }

  newAanvraag(i){
    this.formulieren.modal = false;

    const form = this.formulieren.formulieren[i];
    localStorage.new_aanvraag = form.token;
    this.callPage('aanvragen/new');
  }

  status(id){
    for(const aanvraag of this.aanvragen){
      if(aanvraag.id === id){
        this.statusAanvraag = aanvraag;
      }
    }
    this.statusModal = true;
  }
  selectStatus(id, status){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/aanvragen/status/select`, {
      api_token: localStorage.api_token,
      id: id,
      status: status,
    }, {})
      .then(response => {
        this.statusAanvraag.status_intern = status;
        this.statusModal = false;
      })
      .catch(err => {
        this.infordb.handleError(err);
      });
  }

}
