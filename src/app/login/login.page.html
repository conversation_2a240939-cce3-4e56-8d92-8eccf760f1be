<ion-header>
  <ion-toolbar>
    <div class="d-flex ion-justify-content-between ion-align-items-center mx-2" >
      <h4 class="m-0">Tessa!</h4>
      <div class="flex-between">
        <span class="m-0" *ngIf="version[os]" >v{{version[os]}}</span>
        <ion-button (click)="callPage('settings')" fill="clear" color="dark"><ion-icon name="cog-outline"></ion-icon></ion-button>
      </div>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content padding>
  <div class="ion-text-center h-25" >
    <img class="center my-1" [src]="logo" height="100%">
  </div>
  <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">

    <ion-item >
      <ion-label (click)="subdomain.setFocus()">Subdomein</ion-label>
      <ion-input class="ion-text-end" [(ngModel)]="subdomain" formControlName="subdomain" type="text" name="subdomain"></ion-input>
    </ion-item>

    <ion-item >
      <ion-label (click)="email.focus()">Email</ion-label>
      <ion-input class="ion-text-end" [(ngModel)]="email" formControlName="email" type="email" name="email"></ion-input>
    </ion-item>

    <ion-item >
      <ion-label (click)="this.password.focus()">Wachtwoord</ion-label>
      <ion-input class="ion-text-end" [(ngModel)]="password" formControlName="password" type="password" name="password"></ion-input>
    </ion-item>

    <div class="ion-text-center" >
      <ion-button *ngIf="!autoLog.init" [disabled]="disabled" style="margin-top: 15px" type="submit" expand="full">Inloggen</ion-button>
      <div *ngIf="autoLog.init" class="my-2" >
        <ion-spinner *ngIf="autoLog.init" color="primary" class="my-2" ></ion-spinner>
        <ion-text class="d-block text-primary" >Inloggen</ion-text>
      </div>

      <p *ngIf="autoLog.status" class="text-danger" >{{autoLog.status}}</p>
    </div>
  </form>

  <div class="modal-container" *ngIf="updateModal" >
    <ion-card class="ion-padding" >
      <div class="my-3 mx-2" >
        <div class="d-flex my-2">
          <div>
            <img width="100" src="assets/logo.png" >
          </div>
          <div class="w-100" >
            <div class="m-1" >
              <h3 class="m-0 text" >Nieuwe update beschikbaar!</h3>
              <h6 class="m-0" *ngIf="os === 'android' && mainSettings['app_version_android']" >Versie {{mainSettings['app_version_android'].value}}</h6>
              <h6 class="m-0" *ngIf="os === 'ios' && mainSettings['app_version_ios']" >Versie {{mainSettings['app_version_ios'].value}}</h6>
            </div>
          </div>
        </div>
        <div class="ion-text-center my-2" >
          <button *ngIf="os === 'android'" (click)="openStore()" class="btn btn-playsotre w-100" >UPDATE</button>
          <button *ngIf="os === 'ios'" (click)="openStore()" class="btn btn-applestore w-100" >UPDATE</button>
        </div>
      </div>
      <div class="ion-text-right" >
        <ion-button fill="clear" class="text-primary" (click)="updateModal = false; autoLogin();"  ><small>Doorgaan op eigen risico</small></ion-button>
      </div>
    </ion-card>
  </div>

</ion-content>
