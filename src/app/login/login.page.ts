import {Component, OnInit, ViewChild} from '@angular/core';
import { AlertController, MenuController, Platform } from '@ionic/angular';
import { FormBuilder, FormGroup, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';
import { Device } from "@capacitor/device";
import {AccountsService} from "../services/Accounts/accounts.service";
import { Geolocation } from '@capacitor/geolocation';

import {
	ActionPerformed,
	PushNotificationSchema,
	PushNotifications,
	Token,
} from '@capacitor/push-notifications';
import {Camera} from "@capacitor/camera";

@Component({
	selector: 'app-login',
	templateUrl: './login.page.html',
	styleUrls: ['./login.page.scss'],
	providers: [ Infordb ]
})


export class LoginPage{

	public version = {
		ios: '6.2.0',
		android: '6.2.0',
	};

	public autoLog = {
		init: false,
		status: null,
	};
	device;
	updateModal = false;
	os;
	mainSettings;
	loginForm: FormGroup;
	subdomain;
	email: any;
	token: string;
	password: any;
	disabled = false;
	hash;
	width;
	pin = '';
	navigate_to = '/home'
	logo = '/assets/logo.png';

	constructor(
		public menuCtrl: MenuController,
		public router: Router,
		public atrCtrl: AlertController,
		private platform: Platform,
		public formBuilder: FormBuilder,
		private infordb: Infordb,
		private accountsService: AccountsService,
	) {
		this.loginForm = this.formBuilder.group({
			subdomain: new FormControl('', Validators.compose([
				Validators.required,
			])),
			email: new FormControl('', Validators.compose([
				Validators.required,
				Validators.minLength(6)
			])),
			password: new FormControl('', Validators.compose([
				Validators.required
			]))
		});
	}

	async ionViewWillEnter(){
		this.disabled = false;
		this.device = await Device.getInfo();

		if(this.platform.is('android')){
			this.os = 'android';
		}
		else if(this.platform.is('ios')){
			this.os = 'ios';
		}

		if(this.loginPrefill()){ return; }

		this.defineLogo();
		this.fetchSettings();

		await this.notificationsInit();
		await this.cameraInit();
		await this.locationInit();

		this.width = this.platform.width() * 0.5 / 3;
		const subdomain = window.localStorage.getItem('subdomain');
		const email  = window.localStorage.getItem('email');
		if (email && subdomain) {
			this.subdomain = subdomain;
			this.email = email;
		}
	}

	onSubmit() {
		this.disabled = true;
		this.autoLog.init = true;
		this.autoLog.status = '';

		this.subdomain = this.subdomain.replaceAll(' ', '');
		localStorage.subdomain = this.subdomain.replaceAll(' ', '');

		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/login`, {
			email: this.email,
			password: this.password,
			token: this.token ? this.token : 'no_token_given',
			brand: this.device?.manufacturer || 'Onbekend',
			model: this.device?.model || 'Onbekend',
			os: this.device?.platform || 'Onbekend',
			app_version: this.os == 'android' ? this.version.android : this.version.ios,
		})
			.then(response => {
				if(response.status != 201){ throw response; }

				this.disabled = false;
				this.autoLog.init = false;

				var user = response.data;
				const apiToken = user.api_token;
				user = JSON.stringify(user);
				window.localStorage.setItem('user', user);
				window.localStorage.setItem('email', this.email);
				window.localStorage.setItem('api_token', apiToken);

				this.accountsService.add(this.subdomain, { ...response.data, ...{password: this.password}  })
				this.password = "";

				this.callPage(this.navigate_to);
			})
			.catch(err => {
				this.disabled = false;
				this.autoLog.init = false;
				this.autoLog.status = 'Inloggen niet gelukt';
				this.infordb.handleError(err);
			});
	}

	loginPrefill(){
		if(!localStorage.login_prefill) {return false;}

		const account = JSON.parse(localStorage.login_prefill);
		localStorage.removeItem('login_prefill');

		this.subdomain = account?.subdomain;
		this.email = account?.email;
		this.password = account?.password;
		this.onSubmit();
		return true;
	}

	autoLogin(){
		if(!localStorage.user || !localStorage.subdomain) {return false;}

		let user = JSON.parse(localStorage.user);

		if(!user.values['app_automatisch_uitloggen'] || user.values['app_automatisch_uitloggen'].value !== 'Ja'){return false;}

		this.autoLog.init = true;

		this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/users/refresh-data`, {
			hash: user.password,
			token: this.token ? this.token : 'no_token_given',
			api_token: user.api_token,
			app_version: this.os == 'android' ? this.version.android : this.version.ios,
		})
			.then(response => {
				if (response.status == 201) {
					user = response.data;

					localStorage.api_token = user.api_token;
					localStorage.user = JSON.stringify(response.data);

					setTimeout(() => {
						this.disabled = false;
						this.autoLog.init = false;
						this.callPage(this.navigate_to);
					}, 350);
				}
			})
			.catch(() => {
				this.disabled = false;
				this.autoLog.init = false;
				this.autoLog.status = 'Automatisch inloggen niet gelukt!';
			});
	}

	defineLogo(){
		const date = new Date();
		const year = date.getFullYear();

		const zomer = {start: year+'-07-01', end: year+'-08-31', asset: '/assets/tessa_seasons/tessa_zomer.png'};
		const kerst = {start: year+'-12-20', end: (year+1)+'-01-01', asset: '/assets/tessa_seasons/tessa_kerst.png'};
		const koningsdag = {start: year+'-04-23', end: year+'-04-23', asset: '/assets/tessa_seasons/tessa_koningsdag.png'};
		const pasen = {start: year+'-04-10', end: year+'-04-22', asset: '/assets/tessa_seasons/tessa_pasen.png'};
		const halloween = {start: year+'-10-27', end: year+'-10-31', asset: '/assets/tessa_seasons/tessa_halloween.png'};
		const sinterklaas = {start: year+'-11-29', end: year+'-12-06', asset: '/assets/tessa_seasons/tessa_sinterklaas.png'};
		const valentijnsdag = {start: year+'-02-10', end: year+'-02-14', asset: '/assets/tessa_seasons/tessa_valentijnsdag.png'};

		const seasons = [zomer, kerst, koningsdag, pasen, halloween, sinterklaas, valentijnsdag];

		for(const season of seasons){
			if(this.isDateBetween(season.start, season.end)){
				this.logo = season.asset;
			}
		}
	}

	fetchSettings(){
		this.infordb.post(`https://infordb.ikbentessa.nl/api/settings`)
			.then(response => {
				if(response.status !== 201){ throw response; }

				const { data } = response;

				this.mainSettings = data.settings;
				this.checkVersion();
			})
			.catch(err => {
				this.autoLogin();
				this.infordb.notification({message: 'Nieuwste versie kon niet worden opgehaald.'});
			});
	}

	checkVersion(){
		if(this.platform.is('android')){
			if(this.mainSettings['app_version_android'] && this.isVersionSmaller(this.version.android, this.mainSettings['app_version_android'].value)){
				this.updateModal = true;
				return;
			}
		}
		else if(this.platform.is('ios')){
			if(this.mainSettings['app_version_ios'] && this.isVersionSmaller(this.version.ios, this.mainSettings['app_version_ios'].value)){
				this.updateModal = true;
				return;
			}
		}
		this.autoLogin();
	}
	isVersionSmaller(c, s){
		let current = c.split('.');
		let store = s.split('.');

		current = `0${current[0]}`.slice(-2)+`0${current[1]}`.slice(-2)+`0${current[2]}`.slice(-2)
		store = `0${store[0]}`.slice(-2)+`0${store[1]}`.slice(-2)+`0${store[2]}`.slice(-2)

		return Number(current) < Number(store);
	}

	isDateBetween(a, b){
		const time = new Date().getTime();
		const start = new Date(a).getTime();
		const end = new Date(b).getTime();

		return (time >= start && time <= end);
	}

	async notificationsInit() {
		this.menuCtrl.enable(false);

		console.log('Initializing HomePage');

		// Request permission to use push notifications
		// iOS will prompt user and return if they granted permission or not
		// Android will just grant without prompting
		try {
			const result = await PushNotifications.requestPermissions();

			if (result.receive === 'granted') {
				await PushNotifications.register();
			} else {
				console.warn('Push notification permission not granted');
				return;
			}

			// Set up listeners after registration
			PushNotifications.addListener('registration', (token: Token) => {
				this.token = token.value;
				console.log('Push registration success, token: ' + token.value);
			});

			PushNotifications.addListener('registrationError', (error: any) => {
				console.error('Error on registration: ' + JSON.stringify(error));
			});

			PushNotifications.addListener('pushNotificationReceived', (notification: PushNotificationSchema) => {
				console.log('Push received: ' + JSON.stringify(notification));
				alert(`${notification.title}\n\n${notification.body}`);
			});

			PushNotifications.addListener('pushNotificationActionPerformed', (notification: ActionPerformed) => {
				this.openNotificatie(notification.notification.data);
			});

		} catch (error) {
			console.error('Error during notification init:', error);
		}
	}

	async cameraInit() {
		try {
			const status = await Camera.checkPermissions();

			if (status.camera !== 'granted') {
				const result = await Camera.requestPermissions();

				if (result.camera !== 'granted') {
					alert('Camera toegang is vereist. Ga naar Instellingen > Machtigingen.');
				}
			}
		} catch (err) {
			console.error('cameraInit() failed:', err);
		}
	}

	async locationInit() {
		const gpsPerm = await Geolocation.checkPermissions();

		if (gpsPerm.location !== 'granted') {
			const result = await Geolocation.requestPermissions();

			if (result.location !== 'granted') {
				alert('Camera toegang is vereist. Ga naar Instellingen > Machtigingen.');
			}
		}
	}

	openStore(){
		if(this.os === 'android'){
			window.open('https://play.google.com/store/apps/details?id=com.infordb.ikbentessa');
		}
		else if(this.os === 'ios'){
			window.open('https://apps.apple.com/nl/app/tessa/id1465270625');
		}
	}

	callPage(page) {
		this.navigate_to = '/home';
		this.router.navigate([`/${page.toLowerCase()}`]);
	}

	openNotificatie(data){
		if(data.route != null){
			this.navigate_to = data.route;
		}
	}

}
