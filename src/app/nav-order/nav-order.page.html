<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Navigatiestructuur</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div class="d-flex ion-align-items-center ion-justify-content-between mx-2">
    <h6>{{selectedUser.name}} {{selectedUser.lastname}}</h6>
    <h6 *ngIf="user && user.user_permissions && user.user_permissions['Settings bewerken']" (click)="userModal = true;" class="ion-text-right" ><ion-button fill="clear" class="shadow text" ><ion-icon name="swap-horizontal-outline"></ion-icon></ion-button></h6>
  </div>

  <ion-card *ngFor="let module of modules; let i = index;" class="ion-padding">
    <div class="d-flex ion-justify-content-between ion-align-items-center">
      <div>
        <ion-card-title class="text d-flex ion-align-items-center" >
          <span>
            <span *ngIf="i < original_module_index[module.id]" class="text-danger" ><ion-icon name="chevron-up-outline"></ion-icon></span>
            <span *ngIf="i > original_module_index[module.id]" class="text-success" ><ion-icon name="chevron-down-outline"></ion-icon></span>
          </span>
          <span class="mx-1" >
            {{module.name}}
          </span>
        </ion-card-title>

      </div>
      <div>
        <ion-button class="d-block shadow no-ripple" fill="clear" *ngIf="i !== 0" (click)="moveUp(i)" ><ion-icon name="chevron-up-outline"></ion-icon></ion-button>

        <ion-button class="d-block shadow no-ripple" fill="clear" *ngIf="i !== (modules.length - 1)" (click)="moveDown(i)" ><ion-icon name="chevron-down-outline"></ion-icon></ion-button>
      </div>
    </div>
  </ion-card>

  <div class="modal-container" *ngIf="userModal" (click)="userModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <div>
        <ion-item lines="none">
          <ion-label position="floating">Zoeken</ion-label>
          <ion-input [(ngModel)]="search"></ion-input>
        </ion-item>
      </div>
      <div *ngIf="users" class="ion-padding overflow-auto mh-25-vh"  >
        <div *ngFor="let user of this.users">
          <div *ngIf="!search || (user.name+user.lastname).toLowerCase().includes(search.toLowerCase())" class="ion-text-center" >
            <ion-button (click)="selectUser(user.id)" class="text" fill="clear" ><ion-text class="text">{{user.name}} {{user.lastname}}</ion-text></ion-button>
          </div>
        </div>
      </div>
      <div *ngIf="!users" class="ion-text-center mb-2 py-2" >
        <ion-spinner name="crescent"></ion-spinner>
      </div>
    </ion-card>
  </div>

  <ion-fab *ngIf="!userModal" vertical="bottom" horizontal="start" slot="fixed" class="ion-margin" >
    <ion-fab-button color="success" (click)="onSubmit()" [disabled]="!button" ><ion-icon name="checkmark-outline"></ion-icon></ion-fab-button>
  </ion-fab>

</ion-content>
