import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';

@Component({
  selector: 'app-nav-order',
  templateUrl: './nav-order.page.html',
  styleUrls: ['./nav-order.page.scss'],
	providers: [Infordb]
})
export class NavOrderPage implements OnInit {

  public user = JSON.parse(window.localStorage.getItem('user'));
  public subdomain = window.localStorage.getItem('subdomain');

  public button = true;
  public userModal = false;
  public selectedUser = {id: this.user.user_id, name: this.user.first_name, lastname: this.user.last_name, user_modules: this.user.user_modules};
  public users;
  public search: string;

  public permissions = this.user.user_permissions;
  public modules = this.cloneArray(this.user.user_modules);
  public original_module_index = {};


  constructor(
    private infordb: Infordb,
    private router: Router,
  ) { }

  ngOnInit() {
    if(this.user.values['app_custom_nav_'+this.selectedUser.id]){
      this.modules = this.cloneArray(JSON.parse(this.user.values['app_custom_nav_'+this.selectedUser.id].value));
      this.skippedModules();
    }
    this.originalModuleIndex();
  }

  originalModuleIndex(){
    for(const i in this.modules){
      const module = this.modules[i];
      this.original_module_index[module.id] = i;
    }
  }

  ionViewWillEnter(){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/users/get`)
      .then(response => {
        const { users } = response.data;
        this.users = users;
      })
      .catch(err => {
        this.button = true;
        this.infordb.handleError(err);
      });
  }

  async onSubmit(){
		try{
			this.button = false;
			const store_response = await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/settings/store`, {
				name: 'app_custom_nav_'+this.selectedUser.id,
				value: JSON.stringify(this.modules),
			});
			
			this.user.user_modules = this.modules;
			localStorage.user = JSON.stringify(this.user);
			
			this.callPage('home');
		}
		catch (e) {
			this.infordb.handleError(e);
		}
  }

  selectUser(id){
    this.userModal = false;
    this.selectedUser = this.findUser(id);
    this.modules = this.selectedUser.user_modules;
    if(this.user.values['app_custom_nav_'+this.selectedUser.id]){
      this.modules = this.cloneArray(JSON.parse(this.user.values['app_custom_nav_'+this.selectedUser.id].value));
      this.skippedModules();
    }
    this.originalModuleIndex();
  }

  findUser(id){
    for(const user of this.users){
      if(user.id === id){
        return user;
      }
    }
  }

  moveUp(i){
    const obj = this.modules[i];

    this.modules[i] = this.modules[i - 1];
    this.modules[i - 1] = obj;
  }

  moveDown(i){
    const obj = this.modules[i];

    this.modules[i] = this.modules[i + 1];
    this.modules[i + 1] = obj;
  }

  cloneArray(arr){
    const array = [];
    for(const row of arr){
      array.push(row);
    }
    return array;
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

//Push new modules not saved in the nav_order setting yet
  skippedModules(){
    const available = this.cloneArray(this.selectedUser.user_modules);
    for(const module of this.modules){
      for(const i in available){
        const avail = available[i];
        if(avail.id === module.id){
          available.splice(Number(i), 1);
        }
      }
    }
    for(const module of available){
      this.modules.push(module);
    }
  }

}
