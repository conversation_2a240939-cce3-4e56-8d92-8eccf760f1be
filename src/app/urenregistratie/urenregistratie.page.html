<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="home"></ion-back-button>
    </ion-buttons>
    <ion-title>Urenregistratie</ion-title>

    <ion-buttons slot="end" *ngIf="hasPermission('urenregistratie_weekoverzicht_inzien')">
      <ion-button (click)="weekOverzicht()"><ion-icon name="calendar-outline"></ion-icon></ion-button>
    </ion-buttons>

    <ion-buttons slot="end" *ngIf="roleCanDo('Hele week invullen')">
      <ion-button (click)="navigateToInsert('Hele week', 'week', null)"><ion-icon name="play-back-circle-outline"></ion-icon></ion-button>
    </ion-buttons>

  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list style="height: 100%" *ngIf="loading">
    <ion-item lines="none" >
      <ion-label>Laden...</ion-label>
      <ion-spinner name="lines" ></ion-spinner>
    </ion-item>
  </ion-list>

  <div *ngFor="let date of dates; let i = index">

    <div *ngIf="date.ingevoerd == true">
      <ion-card class="welcome-card bg-success text-white" color="success" (click)="navigateToInsert(date.datum, 'ingevoerd', date.dag)">
        <ion-card-header>
          <ion-card-title class="flex-between"><span>{{ date.dag }} {{ date.datum }}</span><ion-icon *ngIf="date.correctie" class="font-size-15" name="warning-outline"></ion-icon></ion-card-title>
        </ion-card-header>
      </ion-card>
    </div>

    <div *ngIf="date.verlof == true && date.ingevoerd == false && date.feestdagen == false">
      <ion-card class="welcome-card bg-secondary text-white" color="secondary" (click)="navigateToInsert(date.datum, 'verlof', date.dag)">
        <ion-card-header>
          <ion-card-title>{{ date.dag }} {{ date.datum }}</ion-card-title>
        </ion-card-header>
      </ion-card>
    </div>

    <div *ngIf="date.feestdagen == true && date.ingevoerd == false && date.verlof == false">
      <ion-card class="welcome-card bg-warning text-white" color="warning" (click)="navigateToInsert(date.datum, 'feestdag', date.dag)">
        <ion-card-header>
          <ion-card-title>{{ date.dag }} {{ date.datum }}</ion-card-title>
        </ion-card-header>
      </ion-card>
    </div>

    <div *ngIf="date.ingevoerd == false && date.feestdagen == false && date.verlof == false">
      <ion-card class="welcome-card bg-primary text-white" color="primary" (click)="navigateToInsert(date.datum, 'werkdag', date.dag)">
        <ion-card-header>
          <ion-card-title>{{ date.dag }} {{ date.datum }}</ion-card-title>
        </ion-card-header>
      </ion-card>
    </div>

    <div *ngIf="date.goedkeuren == true && date.ingevoerd == true && date.feestdagen == false && date.verlof == false">
      <ion-card class="welcome-card bg-secondary text-white" color="secondary">
        <ion-card-header>
          <ion-card-title>{{ date.dag }} {{ date.datum }}</ion-card-title>
        </ion-card-header>
      </ion-card>
    </div>


  </div>
</ion-content>
