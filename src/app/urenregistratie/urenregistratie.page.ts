import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb, convertDate } from 'infordb'
import {settings} from "ionicons/icons";

@Component({
  selector: 'app-urenregistratie',
  templateUrl: './urenregistratie.page.html',
  styleUrls: ['./urenregistratie.page.scss'],
	providers: [Infordb]
})
export class UrenregistratiePage implements OnInit {

  dates: any;
  loading = true;
  dagen = ["Zondag", "Maandag", "Dinsdag", "Woensdag", "Donderdag", "Vrijdag", "Zaterdag"];
  subdomain = window.localStorage.getItem('subdomain');
  user = JSON.parse(window.localStorage.getItem('user'));

  constructor(
    private infordb: Infordb,
    private router: Router
  ) { }

  ionViewWillEnter(){
    this.getDatums();
  }

  ngOnInit() {
    // this.getDatums()
  }

  navigateToInsert(date, type, day) {
    if(this.user.values.urenregistratie_verplicht_vorige_dag_ingevuld?.value && this.previousDayNotFilled(date, day)){
      this.infordb.notification('Zorg dat eerst dat je voorgaande urenregistratie compleet is!')
    }else{
      window.localStorage.setItem('urenregistratieDate', date);
      window.localStorage.setItem('urenregistratieType', type);
      window.localStorage.setItem('urenregistratieDay', day);
      this.router.navigate(['/ureninvoeren']);
    }
  }

  getDatums() {
    this.loading = true;
    this.dates = [];

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/datums`, {
        token: this.user.api_token
      })
      .then(res => {
        this.loading = false;
        this.dates = res.data;
      })
      .catch(this.infordb.handleError);

  }

  weekOverzicht() {
    this.router.navigate(['/weekoverzicht']);
  }

  hasPermission(permission: string): boolean {
    var found = false;
    this.user.settings.forEach(setting => {
      if (setting.permission.setting == permission) { found = true; }
    });
    return found;
  }
  roleCanDo(permission: string): boolean {
    let found = false;
    this.user.permissions.forEach(perm => {
      if (perm.permission == permission) {
        found = true;
      }
    });

    return found;
  }

  doRefresh(event){
    this.getDatums();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  previousDayNotFilled(dateString, day) {
    const reformattedDateString = this.reformatDateString(dateString);
    let date = new Date(reformattedDateString);
    let subtract = 1;

    if (date.getDay() == 6 || date.getDay() == 0) {
      return false;
    }

    if (day == "Maandag"){
      subtract = 3;
    }

    date.setDate(date.getDate() - subtract);

    let yesterday = convertDate(date);
    let yesterdaysEntry = this.dates.find(entry => entry.datum === yesterday.date)

    if(!yesterdaysEntry || (!yesterdaysEntry.feestdagen && !yesterdaysEntry.verlof && !yesterdaysEntry.ingevoerd)){
      return true
    }

    return false;
  }

  reformatDateString(dateString: string) {
    const [day, month, year] = dateString.split('-');
    return `${year}-${month}-${day}`;
  }

}
