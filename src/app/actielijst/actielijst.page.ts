import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LoadingController } from '@ionic/angular';
import { Infordb, dateSelect } from "infordb";

@Component({
  selector: 'app-actielijst',
  templateUrl: './actielijst.page.html',
  styleUrls: ['./actielijst.page.scss'],
	providers: [Infordb]
})
export class ActielijstPage implements OnInit {

	public dateSelect = dateSelect;
	
  public post = false;
  public modal = false;
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));

  public queries = [];
  public opmerkingen = {};
  public permissions = {};

	public query = {
		selected: null,
		hide: null,
		opmerking: null,
	}
	public inputs = {
		hide_untill: null,
		opmerking: '',
	}
	
	
  constructor(
    protected infordb: Infordb,
    protected router: Router,
    private loadingController: LoadingController,
  ){ }

  ngOnInit(){
    for(const perm of this.user.permissions){
      this.permissions[perm.permission] = true;
    }
  }

  ionViewWillEnter(){
	  this.clearState();
		this.initQueries();
  }

	clearState(){
		this.post = false;
		this.modal = false;
		
		this.query.hide = null;
		this.query.opmerking = null;
		
		this.inputs.hide_untill = null;
		this.inputs.opmerking = '';
	}
	
	initQueries(){
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/actielijst/get`, {
			id: this.user.user_id.toString(),
		})
			.then(response => {
				this.post = true;
				console.log(response);
				if (response.status != 200) { throw response; }

				this.queries = response.data;
			})
			.catch(err => {
				this.post = true;
				this.infordb.handleError(err)
			});
	}
	
  showQuery(id){
    this.query.selected = this.queries.find(q => q.id == id);
		console.log(this.query.selected);
    this.query.hide = null;
    this.query.opmerking = null;
    this.modal = true;
  }
  showKlant(id){
    localStorage.setItem('klantId', id);
    try {
      this.router.navigate([`/klant`]);
    } catch (e) {
      alert(e);
    }
  }

  convertDate(date){
    const d = new Date(date);
    const datum = ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + d.getFullYear();
    const tijd = d.getHours() + ':' + ('0' + d.getMinutes()).slice(-2);
    return {date: datum, time: tijd};
  }

  callPage(page) {
    console.log(page);
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  hideResult(id){
		this.query.opmerking = null;
		
    if(this.query.hide?.id == id){
      this.query.hide = null;
      return;
    }

    this.query.hide = this.query.selected.results.find(result => result.id == id);
  }
  addOpmerking(id){
	  this.query.hide = null;
	  
	  if(this.query.opmerking?.id == id){
		  this.query.opmerking = null;
		  return;
	  }
	  
	  this.query.opmerking = this.query.selected.results.find(result => result.id == id);
  }

  async confirmHide(){
    if(!this.inputs.hide_untill){
      this.infordb.notification('Selecteer datum!');
      return false;
    }

    const loading = await this.loadingController.create({
      message: 'Verbergen...'
    });
    await loading.present();

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/actielijst/hide`, {
      module: this.query.selected.module,
      index: this.query.hide.id,
      date: this.inputs.hide_untill,
    })
      .then(response => {
        loading.dismiss();
        if (response.status === 201) {
          this.ionViewWillEnter();
        }
      })
      .catch(err => {
        loading.dismiss();
        this.infordb.handleError(err)
      });
  }
  async confirmOpmerking(){
    if(!this.inputs.opmerking){
      this.infordb.notification('Opmerking kan niet leeg zijn!');
      return false;
    }

    const loading = await this.loadingController.create({
      message: 'Opmerking toevoegen...'
    });
    await loading.present();

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/actielijst/opmerking`, {
      module: this.query.selected.module,
      index: this.query.opmerking.id,
      opmerking: this.inputs.opmerking,
    })
      .then(response => {
        loading.dismiss();
        if (response.status === 201) {
          this.ionViewWillEnter();
        }
      })
      .catch(err => {
        loading.dismiss();
        this.infordb.handleError(err)
      });
  }
}
