<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Actielijst</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div class="ion-text-center ion-margin" *ngIf="!post">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post && queries.length == 0">
    <h5 class="ion-text-center"><PERSON><PERSON> act<PERSON> gevonden</h5>
  </div>

  <ng-container *ngIf="post && permissions['Acties bekijken']">
    <ion-card *ngFor="let query of queries" (click)="showQuery(query.id)">
      <ion-list lines="none">
        <ion-item>
          <div>
            <h4 class="m-0">{{query.name}}</h4>
            <ion-text class="text-muted">{{query.module}}</ion-text>
          </div>
          <h4 class="m-0 text-muted" slot="end" *ngIf="query.results.length === 0">{{query.results.length}}</h4>
          <h4 class="m-0 text-success" slot="end" *ngIf="query.results.length > 0 && query.results.length <= 3">{{query.results.length}}</h4>
          <h4 class="m-0 text-warning" slot="end" *ngIf="query.results.length > 3 && query.results.length <= 10">{{query.results.length}}</h4>
          <h4 class="m-0 text-danger" slot="end" *ngIf="query.results.length > 10">{{query.results.length}}</h4>
        </ion-item>
      </ion-list>
    </ion-card>
  </ng-container>

  <!--Results-->
  <div class="modal-container" *ngIf="modal" (click)="modal = false;">
    <ion-card class="w-100" (click)="$event.stopPropagation()">
      <div class="bg-white overflow-auto mh-75-vh">

        <h4 *ngIf="!query.selected.results.length" class="ion-text-center text-muted py-2">Geen resultaten gevonden!</h4>
        <div *ngFor="let result of query.selected.results" class="mx-2 border-bottom">

          <div *ngIf="query.selected.module === 'Offertes'">
            <h3 class="text-black">{{result.naam}}</h3>
            <div class="my-1">
              <div class="result-label">Offertenr:</div>
              <div class="result-value">{{result.offertenummer}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Datum:</div>
              <div class="result-value">{{convertDate(result.offerte_datum).date}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Status:</div>
              <div class="result-value">{{result.status}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Geldig tot:</div>
              <div class="result-value">{{convertDate(result.geldig_tot).date}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Verzonden:</div>
              <div class="result-value">{{convertDate(result.sent_at).date}}</div>
            </div>
          </div>
          <div *ngIf="query.selected.module === 'Werkbonnen'">
            <h3 class="text-black">{{result.werkbonnummer}}</h3>
            <div class="my-1">
              <div class="result-label">Ondertekend</div>
              <div class="result-value">{{result.klant_signature ? 'Ja' : 'Nee'}}</div>
            </div>
            <div class="my-1" *ngIf="result.sent_to" >
              <div class="result-label">Verzonden aan</div>
              <div class="result-value">{{result.sent_to}}</div>
            </div>
            <div class="my-1" *ngIf="result.sent_at" >
              <div class="result-label">Verzonden om</div>
              <div class="result-value">{{convertDate(result.sent_at).date}} {{convertDate(result.sent_at).time}}</div>
            </div>
          </div>
          <div *ngIf="query.selected.module === 'Projecten'">
            <h3 class="text-black">{{result.projectnr}}</h3>
            <div class="my-1">
              <div class="result-label">Projectnaam</div>
              <div class="result-value">{{result.projectnaam}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Opdrachtgever</div>
              <div class="result-value">{{result.opdrachtgever}}</div>
            </div>
          </div>
          <div *ngIf="query.selected.module === 'Projecttaken'">
            <h3 class="text-black">{{result.name}}</h3>
            <div class="my-1" *ngFor="let row of result.custom_rows" >
              <div class="result-label">{{row.name}}</div>
              <div class="result-value" [innerHTML]="row.pure_value" ></div>
            </div>
          </div>
          <div *ngIf="query.selected.module === 'Facturen'">
            <h3 class="text-black">{{result.factuurnummer}}</h3>
            <div class="my-1">
              <div class="result-label">Status</div>
              <div class="result-value">{{result.status}}</div>
            </div>
            <div class="my-1" *ngIf="result.sent_to" >
              <div class="result-label">Verzonden aan</div>
              <div class="result-value">{{result.sent_to}}</div>
            </div>
            <div class="my-1" *ngIf="result.sent_at" >
              <div class="result-label">Verzonden om</div>
              <div class="result-value">{{convertDate(result.sent_at).date}} {{convertDate(result.sent_at).time}}</div>
            </div>
          </div>
          <div *ngIf="query.selected.module === 'Checklists'">
            <h3 class="text-black">{{result.checklistnummer}}</h3>
            <div class="my-1">
              <div class="result-label">Checklist</div>
              <div class="result-value">{{result.template_name}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Datum</div>
              <div class="result-value">{{result.datum_value ? convertDate(result.datum_value).date : ''}}</div>
            </div>
            <div class="my-1">
              <div class="result-label">Klant</div>
              <div class="result-value">{{result.customer_name}}</div>
            </div>
          </div>

          <div class="my-1" *ngIf="result._opmerkingen.length" >
            <div class="result-label" >Opmerkingen:</div>
            <div class="result-value" >
              <div *ngFor="let opmerking of result._opmerkingen" class="flex-between mb-1">
                <div>• {{opmerking.opmerking}}</div>
                <div class="text-muted font-size-07">{{convertDate(opmerking.created_at).date}} {{convertDate(opmerking.created_at).time}}</div>
              </div>
            </div>
          </div>

          <div class="d-flex mx--1 my-2">
            <a class="d-block btn btn-inverse-success mx-1 w-100" *ngIf="result.klant_id" (click)="showKlant(result.klant_id)">
              <ion-icon name="person-outline"></ion-icon>
            </a>
            <a class="d-block btn btn-inverse-primary mx-1 w-100" (click)="hideResult(result.id)">
              <ion-icon name="eye-off-outline"></ion-icon>
            </a>
            <a class="d-block btn btn-inverse-primary mx-1 w-100" (click)="addOpmerking(result.id)">
              <ion-icon name="chatbubble-outline"></ion-icon>
            </a>
          </div>

          <div *ngIf="query.hide?.id == result.id">
            <div class="flex-between">
              <span>Verbergen tot</span>
              <ion-button fill="clear" (click)="dateSelect.select(inputs, 'hide_untill')">{{inputs.hide_untill ? convertDate(inputs.hide_untill).date : 'Selecteer datum'}}</ion-button>
            </div>
            <div class="ion-text-center my-1">
              <a class="btn btn-inverse-success" (click)="confirmHide()">Bevestigen</a>
            </div>
          </div>
          <div *ngIf="query.opmerking?.id == result.id">
            <ion-item lines="none">
              <ion-label position="floating">Opmerking toevoegen</ion-label>
              <ion-textarea placeholder="Opmerking" [autoGrow]="true" [(ngModel)]="inputs.opmerking"></ion-textarea>
            </ion-item>
            <div class="ion-text-center my-1">
              <a class="btn btn-inverse-success" (click)="confirmOpmerking()">Bevestigen</a>
            </div>
          </div>

        </div>

      </div>
    </ion-card>
  </div>

  <!--Date select-->
  <div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;" >
    <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()" >

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)" ><ion-icon name="chevron-back-outline"></ion-icon></ion-button>
        <span class="mx-2" >{{dateSelect.vars.date.year}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
      </div>

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)" ><ion-icon name="chevron-back-outline"></ion-icon></ion-button>
        <span class="mx-2" >{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
      </div>

      <div class="flex-between custom-date-picker-days">
        <div>ma</div>
        <div>di</div>
        <div>wo</div>
        <div>do</div>
        <div>vr</div>
        <div>za</div>
        <div>zo</div>
      </div>
      <div class="custom-date-picker">
        <div
          *ngFor="let day of dateSelect.vars.completeDays"
          [ngClass]="{
            'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
            'prev-month': day.type === 'prev',
            'current-month': day.type === 'current',
            'next-month': day.type === 'next'
          }"
          (click)="dateSelect.confirmDate(day)">
          {{ day.day }}
        </div>
      </div>

    </ion-card>
  </div>

</ion-content>
