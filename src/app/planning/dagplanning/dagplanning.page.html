<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/planning"></ion-back-button>
    </ion-buttons>

    <ion-title>{{convertDate(datum)}}</ion-title>

    <ion-buttons slot="end">
      <ion-button (click)="dagplanningIframe()" *ngIf="user.user_permissions['Dagplanning bekijken']" >
        <h2><ion-icon class="calendar-number-outline" name="calendar-number-outline"></ion-icon></h2>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher *ngIf="isAndroid" slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)" >
      <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post && !planning.length && !werkuren" >
    <h5 class="ion-text-center">Geen planning gevonden</h5>
  </div>

  <div *ngIf="werkuren" >
    <ion-card class="p-1" >
      <h3 class="ion-text-center text-primary" >{{werkuren.begin}} - {{werkuren.eind}}</h3>
      <ul *ngIf="werkuren.opmerkingen.length" class="px-2" >
        <li *ngFor="let opmerking of werkuren.opmerkingen" ><h6>{{opmerking}}</h6></li>
      </ul>
    </ion-card>
  </div>

  <div *ngFor="let row of planning" >
    <ion-card class="p-1 border">

      <div class="my-2">
        <div class="w-100 ion-text-center" >
          <ion-card-title class="ion-text-center text my-2" ><h3 class="m-0">{{row.begin.slice(0, 5)}} - {{row.eind.slice(0, 5)}}</h3></ion-card-title>
          <ion-card-title class="ion-text-center" *ngIf="row.completed === '1'" ><span class="badge badge-success" >Afgerond</span></ion-card-title>

          <ion-card-subtitle *ngIf="row.straat || row.huisnummer || row.postcode || row.plaats" class="ion-text-center my-2" >
            <span>{{row.straat}} </span>
            <span>{{row.huisnummer}}, </span>
            <span>{{row.postcode}} </span>
            <span>{{row.plaats}}</span>
          </ion-card-subtitle>
          <ion-card-subtitle *ngIf="row.user_id != user.user_id" class="ion-text-center my-2 opacity-50" >
            <div  class="d-flex ion-align-items-center ion-justify-content-center text-warning" >
              <ion-icon class="mx-1" name="alert-circle-outline"></ion-icon>
              <span *ngIf="row.user" >Ingepland voor: {{row.user.name}} {{row.user.lastname}}</span>
              <span *ngIf="!row.user" >Ingepland als optie</span>
            </div>
          </ion-card-subtitle>
        </div>
        <div class="ion-text-center text" *ngIf="settings['planning_acties_maps'] && settings['planning_acties_maps'] === 'Aan' ||  settings['planning_acties_sms'] && settings['planning_acties_sms'] === 'Aan' || settings['planning_acties_email'] && settings['planning_acties_email'] === 'Aan'" >
          <h4 (click)="setShow('buttons', row.id)" *ngIf="!show.buttons[row.id]"><ion-icon name="chevron-down-outline"></ion-icon></h4>
          <h4 (click)="setShow('buttons', row.id)" *ngIf="show.buttons[row.id]"><ion-icon name="chevron-up-outline"></ion-icon></h4>
          <div *ngIf="show.buttons[row.id]">
            <div class="d-flex ion-justify-content-center" >
              <div *ngIf="settings['planning_acties_maps'] && settings['planning_acties_maps'] === 'Aan'" >
                <div *ngIf="row.straat || row.postcode || row.plaats" >
                  <ion-button (click)="maps(row.id)" class="mx-1" >
                    <ion-icon name="map-outline"></ion-icon>
                  </ion-button>
                </div>
                <div *ngIf="!row.straat && !row.postcode && !row.plaats" >
                  <ion-button (click)="maps(row.id)" class="mx-1 opacity-25" >
                    <ion-icon name="map-outline"></ion-icon>
                  </ion-button>
                </div>
              </div>

              <div *ngIf="settings['planning_acties_sms'] && settings['planning_acties_sms'] === 'Aan'" >
                <div *ngIf="row.klanten" >
                  <ion-button (click)="smsInit(row.klanten.id, row.klanten.telefoonnummer)" [disabled]="!row.klanten.telefoonnummer" class="mx-1" >
                    <ion-icon name="chatbubble-ellipses-outline"></ion-icon>
                  </ion-button>
                </div>
              </div>

              <div *ngIf="settings['planning_acties_email'] && settings['planning_acties_email'] === 'Aan'" >
                <div *ngIf="row.klanten" >
                  <ion-button (click)="emailInit(row.klanten.id, row.klanten.email)" class="mx-1" >
                    <ion-icon name="mail-outline"></ion-icon>
                  </ion-button>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <div *ngIf="row.legenda" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('activiteit', row.id)">
          <h4 class="m-0" >Activiteit</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.activiteit[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.activiteit[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.activiteit[row.id]" class="my-1" >
          <div class="my-2" >
            <span>Naam</span>
            <h5 class="text m-0" >{{row.legenda.naam}}</h5>
          </div>
        </div>
      </div>

      <div *ngIf="row.aantal_personen || row.prijs" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('aantal_personen', row.id)">
          <h4 class="m-0" >Aantal personen</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.aantal_personen[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.aantal_personen[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.aantal_personen[row.id]" class="my-1" >
          <div class="my-2" >
            <span>Aantal personen</span>
            <h5 class="text m-0" >{{row.aantal_personen}}</h5>
          </div>
          <div class="my-2" >
            <span>Prijs p.p.</span>
            <h5 class="text m-0" >{{row.prijs}}</h5>
          </div>
          <div class="my-2" >
            <span>BTW</span>
            <h5 class="text m-0" >{{row.btw}}</h5>
          </div>
        </div>
      </div>

      <div *ngIf="row.klanten" class="my-1 px-2 py-1 rounded border card-color"  >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('klant', row.id)">
          <h4 class="m-0" >Klant</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.klant[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.klant[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.klant[row.id]" class="my-1" >
            <div class="my-2" *ngIf="row.klanten.soort_klant" >
              <span>Soort klant</span>
              <h5 class="text m-0" >{{row.klanten.soort_klant}}</h5>
            </div>
            <div class="my-2" *ngIf="row.klanten.naam" >
              <span>Bedrijfsnaam</span>
              <h5 class="text m-0" >{{row.klanten.naam}}</h5>
            </div>
            <div class="my-2" *ngIf="row.klanten.contactpersoon_voornaam || row.klanten.contactpersoon_achternaam" >
              <span>Naam</span>
              <h5 class="text m-0" >{{row.klanten.contactpersoon_voornaam}} {{row.klanten.contactpersoon_achternaam}}</h5>
            </div>
            <div class="my-2" *ngIf="row.klanten.email" >
              <span>Email</span>
              <h5 class="text m-0" >{{row.klanten.email}}</h5>
            </div>
            <div class="my-2" *ngIf="row.klanten.telefoonnummer" >
              <span>Telefoonnummer</span>
              <h5 class="text m-0">
                <a class="text-primary" (click)="$event.stopPropagation(); call(row.klanten.telefoonnummer)" >
                  <ion-icon name="call-outline"></ion-icon>
                  <span class="ml-2" >{{row.klanten.telefoonnummer}}</span>
                </a>
              </h5>
            </div>
            <div class="my-2" *ngIf="row.klanten.straat || row.klanten.huisnummer || row.klanten.postcode || row.klanten.plaats" >
              <span>Adres</span>
              <div class="flex-between">
                <div>
                  <h5 class="text m-0" >{{row.klanten.straat}} {{row.klanten.huisnummer}}{{row.klanten.toevoeging}},</h5>
                  <h5 class="text m-0" >{{row.klanten.postcode}} {{row.klanten.plaats}}</h5>
                </div>
                <div>
                  <ion-button fill="clear" (click)="maps(row.id, 'klant')" class="mx-1" >
                    <ion-icon name="map-outline"></ion-icon>
                  </ion-button>
                </div>
              </div>
            </div>
          </div>
      </div>

      <div *ngIf="row.project" class="my-1 px-2 py-1 rounded border card-color"  >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('project', row.id)">
          <h4 class="m-0" >Project</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.project[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.project[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.project[row.id]" class="my-1" >
            <div class="my-2" *ngIf="!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].nummer">
              <span>Projectnummer</span>
              <h5 class="text m-0" >{{row.project.projectnr}}</h5>
            </div>
            <div class="my-2" *ngIf="row.project.projectnaam && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].naam)" >
              <span>Projectnaam</span>
              <h5 class="text m-0" >{{row.project.projectnaam}}</h5>
            </div>
            <div class="my-2" *ngIf="row.project.opdrachtnummer && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].opdrachtnummer)" >
              <span>Opdrachtnummer</span>
              <h5 class="text m-0" >{{row.project.opdrachtnummer}}</h5>
            </div>
            <div class="my-2" *ngIf="row.project.taaknummer && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].taaknummer)" >
              <span>Taaknummer</span>
              <h5 class="text m-0" >{{row.project.taaknummer}}</h5>
            </div>
            <div class="my-2" *ngIf="row.project.opdrachtgever && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].opdrachtgever)" >
              <span>Opdrachtgever</span>
              <h5 class="text m-0" >{{row.project.opdrachtgever}}</h5>
            </div>
            <div class="my-2" *ngIf="row.project.code && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].code)" >
              <span>Code</span>
              <h5 class="text m-0" >{{row.project.code}}</h5>
            </div>
            <div class="my-2" *ngIf="row.project.contactpersoon && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].contactpersoon)" >
              <span>Contactpersoon</span>
              <h5 class="text m-0" >{{row.project.contactpersoon.voornaam}} {{row.project.contactpersoon.achternaam}}</h5>
              <h6 class="text m-0" *ngIf="row.project.contactpersoon.mobiel" >
                <a class="text-primary" (click)="$event.stopPropagation(); call(row.project.contactpersoon.mobiel)" >
                  <ion-icon name="call-outline"></ion-icon>
                  <span class="ml-2" >{{row.project.contactpersoon.mobiel}}</span>
                </a>
              </h6>
              <h6 class="text m-0" *ngIf="row.project.contactpersoon.telefoon" >
                <a class="text-primary" (click)="$event.stopPropagation(); call(row.project.contactpersoon.telefoon)" >
                  <ion-icon name="call-outline"></ion-icon>
                  <span class="ml-2" >{{row.project.contactpersoon.telefoon}}</span>
                </a>
              </h6>
            </div>
            <div class="my-2" *ngIf="!row.project.contactpersoon && row.project.klant && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].contactpersoon)" >
              <span>Contactpersoon</span>
              <h5 class="text m-0" >{{row.project.klant.contactpersoon_voornaam}} {{row.project.klant.contactpersoon_achternaam}}</h5>
              <h6 class="text m-0" *ngIf="row.project.klant.contactpersoon_mobiel" >
                <a class="text-primary" (click)="$event.stopPropagation(); call(row.project.klant.contactpersoon_mobiel)" >
                  <ion-icon name="call-outline"></ion-icon>
                  <span class="ml-2" >{{row.project.klant.contactpersoon_mobiel}}</span>
                </a>
              </h6>
              <h6 class="text m-0" *ngIf="row.project.klant.contactpersoon_telefoon && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].contactpersoon_telefoon)" >
                <a class="text-primary" (click)="$event.stopPropagation(); call(row.project.klant.contactpersoon_telefoon)" >
                  <ion-icon name="call-outline"></ion-icon>
                  <span class="ml-2" >{{row.project.klant.contactpersoon_telefoon}}</span>
                </a>
              </h6>
            </div>
            <div class="my-2" *ngIf="(row.project.adres || row.project.woonplaats) && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].adres)" >
              <span>Adres</span>
              <div class="flex-between">
                <div>
                  <h5 class="text m-0" >{{row.project.adres}} {{row.project.huisnummer}}{{row.project.toevoeging}},</h5>
                  <h5 class="text m-0" >{{row.project.postcode}} {{row.project.woonplaats}}</h5>
                </div>
                <div>
                  <ion-button fill="clear" (click)="maps(row.id, 'project')" class="mx-1" >
                    <ion-icon name="map-outline"></ion-icon>
                  </ion-button>
                </div>
              </div>
            </div>
            <div class="my-2" *ngIf="row.project.omschrijving && (!settings['planning_project_info_hide'] || !settings['planning_project_info_hide'].omschrijving)" >
              <span>Omschrijving</span>
              <h5 class="text m-0" >{{row.project.omschrijving}}</h5>
            </div>
            <div *ngIf="row.project.custom">
              <div *ngFor="let custom of row.project.custom">

                <div class="my-2" *ngIf="custom.name == 'Opmerking' && custom.value != ''" >
                  <span>Opmerking</span>
                  <h5 class="text m-0" >{{custom.value}}</h5>
                </div>
                <div class="my-2" *ngIf="custom.name == 'Artikelen' && custom.value != '' && custom.value != []" >
                  <span>Artikelen</span>
                  <h5 class="text m-0" *ngFor="let value of custom.value">{{value.name}}</h5>
                </div>

              </div>
            </div>
          </div>
      </div>

      <div *ngIf="row.taken?.length" class="my-1 px-2 py-1 rounded border card-color"  >
        <div class="text d-flex ion-justify-content-between" (click)="setShow('taken', row.id)">
          <h4 class="m-0" >Taken</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.taken[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.taken[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.taken[row.id]" class="my-1" >
            <div *ngFor="let taak of row.taken" >
              <div class="my-2" >
                <span>Naam</span>
<!--                In app gebruik je (touchstart) en (touchend). voor desktop (dus testen) (mousedown) en (mouseup)-->
                <h5 (touchstart)="touchStart(taak.name)" (touchend)="touchEnd()"  class="text m-0" >{{taak.name}}</h5>
              </div>
            </div>
          </div>
      </div>

      <div *ngIf="row.day_taken_planning?.length" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('day_taken_planning', row.id)">
          <h4 class="m-0" >Andere medewerkers</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.day_taken_planning[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.day_taken_planning[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.day_taken_planning[row.id]" class="my-1" >
            <div *ngFor="let taak_planning of row.day_taken_planning" >
              <div class="flex-between">
                <span>{{taak_planning.user?.name || ''}} {{taak_planning.user?.lastname}}</span>
                <span>{{taak_planning.begin.slice(0, 5)}} - {{taak_planning.eind.slice(0, 5)}}</span>
              </div>
            </div>
          </div>
      </div>

      <div *ngIf="row.aanvraag" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('aanvraag', row.id)">
          <h4 class="m-0" >Aanvraag</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.aanvraag[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.aanvraag[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.aanvraag[row.id]" class="my-1" >
          <div class="my-2" >
            <span>Aanvraagnummer</span>
            <h5 class="text m-0" >{{row.aanvraag.aanvraagnummer}}</h5>
          </div>
          <div class="my-2" >
            <span>Status</span>
            <h5 class="text m-0" >{{row.aanvraag.status}}</h5>
          </div>
          <div class="my-2" *ngFor="let value of row.aanvraag.values" >
            <span>{{value.naam}}</span>
            <h5 class="text m-0" >{{value.value}}</h5>
          </div>
        </div>
      </div>

      <div *ngIf="row.offerte" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('offerte', row.id)">
          <h4 class="m-0" >Offerte</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.offerte[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.offerte[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.offerte[row.id]" class="my-1" >
          <div class="my-2" >
            <span>Naam</span>
            <h5 class="text m-0" >{{row.offerte.naam}}</h5>
          </div>
          <div class="my-2" >
            <span>Offertenummer</span>
            <h5 class="text m-0" >{{row.offerte.offertenummer}}</h5>
          </div>
          <div class="my-2" >
            <span>Status</span>
            <h5 class="text m-0" >{{row.offerte.status}}</h5>
          </div>
          <div *ngIf="offerteInfoKeys[row.offerte.template_id]" >
            <div  class="my-2" *ngFor="let key of offerteInfoKeys[row.offerte.template_id]" >
              <span>{{key}}</span>
              <h5 class="text m-0" ><span *ngIf="row.offerte.values[key]" >{{row.offerte.values[key].tekst}}</span></h5>
            </div>
          </div>
        </div>
      </div>

        <div *ngIf="row.werkbon_keywords" class="my-1 px-2 py-1 rounded border card-color"  >
            <div class="text d-flex ion-justify-content-between " (click)="setShow('werkbon', row.id)">
                <h4 class="m-0" >Werkbon</h4>
                <h4 class="m-0 d-flex" *ngIf="!show.werkbon[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
                <h4 class="m-0 d-flex" *ngIf="show.werkbon[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
            </div>
            <div *ngIf="show.werkbon[row.id]" class="my-1" >
                <div *ngFor="let werkbon of row.werkbon_keywords | keyvalue">
                    <div *ngFor="let item of werkbon.value | keyvalue">
                        <div class="flex-between">
                            <div class="my-2">
                                <span>{{ replaceUnderscore(item.key) }}</span>
                                <h5 class="text m-0">{{ item.value ?? '' }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

      <div *ngIf="row.opmerking" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('opmerking', row.id)">
          <h4 class="m-0" >Opmerking</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.opmerking[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.opmerking[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.opmerking[row.id]" class="my-1" >
          <div class="my-2 text" >
            <span [innerHTML]="row.opmerking" ></span>
          </div>
        </div>
      </div>

      <div *ngIf="row.custom?.length" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('custom', row.id)">
          <h4 class="m-0" >Custom</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.custom[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.custom[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.custom[row.id]">
          <div *ngFor="let custom of row.custom">
            <div class="my-2" *ngIf="custom.value != ''" >
              <span>{{custom.name}}</span>
              <h5 *ngIf="custom.type != 'dataset'" class="text m-0" >{{custom.value}}</h5>
              <div *ngIf="custom.type == 'dataset'" >
                <h5 *ngFor="let value of custom.value" class="text m-0" >{{value.aantal}} X {{value.name}}</h5>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="row.explorer.length" class="my-1 px-2 py-1 rounded border card-color" >
        <div class="text d-flex ion-justify-content-between " (click)="setShow('files', row.id)">
          <h4 class="m-0" >Bestanden</h4>
          <h4 class="m-0 d-flex" *ngIf="!show.files[row.id]"><ion-icon class="ion-align-self-center" name="chevron-down-outline"></ion-icon></h4>
          <h4 class="m-0 d-flex" *ngIf="show.files[row.id]"><ion-icon class="ion-align-self-center" name="chevron-up-outline"></ion-icon></h4>
        </div>
        <div *ngIf="show.files[row.id]" class="my-1" >
          <div class="my-2 text" (click)="$event.stopPropagation()" >
            <ion-button (click)="download(file.src)" class="my-1 w-100" *ngFor="let file of row.explorer" >
              <ion-text>{{file.name}}</ion-text>
            </ion-button>
          </div>
        </div>
      </div>

      <div *ngIf="modules['Werkbonnen'] && settings['app_planning_disable_werkbonnen'] != 'ja'" class="my-2">

        <div *ngIf="settings['werkbon_planning_manier_aanmaken'] == 'released_werkbon'" >

          <div *ngIf="row.released_werkbon" >
            <a class="w-100 btn btn-inverse-primary my-2" *ngIf="row.completed !== '1'"  (click)="selectWerkbon(row.id)" >Werkbon {{ (row.released_werkbon.werkbonnummer || '')+' invullen' }} </a>
            <div class="alert alert-success" *ngIf="row.completed === '1'" >Werkbon ingevuld: {{ (row.released_werkbon.werkbonnummer || '') }}</div>
          </div>

        </div>

        <div *ngIf="settings['werkbon_planning_manier_aanmaken'] == 'single'">
          <div *ngIf="row.project && row.project.werkbon" class="alert alert-success" >
            Werkbon uitgebracht: {{row.project.werkbon.werkbonnummer}}
          </div>
          <div *ngIf="row.offerte && row.offerte.project && row.offerte.project.werkbon" class="alert alert-success" >
            Werkbon uitgebracht: {{row.offerte.project.werkbon.werkbonnummer}}
          </div>
          <div>
            <div *ngIf="row.offerte && !row.offerte.project" class="alert alert-warning" >Offerte nog niet geaccordeerd</div>
            <a class="w-100 btn btn-inverse-primary my-2" *ngIf="row.project && !row.project.werkbon || row.offerte && row.offerte.project && !row.offerte.project.werkbon" (click)="selectWerkbon(row.id)" >Werkbon</a>
          </div>
        </div>

        <div *ngIf="settings['werkbon_planning_manier_aanmaken'] == 'multiple'">
          <a class="w-100 btn btn-inverse-primary my-2"  (click)="selectWerkbon(row.id)" >Werkbon</a>
        </div>

        <div *ngIf="row.local_werkbonnen.length" >
          <div class="flex-between my-2 font-size-09">
            <div class="border-bottom w-100"></div>
            <div class="text-center nobr mx-3">Of selecteer een lokaal opgeslagen werkbon</div>
            <div class="border-bottom w-100"></div>
          </div>

          <div *ngFor="let bon of row.local_werkbonnen" >
            <a class="w-100 btn btn-inverse-primary my-2"  (click)="selectLocalWerkbon(bon._index)" >
              <span class="mx-2" >{{bon.name}}</span>
              <span class="mx-2 opacity-50" >( {{bon.time.date}} {{bon.time.time}} )</span>
            </a>
          </div>
        </div>


      </div>

      <div *ngIf="user.user_permissions['Planning bewerken'] && this.user.values['app_planning_edit']">
        <div class="d-flex ion-justify-content-center" >
          <a (click)="edit(row.id)" class="w-100 btn btn-inverse-primary my-2" >Bewerken</a>
        </div>
      </div>

    </ion-card>
  </div>

  <div class="modal-container" *ngIf="werkbonnenModal" (click)="werkbonnenModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none" class="border-bottom" >
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div class="ion-padding overflow-auto mh-33-vh">
        <div *ngFor="let template of werkbonnenTemplates" class="my-2">
          <div class="ion-text-center mb-1" >
            <ion-button class="h-auto ion-text-wrap" fill="clear" (click)="newWerkbon(template.id)" >
              <span class="d-block p-1" >{{template.naam}}</span>
            </ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

  <div class="modal-container" *ngIf="smsVars.modal" (click)="smsVars.modal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none">
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div *ngFor="let sms of smsVars.templates;let i = index" class="my-2" >
        <div class="ion-text-center my-2"  >
          <ion-button (click)="sendSms(i)" class="text" fill="clear" ><ion-text class="text">{{sms.name}}</ion-text></ion-button>
        </div>
      </div>
    </ion-card>
  </div>

  <div class="modal-container" *ngIf="emailVars.modal" (click)="emailVars.modal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-item lines="none">
        <ion-label>Selecteer template</ion-label>
      </ion-item>
      <div *ngFor="let email of emailVars.templates; let i = index" class="my-2" >
        <div class="ion-text-center my-2"  >
          <ion-button (click)="selectEmail(i)" class="text" fill="clear" ><ion-text class="text">{{email.name}}</ion-text></ion-button>
        </div>
      </div>
    </ion-card>
  </div>

  <div class="modal-container" *ngIf="emailVars.sendModal" (click)="emailVars.sendModal = false" >
    <ion-card class="w-100" (click)="$event.stopPropagation()" >
      <ion-list>
        <ion-item>
          <ion-label position="floating">Afzender</ion-label>
          <ion-input [(ngModel)]="emailVars.form.afzender" [value]="emailVars.form.afzender"></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Ontvanger</ion-label>
          <ion-input [(ngModel)]="emailVars.form.ontvanger" [value]="emailVars.form.ontvanger"></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Onderwerp</ion-label>
          <ion-input [(ngModel)]="emailVars.form.onderwerp" [value]="emailVars.form.onderwerp" ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">Inhoud</ion-label>
          <ion-textarea  [autoGrow]="true" [(ngModel)]="emailVars.form.content" [value]="emailVars.form.content" ></ion-textarea>
        </ion-item>
      </ion-list>
      <ion-button [disabled]="!btn" (click)="submitEmail()" color="success" type="submit" expand="full">Versturen</ion-button>
    </ion-card>
  </div>

</ion-content>

