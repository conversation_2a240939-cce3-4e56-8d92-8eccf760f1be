import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';
import {ClientStorageService} from "../../services/ClientStorage/client-storage.service";
import { Platform } from '@ionic/angular';

@Component({
  selector: 'app-dagplanning',
  templateUrl: './dagplanning.page.html',
  styleUrls: ['./dagplanning.page.scss'],
  providers: [ Infordb ],
})
export class DagplanningPage implements OnInit {
  public modules = {};
  public settings = {};
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(localStorage.getItem('user'));
  public datum = localStorage.getItem('planningDatum');
  public planning;
  public werkuren;
  public offerteInfo;
  public offerteInfoKeys = {};
  public post = false;
  public btn = true;
  public pressTimer;
  public isAndroid: boolean;

  public show = {
    buttons: {},
    activiteit: {},
    klant: {},
    project: {},
    aanvraag: {},
    offerte: {},
    werkbon: {},
    opmerking: {},
    custom: {},
    files: {},
    aantal_personen: {},
    taken: {},
	  day_taken_planning: {},
  };

  public smsVars = {
    klant_id: null,
    nummer: null,
    modal: false,
    templates: [],
  };
  public emailVars = {
    klant_id: null,
    email: null,
    modal: false,
    templates: [],
    sendModal: false,
    form: null,
  };

  public werkbonPlanning;
  public werkbonnenModal = false;
  public werkbonnenTemplates;

  constructor(
     private router: Router,
     private infordb: Infordb,
     private clientStorage: ClientStorageService,
     private platform: Platform
  ) {
    this.isAndroid = this.platform.is('android');
  }

  ngOnInit() {
    for(const module of this.user.modules){
      this.modules[module.module.name] = true;
    }
    for(const key in this.user.values){
      const setting = this.user.values[key];
      if(setting.naam == 'planning_project_info_hide'){
        this.settings[setting.naam] = JSON.parse(setting.value);
        console.log(JSON.parse(setting.value));

        continue;
      }
      this.settings[setting.naam] = setting.value;
    }

  }
  ionViewWillEnter(){
    this.post = false;
    this.planning = [];

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/planning/dagplanning`, {
      user: this.user.user_id.toString(),
      datum: this.datum,
    })
      .then(response => {

        if (response.status === 201) {
          const {planning, werkuren, werkbonnenTemplates, offerteInfo, smsTemplates, emailTemplates} = response.data;

          for (let row of planning) {
            if (row.werkbon_keywords) {
              row.werkbon_keywords = JSON.parse(row.werkbon_keywords);
            }
          }

          this.planning = planning;
          this.werkuren = werkuren;
          this.werkbonnenTemplates = werkbonnenTemplates;
          this.offerteInfo = offerteInfo;
          this.smsVars.templates = smsTemplates;
          this.emailVars.templates = emailTemplates;

          this.postLoops();
          this.post = true;
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err)
      });
  }
  postLoops(){
    this.fillShowCustom()
    this.openTabs();
    this.fillOfferteInfoKeys();
    this.setLocalWerkbonnen();
  }

  fillShowCustom(){
    for(const planning of this.planning){
      try {
        if (planning.custom) {
          for (let custom of planning.custom) {
            if (custom.type == 'dataset') {
              custom.value = JSON.parse(custom.value);
            }
          }
        }
        if(planning.project && planning.project.custom){
          for(let custom of planning.project.custom){
            if(custom.name == 'Artikelen'){
              custom.value = JSON.parse(custom.value);
            }
          }
        }
      }catch (e){
        this.infordb.handleError(e);
      }
    }
  }

  convertDate(date){
    const datum = new Date(date);
    return ('0' + datum.getDate()).slice(-2) + '-' + ('0' + (datum.getMonth() + 1)).slice(-2) + '-' + datum.getFullYear();
  }

  fillOfferteInfoKeys(){
    for (const offerteTemplateId in this.offerteInfo){
      this.offerteInfoKeys[offerteTemplateId] = [];
      for(const key in this.offerteInfo[offerteTemplateId]){
        this.offerteInfoKeys[offerteTemplateId].push(key);
      }
    }
  }
  openTabs(){
    for(const row of this.planning){
      if(this.settings['planning_view_buttons'] === 'ja'){this.show.buttons[row.id] = true;}
      if(this.settings['planning_view_activiteit'] === 'ja'){this.show.activiteit[row.id] = true;}
      if(this.settings['planning_view_aantal_personen'] === 'ja'){this.show.aantal_personen[row.id] = true;}
      if(this.settings['planning_view_klant'] === 'ja'){this.show.klant[row.id] = true;}
      if(this.settings['planning_view_project'] === 'ja'){this.show.project[row.id] = true;}
      if(this.settings['planning_view_aanvraag'] === 'ja'){this.show.aanvraag[row.id] = true;}
      if(this.settings['planning_view_offerte'] === 'ja'){this.show.offerte[row.id] = true;}
      if(this.settings['planning_view_opmerking'] === 'ja'){this.show.opmerking[row.id] = true;}
      if(this.settings['planning_view_custom'] === 'ja'){this.show.custom[row.id] = true;}
      if(this.settings['planning_view_files'] === 'ja'){this.show.files[row.id] = true;}
    }
  }

  selectWerkbon(id){
    this.werkbonPlanning = this.planning.find(row => row.id == id);
    const { released_werkbon } = this.werkbonPlanning;

    if(released_werkbon){
      localStorage.setItem('werkbonEdit', released_werkbon.id);
      localStorage.setItem('werkbonTemplateId', released_werkbon.template_id);
      localStorage.setItem('planningWerkbon', JSON.stringify(this.werkbonPlanning));
      this.callPage('/werkbonnen/new')

      return;
    }

    this.werkbonnenModal = true;
  }
  newWerkbon(template){
    this.werkbonnenModal = false;

    localStorage.setItem('werkbonTemplateId', template);
    localStorage.setItem('planningWerkbon', JSON.stringify(this.werkbonPlanning));

    this.callPage('/werkbonnen/new')
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  doRefresh(event) {
    this.ionViewWillEnter();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  maps(i, target = null){
    const pln = this.findPlanning(i);
    if(!pln){
      alert('Er is iets foutgegaan!');
      return false;
    }

    let nav = '';
    if(!target){
      const { straat, huisnummer, toevoeging, postcode, plaats } = pln;
      nav = `${straat || ''} ${huisnummer || ''}${toevoeging || ''} ${postcode || ''} ${plaats || ''}`;
    }
    else if(target == 'klant'){
      const { straat, huisnummer, toevoeging, postcode, plaats } = pln.klanten;
      nav = `${straat || ''} ${huisnummer || ''}${toevoeging || ''} ${postcode || ''} ${plaats || ''}`;
    }
    else if(target == 'project'){
      const { adres, huisnummer, toevoeging, postcode, woonplaats } = pln.project;
      nav = `${adres || ''} ${huisnummer || ''}${toevoeging || ''} ${postcode || ''} ${woonplaats || ''}`;
    }

		window.location.href = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(nav)}`;
  }

  smsInit(id, nummmer){
    this.smsVars.modal = true;
    this.smsVars.klant_id = id;
    this.smsVars.nummer = nummmer;
  }
  sendSms(i){
		const content = encodeURIComponent(this.smsVars.templates[i].content);
		window.location.href = `sms: ${this.smsVars.nummer}?&body=${content}`

	  this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/sms/store`, {
		  user: this.user.user_id,
		  klant: this.smsVars.klant_id,
		  number: this.smsVars.nummer,
		  message: this.smsVars.templates[i].content,
	  })
		  .catch(err => {
			  this.post = true;
			  this.infordb.handleError(err);
		  });
  }

  call(number){
    if(!confirm(`Wilt u ${number} bellen?`)){ return }
		window.location.href = `tel: ${number}`;
  }

  emailInit(id, email){
    this.emailVars.modal = true;
    this.emailVars.klant_id = id;
    this.emailVars.email = email;
  }
  selectEmail(i){
    this.emailVars.sendModal = true;
    this.emailVars.form = { ...this.emailVars.templates[i] };
    this.emailVars.form.ontvanger = this.emailVars.email;
  }
  submitEmail(){
    if(!this.emailVars || !this.emailVars.form.afzender || !this.emailVars.form.ontvanger  || !this.emailVars.email || !this.emailVars.form.onderwerp || !this.emailVars.form.content){
      alert('Alle velden zijn verplicht');
      return false;
    }
    this.btn = false;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/klanten/email/store`, {
      email: JSON.stringify(this.emailVars.form),
      klant: this.emailVars.klant_id,
      user: this.user.user_id,
    })
      .then(response => {
        this.btn = true;
        this.emailVars.form = null;
        this.emailVars.sendModal = false;
        this.emailVars.modal = false;
        this.infordb.notification({message: 'Email verzonden!'});
      })
      .catch(err => {
        this.btn = true;
        this.infordb.handleError(err);
      });
  }

  setShow(target, id){
    this.show[target][id] = !this.show[target][id];
  }

  findPlanning(id){
    for(const row of this.planning){
      if(row.id == id){return row;}
    }
  }

	setLocalWerkbonnen(){
		this.planning.map(row => { row.local_werkbonnen = this.findLocalPlanningWerkbonnen(row.id); })
    }


	findLocalPlanningWerkbonnen(id){
        let werkbonnen = this.clientStorage.get('werkbonnen_local_data', []);
		werkbonnen.map((werkbon, index) => {
			werkbon._index = index;
			return werkbon;
		});

		return werkbonnen.filter(bon => bon?.planning?.id == id);
	}
	selectLocalWerkbon(i){
        const local_werkbonnen = this.clientStorage.get('werkbonnen_local_data', []);

		localStorage.werkbonnen_local_selected = i;
		localStorage.checklistTemplateId = local_werkbonnen[i].template.id;

		this.callPage('werkbonnen/new');
	}

  download(src){
    const link = 'https://' + this.subdomain + '.ikbentessa.nl/api/file/explorer/files/' + src;
    window.open(link);
  }

  touchStart(text) {
    this.pressTimer = setTimeout(() => {
      this.copyText(text);
    }, 1000);
  }

  touchEnd() {
    clearTimeout(this.pressTimer);
  }

  copyText(text){
    const el = document.createElement("textarea");
    el.value = text;
    el.setAttribute("readonly", "");
    document.body.appendChild(el);

    el.select();
    // Copy text to clipboard
    try {
      const successful = document.execCommand("copy");
      if (successful) {
        this.infordb.notification({message: 'De tekst is succesvol gekopieerd naar je klembord.'});
        document.body.removeChild(el);
        return true;
      }
    }catch (err) {
      this.infordb.notification({message: 'Er is iets misgegaan tijdens het kopiëren van de tekst.'});
      return false;
    }
  }

  page(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  dagplanningIframe(){
		localStorage.iframeRoute = `planning/dagplanning/pdf/${this.datum}`
	  this.page('iframe');
  }

  edit(id){
    const plan = this.findPlanning(id);
    const date = new Date(plan.datum);
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    localStorage.iframeRoute = `iframe/planning/overzicht/dag/${year}/${month}-${day}?edit=${id}`;
    this.page('iframe');

  }

  replaceUnderscore(value: string): string {
    return value ? value.replace(/_/g, ' ') : value;
  }

}
