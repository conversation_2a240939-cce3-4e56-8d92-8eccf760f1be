<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Planning</ion-title>
    <ion-buttons slot="end">

      <ion-button (click)="dagplanningExport()" *ngIf="user.user_permissions['Dagplanning bekijken'] && bladeExists">
        <h2><ion-icon name="download-outline" class="mr-2"></ion-icon></h2>
      </ion-button>

      <ion-button (click)="dagplanningPeriodeIframe()" *ngIf="user.user_permissions['Planning bewerken']" >
        <h2><ion-icon class="calendar-number-outline" name="calendar-number-outline"></ion-icon></h2>
      </ion-button>

      <ion-button (click)="agendaIframe()" *ngIf="!user.values['planning_app_verbergen_weekoverzicht']" >
        <h2><ion-icon class="ion-margin-horizontal" name="calendar-outline"></ion-icon></h2>
      </ion-button>

    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content>

  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)" >
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post">
    <div class="d-flex bg-inverse-secondary pt-2" *ngIf="user.values['planning_werkuren'].value === 'Aan'">
      <div class="ion-text-center w-100 rounded-top p-2" [ngClass]="tabClass.planning" (click)="selectTab('planning')" >Planning</div>
      <div class="ion-text-center w-100 rounded-top p-2" [ngClass]="tabClass.werkuren" (click)="selectTab('werkuren')" >Werkuren</div>
    </div>
    <div *ngIf="tab === 'planning'" >
      <div *ngIf="!planning.length" >
        <h5 class="ion-text-center">Geen planning gevonden</h5>
      </div>
      <div class="ion-margin-vertical" *ngFor="let row of planning ">
        <ion-card class="ion-margin-horizontal" (click)="callPage('dagplanning', row.date)" >
          <div class="d-flex ion-justify-content-between ion-align-items-center p-1" >
            <div class="w-100" >
              <div >
                <h4 class="d-inline-block text mr-1 m-0" >{{row.dow}}</h4><span class="mx-1" >{{row.convDate}}</span>
              </div>
              <h2 style="display: inline-block; color: #3171e0" >{{row.begin.slice(0,5)}} - {{row.eind.slice(0,5)}}</h2>
            </div>
            <div>
              <ion-button fill="clear" class="text"><h1 class="0" ><ion-icon name="chevron-forward-outline"></ion-icon></h1></ion-button>
            </div>
          </div>
          <div *ngIf="currentDate === convertDate(row.date)" class="w-100 h-5 bg-success" ></div>
        </ion-card>
      </div>
    </div>
    <div *ngIf="tab === 'werkuren'" >
      <div *ngIf="!werkuren.length" >
        <h5 class="ion-text-center">Geen werkuren gevonden</h5>
      </div>
      <div class="ion-margin-vertical" *ngFor="let row of werkuren ">
        <ion-card class="ion-margin-horizontal" (click)="callPage('dagplanning', row.date)" >
          <div class="d-flex ion-justify-content-between ion-align-items-center p-1" >
            <div class="w-100" >
              <div >
                <h4 class="d-inline-block text mr-1 m-0" >{{row.dow}}</h4><span class="mx-1" >{{row.convDate}}</span>
              </div>
              <h2 class="text-primary mb-0" >{{row.begin.slice(0,5)}} - {{row.eind.slice(0,5)}}</h2>
            </div>
            <div>
              <ion-button fill="clear" class="text"><h1 class="0" ><ion-icon name="chevron-forward-outline"></ion-icon></h1></ion-button>
            </div>
          </div>
          <div class="px-3" >
            <ul class="p-0">
              <li *ngFor="let opmerking of row.opmerkingen" ><ion-text>{{opmerking}}</ion-text></li>
            </ul>
          </div>
          <div *ngIf="currentDate === convertDate(row.date)" class="w-100 h-5 bg-success" ></div>
        </ion-card>
      </div>
    </div>
  </div>



</ion-content>
