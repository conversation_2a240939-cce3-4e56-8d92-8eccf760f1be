import { Component, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import { Infordb } from "infordb";

@Component({
  selector: 'app-planning',
  templateUrl: './planning.page.html',
  styleUrls: ['./planning.page.scss'],
  providers: [Infordb],
})
export class PlanningPage implements OnInit {
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public settings = {};
  public currentDate = this.convertDate(new Date(new Date().getTime()));
  dates: any;
  public planning = [];
  public werkuren = [];
  public post = false;
  public link;
  public dow = ['Zondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag'];
  public tab = 'planning';

  public bladeExists = false;
  public tabClass = {
    planning: 'bg-white border border-bottom-0',
    werkuren: 'border-bottom'
  };
  public planningBewerkenPerm = this.hasPermission('planning bewerken')


  constructor(
      public router: Router,
      private infordb: Infordb,
  ) { }

  async ngOnInit(){
    await this.checkUrenIngevuld();
    this.getSettings();
  }
  ionViewWillEnter(){
    this.post = false;
    this.planning = [];
    this.werkuren = [];

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/planning/index`, {
      user: this.user.user_id.toString(),
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          const {planning, werkuren, dagplanningExportExists} = response.data;
          this.planning = planning;
          this.werkuren = werkuren;
          this.bladeExists = dagplanningExportExists;
          this.afterPost();
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }

  afterPost(){
    for(const day of this.planning){
      const weekDay = new Date(day.date).getDay();
      day.dow = this.dow[weekDay];
      day.convDate = this.convertDate(day.date);
    }
    for(const day of this.werkuren){
      const weekDay = new Date(day.date).getDay();
      day.dow = this.dow[weekDay];
      day.convDate = this.convertDate(day.date);
    }

    if(this.werkuren.length && this.user.values['planning_werkuren'].value === 'Aan'){
      this.tab = 'werkuren';
      this.tabClass.werkuren = 'bg-white border border-bottom-0';
      this.tabClass.planning = 'border-bottom';
    }

  }

  selectTab(tab){
    this.tab = tab;

    this.tabClass.werkuren = 'border-bottom';
    this.tabClass.planning = 'border-bottom';


    this.tabClass[tab] = 'bg-white border border-bottom-0 ';
  }

  convertDate(date){
    const datum = new Date(date);
    return ('0' + datum.getDate()).slice(-2) + '-' + ('0' + (datum.getMonth() + 1)).slice(-2) + '-' + datum.getFullYear();
  }
  previousDayNotFilled(dateString) {
    let date = new Date(dateString);

    if (date.getDay() == 6 || date.getDay() == 0) {
      return false;
    }

    let subtract = 1;

    if (date.getDay() == 1){
      subtract = 3;
    }

    date.setDate(date.getDate() - subtract);
    let yesterday = this.convertDate(date);

    let yesterdaysEntry = this.dates.find(entry => entry.datum === yesterday)

    if(!yesterdaysEntry || (!yesterdaysEntry.feestdagen && !yesterdaysEntry.verlof && !yesterdaysEntry.ingevoerd)){
      return true
    }

    return false;
  }
  async getDatums() {
    this.dates = [];

    try {
      const res = await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/datums`, {
        token: this.user.api_token
      });

      this.dates = res.data;
    } catch (error) {
      this.infordb.handleError(error);
    }
  }

  callPage(page, datum) {
    localStorage.setItem('planningDatum', datum);
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  page(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  dateNow(){
    const datum = new Date();
    return datum.getFullYear() + '-' + ('0' + (datum.getMonth() + 1)).slice(-2) + '-' + ('0' + datum.getDate()).slice(-2);
  }

  addDays(dateString, days) {
    const date = new Date(dateString);
    date.setDate(date.getDate() + (days - 1));
    return date.getFullYear() + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + ('0' + date.getDate()).slice(-2);
  }

  dagplanningPeriodeIframe(){
    let periodeArr = {};
    periodeArr['start'] = this.dateNow();
    periodeArr['eind'] = this.addDays(this.dateNow(), this.settings['planning_dagplanning_periode_dagen']);

    let periodeStr = JSON.stringify(periodeArr);

		localStorage.iframeRoute = `planning/dagplanning/periode/pdf/${periodeStr}`;
	  this.page('iframe');
  }
  agendaIframe(){
    const date = new Date();
    const y = date.getFullYear();
    const m = date.getMonth()+1;
    localStorage.iframeRoute = `iframe/planning/${y}/${m}/${this.user.user_id}`
    this.page('iframe');
  }

  doRefresh(event) {
    this.ionViewWillEnter();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  dagplanningExport(){
	  localStorage.iframeRoute = `planning/dagplanning/pdf/${this.dateNow()}`
	  this.page('iframe');
  }
  hasPermission(permissionName: string): boolean {
    var found = false;
    this.user.permissions.forEach(permission => {
      if (permission.permission == permissionName) { found = true; }
    });
    return found;
  }

  getSettings(){
    for(const key in this.user.values){
      const setting = this.user.values[key];
      this.settings[setting.naam] = setting.value;
    }
  }

  async checkUrenIngevuld(){
    await this.getDatums();
    if(this.user.values.planning_app_blok_uren_incompleet?.value && this.previousDayNotFilled(this.dateNow())){
      this.infordb.notification({message: 'Zorg dat eerst dat je urenregistratie compleet is', duration: 4000})
      this.callPage('home', null)
    }
  }
}
