import { Component, OnInit } from '@angular/core';
import { Infordb } from 'infordb';
import { convertDate } from 'infordb';
import { Router } from '@angular/router';

@Component({
  selector: 'app-todo',
  templateUrl: './todo.page.html',
  styleUrls: ['./todo.page.scss'],
  providers: [Infordb],
})
export class TodoPage implements OnInit {

  todos = [];
  loading = true;
  subdomain = window.localStorage.getItem('subdomain');
  public convertDate = convertDate

  constructor(
    private infordb: Infordb,
    public router: Router,
  ) { }

  ngOnInit() {
    this.getTodos();
  }

  getTodos() {
    this.infordb.get(`https://${this.subdomain}.ikbentessa.nl/api/projecten/todo/get`)
      .then(res => {
        this.todos = res.data;

        this.fillTodos();
      })
      .catch(this.infordb.handleError);
  }

  fillTodos() {
    for (const todo of this.todos) {
      for (const custom of todo.custom) {

        if (custom.type == 'date' && custom.value) {
          custom.value = this.convertDate((custom.value)).date;
        }
        else if (custom.type == 'user' && custom.user) {
          const { name, lastname } = custom.user;
          custom.value = `${name || ''} ${lastname || ''}`;
        }
        else if (custom.type == 'machine' && custom.machine) {
          custom.value = `${custom.machine.name || ''}`;
        }
        else if (custom.type == 'checkbox') {
          custom.value = Number(custom.value) ? { icon: 'checkmark-circle', color: 'success' } : { icon: 'close-circle', color: 'danger' };
        }
        else if (custom.type == 'select') {
          let parsedData = JSON.parse(custom.data);
          let selectedOption = parsedData.options.find(option => option.value === custom.value);
          custom.value = { name: selectedOption?.name || '' };
        }
        else if (custom.type == 'checklist') {
          const list = JSON.parse(custom.value || '');
          custom.value = list.map(item => ({
            name: item.name,
            statusColor: item.statuscolor || (item.checked ? '#5cb85c' : '#999999'),
            status: item.status || ''
          }));
        }
      }
    }
    this.loading = false;
  }

  doRefresh(event){
    this.getTodos();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }
  convert(date, reverse = false) {
    let d = new Date(date);
    if(reverse){
      return ('00' + d.getFullYear()).slice(-4) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('0' + d.getDate()).slice(-2);
    }
    return ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + ('00' + d.getFullYear()).slice(-4);
  }
  editTodo(id) {
    localStorage.iframeRoute = `/iframe/projecten/taken/edit/${id}`
    this.page('iframe');
  }

  page(page) {
      try {
      this.router.navigate([`/${page.toLowerCase()}`]);
      } catch (e) {
      alert(e);
      }
  }
}
