<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>To do</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list style="height: 100%" *ngIf="loading">
    <ion-item>
      <ion-label>Laden...</ion-label>
      <ion-spinner name="lines" ></ion-spinner>
    </ion-item>
  </ion-list>

    <div class="flex-between overflow-auto p2 mt-2">
        <div class="w-100 ion-text-center mx-1">
            <a class="btn btn-primary d-flex align-items-center justify-content-center" (click)="editTodo('todo')">
                <span>Todo lijsten wijzigen</span>
            </a>
        </div>
    </div>


  <ion-card *ngFor="let todo of todos; let i = index">
    <ion-card-header>
      <ion-card-title>{{ todo.name }}</ion-card-title>
      <small>{{ todo.created_at | date: 'dd-MM-yyyy' }}</small>
    </ion-card-header>

    <ion-card-content class="pre">
      <div *ngFor="let custom of todo.custom; let j = index">
        <ion-row>
          <ion-col>
            <b>{{ custom.name }}</b>
          </ion-col>
          <ion-col>
            <b>:</b>
          </ion-col>
          <ion-col>
            <ng-container *ngIf="custom.type === 'checkbox'">
              <ion-icon
                [name]="custom.value.icon"
                [color]="custom.value.color"
                class="font-size-125 rounded">
              </ion-icon>
            </ng-container>
            <ng-container *ngIf="custom.type === 'select'">
              {{ custom.value.name ?? '' }}
            </ng-container>
            <ng-container *ngIf="custom.type === 'checklist'">
              <div *ngFor="let item of custom.value"
                   class="checklist-taak cursor-pointer d-flex font-size-075 hover-shadow px-2 py-1 rounded-pill"
                   [style.color]="item.statusColor">
              <span class="mr-2">
                <ion-icon name="radio-button-off" class="m-0"></ion-icon>
              </span>
                <span>{{ item.name }}</span>
              </div>
            </ng-container>
            <ng-container *ngIf="custom.type !== 'checkbox' && custom.type !== 'select' && custom.type !== 'checklist'">
              {{ custom.value ?? '' }}
            </ng-container>
          </ion-col>
        </ion-row>
      </div>
    </ion-card-content>
  </ion-card>


</ion-content>
