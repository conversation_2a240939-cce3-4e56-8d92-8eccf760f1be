<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button [disabled]="this.ls.beeing_calculeted" text="Terug" defaultHref="/login"></ion-back-button>
    </ion-buttons>
    <ion-title>Settings</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >

    <ion-card class="bg-white p-2">
      <div class="flex-between font-size-1 text-dark">
        <span>Local Storage</span>
        <span class="flex-between" >
          <span>{{ls.current}}MB / {{ls.max}}MB</span>
          <a (click)="calculateMaxLs()" class="ml-1" ><ion-icon name="refresh-outline"></ion-icon></a>
        </span>
      </div>
      <div class="text-muted my-1" >Wanneer uw local storage vol is kunnen er problemen voorkomen.</div>
      <ion-progress-bar [value]="ls.current / ls.max"></ion-progress-bar>
      <div *ngIf="_show.ls" >

        <div class="my-3 p-2 bg-light-grey rounded text-dark font-size-09" >
          <div class="flex-between">
            <b>Systeem</b>
            <b>{{ls.total.system}}MB</b>
          </div>
        </div>

        <div *ngIf="ls.deletable.checklists.length" class="my-3 p-2 bg-light-grey rounded text-dark font-size-09" >
          <div class="flex-between">
            <b>Lokale Checklisten</b>
            <b>{{ls.total.checklists}}MB</b>
          </div>
          <div class="flex-between my-1" *ngFor="let save of ls.deletable.checklists" >
            <span>{{save.name}}</span>
            <ion-button (click)="deleteLocalCk(save.index)" fill="clear" color="danger">{{save.kb}}KB<ion-icon name="close-outline"></ion-icon></ion-button>
          </div>
        </div>

        <div *ngIf="ls.deletable.werkbonnen.length" class="my-3 p-2 bg-light-grey rounded text-dark font-size-09" >
          <div class="flex-between">
            <b>Lokale werkbonnen</b>
            <b>{{ls.total.werkbonnen}}MB</b>
          </div>
          <div class="flex-between my-1" *ngFor="let save of ls.deletable.werkbonnen" >
            <span>{{save.name}}</span>
            <ion-button (click)="deleteLocalWerkbonnen(save.index)" fill="clear" color="danger">{{save.kb}}KB<ion-icon name="close-outline"></ion-icon></ion-button>
          </div>
        </div>

        <div *ngIf="ls.deletable.uren.length" class="my-3 p-2 bg-light-grey rounded text-dark font-size-09" >
          <div class="flex-between">
            <b>Lokale Uren</b>
            <b>{{ls.total.uren}}MB</b>
          </div>
          <div class="flex-between my-1" *ngFor="let save of ls.deletable.uren" >
            <span>{{save.name}}</span>
            <ion-button (click)="deleteLocalUren(save.index)" fill="clear" color="danger">{{save.kb}}KB<ion-icon name="close-outline"></ion-icon></ion-button>
          </div>
        </div>


      </div>
      <div class="ion-text-center mt-2">
        <ion-button fill="clear" (click)="_show.ls = !_show.ls" ><ion-icon name="{{_show.ls ? 'chevron-up-outline' : 'chevron-down-outline'}}"></ion-icon></ion-button>
      </div>
    </ion-card>

    <div class="m-2">
      <ion-button color="primary" class="w-100" (click)="changePassword()" >Wachtwoord wijzigen</ion-button>
    </div>

  </ion-card>
</ion-content>
