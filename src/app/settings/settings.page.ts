import { Component, OnInit } from '@angular/core';
import {ClientStorageService} from "../services/ClientStorage/client-storage.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-settings',
  templateUrl: './settings.page.html',
  styleUrls: ['./settings.page.scss'],
})
export class SettingsPage implements OnInit {

  public user = JSON.parse(localStorage.user || '{}');
  public ls = {
    current: 0,
    beeing_calculeted: false,
    max: Number(localStorage.max_size || 0),
    deletable: {
      checklists: [],
      werkbonnen: [],
      uren: []
    },
    total: {
      system: 0,
      checklists: 0,
      werkbonnen: 0,
      uren: 0,
    }
  }

  public _show = {
    ls: false,
  }

  constructor(
      private router: Router,
      private clientStorage: ClientStorageService,
  ) { }

  ngOnInit() {}

  ionViewWillEnter(){
    this.ls.current = this.stringToMb(JSON.stringify(localStorage));
    this.initDeletable()
  }
  ionViewWillLeave(){
    localStorage.removeItem('max_size_filler')
  }

  initDeletable(){
    this.ls.deletable.checklists = [];
    this.ls.deletable.werkbonnen = [];
    this.ls.deletable.uren = [];


    try{
      const ckLocalData = this.clientStorage.get('checklists_local_data', []);
      this.ls.total.checklists = this.stringToMb(JSON.stringify(ckLocalData));
      for(const i in ckLocalData){
        const ck = ckLocalData[i];
        this.ls.deletable.checklists.push({name: ck.name, index: i, kb: this.stringToMb(JSON.stringify(ck)) * 1000});
      }
    }
    catch(e){ alert('Checklisten data kan niet worden opgehaald!') }

    try{
      const werkbonnenLocalData = this.clientStorage.get('werkbonnen_local_data', []);
      this.ls.total.werkbonnen = this.stringToMb(JSON.stringify(werkbonnenLocalData));
      for(const i in werkbonnenLocalData){
        const save = werkbonnenLocalData[i];
        this.ls.deletable.werkbonnen.push({name: save.name, index: i, kb: this.stringToMb(JSON.stringify(save)) * 1000});
      }
    }
    catch (e){ alert('Werkbonnen data kan niet worden opgehaald!')}

    try{
      const urenLocalData = this.clientStorage.get('uren_local_data', {});
      this.ls.total.uren = this.stringToMb(JSON.stringify(urenLocalData));
      for(const i in urenLocalData){
        this.ls.deletable.uren.push({name: i, index: i, kb: this.stringToMb(JSON.stringify(urenLocalData[i])) * 1000 });
      }
    }
    catch (e){ alert('Urenregistratie data kan niet worden opgehaald!')}

    const { checklists, werkbonnen, uren } = this.ls.total;
    this.ls.total.system = Number((this.ls.current - checklists - werkbonnen - uren).toFixed(2));
  }

  //delete functions
  deleteLocalCk(i){
    const data = this.clientStorage.get('checklists_local_data', []);

    data.splice(i, 1);
    this.clientStorage.set('checklists_local_data', data);

    this.ionViewWillEnter()
    this.initDeletable();
  }
  deleteLocalUren(i){
    const data = this.clientStorage.get('uren_local_data', {});
    delete data[i]

    this.clientStorage.set('uren_local_data', data);

    this.ionViewWillEnter()
    this.initDeletable();
  }
  deleteLocalWerkbonnen(i){
    const data = this.clientStorage.get('werkbonnen_local_data', []);
    data.splice(i, 1)
    this.clientStorage.set('werkbonnen_local_data', data);

    this.ionViewWillEnter()
    this.initDeletable();
  }

  calculateMaxLs(){
    this.ls.beeing_calculeted = true;

    let filler = [];
    const interval = setInterval(() => {
      try{
        localStorage.max_size_filler = JSON.stringify(filler);
        localStorage.max_size = this.stringToMb(JSON.stringify(localStorage));

        this.ls.max = this.stringToMb(JSON.stringify(localStorage));
        filler.push(this.kb1());
      }
      catch(e){
        clearInterval(interval);
        localStorage.removeItem('max_size_filler');
        filler = [];
        this.ls.beeing_calculeted = false;
      }
    }, 40);
  }
  stringToMb(string) {
    return Number(
      ((new TextEncoder().encode(string)).length / 1000 / 1000).toFixed(2)
    );
  }
  kb1(){
    return (`66125646655438184824034357503490176636099264991633465762201498014519123891859268733983653039388726432642995143358504569007771
58598693402496866943402835041634570224118066330404568236483221494076492917098844866249914290879929866424562331479470484929530
47981071980750177177087538144356263522627349597567256092672809627220185268573884037546233149941048425721886017397002493771038
59789493522946388742872159309483907924798646897590296799087138432035293041592297258616156208443607672462374144231313952523825
41214722436789521357506910806784385239131212667915286065697223577192349536631069819291852420161751071280762096700317526464632
90928765621229518421461199169418959317189370377096223039048075197848769839858594855143546758093458201630388955491473164903161
19029733685356457419092050823362333977133993758927393621966880365414110809808625711116204972494708604941468381375412202718800
30757276143464395289644876909915866493212206250053550400385293673376701537468360960764657913786708380781323834871961191069325
5294339716425075`).repeat(40)
  }

  async changePassword() {
      localStorage.setItem('reset_password_optional', '1');
      await this.router.navigate(['/reset-password']);
  }
}
