import { Component, OnInit } from '@angular/core';
import { ActionSheetController } from '@ionic/angular';
import { timeSelect, dateSelect, Infordb } from 'infordb';

@Component({
  selector: 'app-beschikbaarheid',
  templateUrl: './beschikbaarheid.page.html',
  styleUrls: ['./beschikbaarheid.page.scss'],
	providers: [Infordb],
})
export class BeschikbaarheidPage implements OnInit {
	
	public timeSelect = timeSelect;
	public dateSelect = dateSelect;

	public user = JSON.parse(window.localStorage.getItem('user'));
  public subdomain = window.localStorage.getItem('subdomain');

  public optie = false;
  public opties = [];
  public beschikbaarheid;

  public button = true;
  public post = false;
  public dates = [];
  public loop = [];
  public maanden = ["Januari","Februari","Maart","April","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","September","<PERSON><PERSON><PERSON>","November","December"]
  public dow = ['<PERSON>ondag', 'Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag'];
  public form = {
    id: 0,
    state: false,
    date: null,
    start: null,
    end: null,
    day: false,
    type: null,
    description: '',
    optie: null,
  };
  public edit = {
    date: null,
  }

  constructor(
    private infordb: Infordb,
    private actionSheetController: ActionSheetController
  ) { }

  ngOnInit() {
    for(let i = 0; i<24; i++){
      this.loop.push(i);
    }
    if(this.user.values['beschikbaarheid_invoer'] && this.user.values['beschikbaarheid_invoer'].value === 'optie'){
      this.optie = true;
    }
    if(this.user.values['beschikbaarheid_opties'] && this.user.values['beschikbaarheid_opties'].value){
      this.opties = JSON.parse(this.user.values['beschikbaarheid_opties'].value);
    }
  }
  ionViewWillEnter(){
    this.post = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/beschikbaarheid/get`, {
      user: this.user.user_id.toString(),
    })
      .then(response => {
        if (response.status === 201) {
          this.beschikbaarheid = response.data.dates;
          this.post = true;

          this.orderDates();
          this.fillDates();
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err)
      });
  }

  onSubmit(){
    let day = 1;

		if(!this.form.date){
			this.infordb.notification('Selecteer datum!');
			return false;
		}
    if(this.form.type === null){
      this.infordb.notification('Selecteer beschikbaarheid optie!');
      return false;
    }
    if(!this.form.day){
      day = 0;
      if(!this.form.start || !this.form.end){
        this.infordb.notification('Selecteer een tijdstip!');
        return false;
      }
    }

    const startTimeDate = new Date(this.form.start);
    const endTimeDate = new Date(this.form.end);
    const date = new Date(this.form.date);


    const sDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), startTimeDate.getHours(), startTimeDate.getMinutes()).getTime();
    const eDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), endTimeDate.getHours(), endTimeDate.getMinutes()).getTime();

    if(day === 0 && sDate >= eDate){
      alert('Onjuiste eindtijd!');
      return false;
    }

    this.button  = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/beschikbaarheid/store`, {
      user: this.user.user_id,
      id: this.form.id,
      date: this.form.date,
      start: this.form.start,
      end: this.form.end,
      day: day ? '1' : '0',
      type: this.form.type,
      opmerking: this.form.description,
    })
      .then(response => {
        this.button = true;
        if (response.status === 201) {
          this.ionViewWillEnter();
          this.form.state = false;
        }
        else if(response.status === 202){
          alert("Aangegeven tijd is al ingevuld")
        }
      })
      .catch(err => {
        this.button = true;
        this.infordb.handleError(err)
      });


  }
  delete(){
    this.button = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/beschikbaarheid/delete`, {
      id: this.form.id.toString(),
    })
      .then(response => {
        this.button = true;
        if (response.status === 201) {
          this.ionViewWillEnter();
          this.form.state = false;
        }
      })
      .catch(err => {
        this.button = true;
        this.infordb.handleError(err)
      });

  }

  editModal(id){
    let day;

    for(const i in this.beschikbaarheid){
      day = this.beschikbaarheid[i].find(x => x.id == id);
      if(day){break;}
    }

    this.form.id = id;
    this.form.state = true;
    this.form.date = day.date;
    this.form.start = day.start.slice(0, 5);
    this.form.end = day.end.slice(0, 5);
    this.form.day = !!Number(day.day);
    this.form.type = Number(day.type);
    this.form.description = day.opmerking || '';
    this.form.optie = this;

    if(this.optie){
      for(const opt of this.opties){
        if(opt.start.slice(0, 5) == day.start.slice(0, 5) && opt.end.slice(0, 5) == day.end.slice(0, 5)){
          this.form.optie = opt;
        }
      }
    }

  }
  openForm(id = null){
    this.form.id = null;
    this.form.state = true;
    this.form.date = null;
    this.form.start = null;
    this.form.end = null;
    this.form.day = false;
    this.form.type = null;
    this.form.description = '';
    this.form.optie = null;
  }
  orderDates(){
    const temp = {};
    for(const i in this.beschikbaarheid){
      temp[new Date(i).getTime()] = this.beschikbaarheid[i];
    }
    this.beschikbaarheid = this.orderKeys(temp);
  }
  orderKeys(obj) {

    var keys = Object.keys(obj).sort(function keyOrder(k1, k2) {
      if (k1 < k2) return -1;
      else if (k1 > k2) return +1;
      else return 0;
    });
    var i, after = {};
    for (i = 0; i < keys.length; i++) {
      after[keys[i]] = obj[keys[i]];
      delete obj[keys[i]];
    }
    for (i = 0; i < keys.length; i++) {
      obj[keys[i]] = after[keys[i]];
    }
    return obj;
  }
  fillDates(){
    this.dates = [];
    for(const i in this.beschikbaarheid){
      const empty = {};
      for(let h = 0; h<24; h++){
        empty[h] = null;
      }

      const date = new Date(this.beschikbaarheid[i][0].date);
      const obj = {
        timestamp: i,
        dow: date.getDay(),
        date: this.convert(date),
        oriDate: this.convertOri(date),
        maand: this.maanden[date.getMonth()],
        set: empty
      };

      for(const day of this.beschikbaarheid[i]){
        if(day.day === '1'){
          for(let h = 0; h<24; h++){
            obj.set[h] = day.type;
          }
        }
        else{
          const sa = (day.date+' '+day.start).split(/[- :]/);
          const ea = (day.date+' '+day.end).split(/[- :]/);

          const sh = Number(sa[3]);
          const eh = Number(ea[3]);


          for(let z = sh; z <= eh; z++){
            obj.set[z] = day.type;
          }

        }
      }
      this.dates.push(obj);
    }
  }

  async selectOptie(){
    const buttons = [];
    for(const opt of this.opties){
      buttons.push({
        text: opt.name,
        icon: 'chevron-forward-outline',
        handler: () => {
          this.form.optie = opt;
          this.form.start = opt.start;
          this.form.end = opt.end;
        },
      });
    }

    const actionSheet = await this.actionSheetController.create({
      buttons,
    });
    await actionSheet.present();
  }

  convert(d) {
		if(typeof d == 'string'){ d = new Date(d); }
		
    return ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + d.getFullYear();
  }
  convertOri(d) {
    return  d.getFullYear() +'-'+ ('0' + (d.getMonth() + 1)).slice(-2) +'-'+ ('0' + d.getDate()).slice(-2);
  }

}
