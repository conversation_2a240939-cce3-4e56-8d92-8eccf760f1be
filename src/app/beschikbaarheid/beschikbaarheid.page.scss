.w-100{
  width: 100%;
}
.border{
  border: 1px solid;
}
.rounded{
  border-radius: 0.25rem !important;
}
.my-0{
  margin-top: 0;
  margin-bottom: 0;
}
.mr-1{
  margin-right: .5rem;
}
.ml-1{
  margin-left: .5rem;
}
.mt-1{
  margin-top: .5rem;
}
.my-1{
 margin-top: .5rem;
 margin-bottom: .5rem;
}
.m-1{
  margin: .5rem;
}
.p-1{
  padding: .5rem;
}
.py-2{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.text-success{
  color: #19d895B2;
}
.bg-success {
  background-color: #19d895B2;
}
.bg-danger{
  background-color: #ff6258B2;
}
.shadow {
  box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 15%);
}

.d-flex{
  display: flex;
  flex-wrap: nowrap;
}
.overflow-hidden{
  overflow: hidden;
}

.text{
  color: black;
}
.h-10{
  height: 10px;
}

@media (prefers-color-scheme: dark) {
  .shadow {
    box-shadow: 0.25rem 0.25rem 0.5rem rgb(0 0 0 / 35%);
  }
  .text{
    color: white;
  }
}
