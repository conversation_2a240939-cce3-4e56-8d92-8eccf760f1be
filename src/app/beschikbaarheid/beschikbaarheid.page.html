<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Beschikbaarheid</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div *ngIf="!post" class="ion-text-center ion-margin">
    <ion-spinner></ion-spinner>
  </div>

  <div *ngIf="post" >
    <div (click)="openForm()" class="my-2 ion-text-center">
      <a class="btn btn-inverse-primary" >Beschikbaarheid invullen</a>
    </div>
  </div>

  <div *ngIf="post && !dates.length" class="ion-text-center ion-margin" >
    <h5>Geen resultaten gevonden</h5>
  </div>


  <div *ngIf="dates.length && post" >
    <div *ngFor="let date of dates; let i = index" >
      <div *ngIf="i === 0 || dates[(i - 1)].maand != date.maand" >
        <h4 class="ion-text-center my-2" >{{date.maand}}</h4>
      </div>

      <ion-card  class="py-2">
        <ion-card-title (click)="edit.date == date.oriDate ? edit.date = null : edit.date = date.oriDate" class="ion-padding-horizontal d-flex ion-justify-content-between" >

          <div>
            <ion-text>{{dow[date.dow]}}</ion-text>
          </div>

          <div class="d-flex ion-align-items-center" >
            <small class="ion-margin-horizontal" >{{date.date}}</small>
            <ion-icon *ngIf="form.date !== date.oriDate" name="chevron-down-outline"></ion-icon>
            <ion-icon *ngIf="form.date === date.oriDate" name="chevron-up-outline"></ion-icon>
          </div>

        </ion-card-title>

          <div class="rounded shadow overflow-hidden ion-margin-horizontal d-flex h-10 mt-1" >
            <div *ngFor="let i of loop; " class="w-100" >
              <div *ngIf="date.set[i] === '1'" class="bg-success h-10" ></div>
              <div *ngIf="date.set[i] === '0'" class="bg-danger h-10" ></div>
              <div *ngIf="date.set[i] !== '0' && date.set[i] !== '1'" class="h-10" ></div>
            </div>
          </div>

          <div class="my-2" *ngIf="edit.date === date.oriDate">
            <section *ngIf="beschikbaarheid[date.timestamp]" >
              <section *ngFor="let day of beschikbaarheid[date.timestamp]" class="border bg-white text m-1 rounded overflow-hidden" >
                  <div (click)="editModal(day.id)" >
                    <div  class="flex-between text-dark p-1"  >
                      <div>
                        <h6 class="my-0" *ngIf="day.day === '0'" >{{day.start.slice(0, 5)}} - {{day.end.slice(0, 5)}}</h6>
                        <h6 class="my-0" *ngIf="day.day !== '0'" >Hele dag</h6>
                        <small>{{day.opmerking}}</small>
                      </div>
                      <ion-icon name="settings-outline" class="text-dark"></ion-icon>
                    </div>
                    <div [ngClass]="day.type === '0' ? 'bg-danger' : 'bg-success'" style="height: 2px" ></div>
                  </div>
                </section>
              </section>
            </div>
          </ion-card>
        </div>
  </div>




  <!-- Beschikbaarheid-->
  <div class="modal-container" *ngIf="form.state" (click)="form.state = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <ion-list class="ion-padding">

        <ion-item lines="none" >
          <ion-label>Datum</ion-label>
          <ion-button fill="clear" (click)="dateSelect.select(form, 'date')" >{{form.date ?  convert(form.date) : 'Selecteer datum'}}</ion-button>
        </ion-item>

        <div *ngIf="!optie && !form.day" >
          <ion-item lines="none"  >
            <ion-label>Van</ion-label>
            <ion-button fill="clear" (click)="timeSelect.select(form, 'start')" >{{form.start || 'Selecteer tijd'}}</ion-button>
          </ion-item>
          <ion-item lines="none"  >
            <ion-label>Tot</ion-label>
            <ion-button fill="clear" (click)="timeSelect.select(form, 'end')" >{{form.end || 'Selecteer tijd'}}</ion-button>
          </ion-item>
        </div>

        <div *ngIf="optie" >
          <ion-item (click)="selectOptie()" [disabled]="form.day" lines="none"  >
            <ion-label >Tijdstip</ion-label>
            <ion-label class="ion-text-right" *ngIf="form.optie" >{{form.optie.name}}</ion-label>
            <ion-label class="ion-text-right" *ngIf="!form.optie" ><ion-icon name="chevron-down-outline"></ion-icon></ion-label>
          </ion-item>
        </div>

        <ion-item lines="none"  >
          <ion-label>Hele dag</ion-label>
          <ion-checkbox [(ngModel)]="form.day" ></ion-checkbox>
        </ion-item>

        <ion-item lines="none"  >
          <ion-label >Opmerking:</ion-label>
          <ion-input class="ion-text-right" [(ngModel)]="form.description" ></ion-input>
        </ion-item>

        <div class="d-flex mx--1" >
          <div class="w-100 m-1 rounded shadow" [ngClass]="form.type === 1 ? 'bg-success text-white' : 'text-dark'" (click)="form.type = 1">
            <p class="ion-text-center my-0 ion-padding-vertical">Beschikbaar</p>
          </div>
          <div class="w-100 m-1 rounded shadow" [ngClass]="form.type === 0 ? 'bg-danger text-white' : 'text-dark'" (click)="form.type = 0">
            <p class="ion-text-center my-0 ion-padding-vertical">Niet beschikbaar</p>
          </div>
        </div>

        <div class="ion-text-center my-2" >
          <a (click)="delete()" *ngIf="button && form.id" class="btn btn-inverse-danger w-100 mb-1" >Verwijderen</a>
          <a (click)="onSubmit()" *ngIf="button" class="btn btn-inverse-success w-100 mb-1" >Opslaan</a>
          <ion-spinner *ngIf="!button" color="success" ></ion-spinner>
        </div>

      </ion-list>
    </ion-card>
  </div>

  <!-- Date- TimeSelect -->
  <div class="modal-container" *ngIf="timeSelect.modal" (click)="timeSelect.modal = false;" >
    <ion-card class="p-2 text-dark" style="width: 90vw; max-width: 750px;" (click)="$event.stopPropagation()" >
      <div class="d-flex ion-justify-content-around font-size-125">

        <div class="flex-between w-100" >
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

        <!--      Hours-->
        <div (scroll)="timeSelect.scrollTime('hour')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-hour" >
          <div style="height: 39px" ></div>
          <div class="ion-text-center my-4" id="custom-time-hour-{{h}}" *ngFor="let h of timeSelect.vars.hours" (click)="timeSelect.selectTimeValue('hour', h)" >
            <span class="{{timeSelect.vars.time.hour == h ? 'text-primary' : ''}}" >{{('0'+h).slice(-2)}}</span>
          </div>
          <div style="height: 39px" ></div>
        </div>

        <div class="flex-between w-50" >
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

        <!--      Minutes-->
        <div (scroll)="timeSelect.scrollTime('min')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-min" >
          <div style="height: 39px" ></div>
          <div class="ion-text-center my-4" id="custom-time-min-{{m}}" *ngFor="let m of timeSelect.vars.minutes" (click)="timeSelect.selectTimeValue('min', m)">
            <span class="{{timeSelect.vars.time.min == m ? 'text-primary' : ''}}" >{{('0'+m).slice(-2)}}</span>
          </div>
          <div style="height: 39px" ></div>
        </div>

        <div class="flex-between w-100" >
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

      </div>
      <div class="mt-1 pt-1 ion-text-right border-top" >
        <ion-button fill="clear" (click)="timeSelect.confirmTime()" >OK</ion-button>
      </div>
    </ion-card>
  </div>
  <div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;" >
    <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()" >

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)" ><ion-icon name="chevron-back-outline"></ion-icon></ion-button>
        <span class="mx-2" >{{dateSelect.vars.date.year}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
      </div>

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)" ><ion-icon name="chevron-back-outline"></ion-icon></ion-button>
        <span class="mx-2" >{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)" ><ion-icon name="chevron-forward-outline"></ion-icon></ion-button>
      </div>

      <div class="flex-between custom-date-picker-days">
        <div>ma</div>
        <div>di</div>
        <div>wo</div>
        <div>do</div>
        <div>vr</div>
        <div>za</div>
        <div>zo</div>
      </div>
      <div class="custom-date-picker">
        <div
          *ngFor="let day of dateSelect.vars.completeDays"
          [ngClass]="{
            'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
            'prev-month': day.type === 'prev',
            'current-month': day.type === 'current',
            'next-month': day.type === 'next'
          }"
          (click)="dateSelect.confirmDate(day)">
          {{ day.day }}
        </div>
      </div>

    </ion-card>
  </div>

</ion-content>


