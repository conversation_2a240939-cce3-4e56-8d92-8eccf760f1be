<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="home"></ion-back-button>
    </ion-buttons>
    <ion-title>Weekoverzichten</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>

  <ion-list style="height: 100%" *ngIf="loading">
    <ion-item lines="none">
      <ion-label>Laden...</ion-label>
      <ion-spinner name="lines" ></ion-spinner>
    </ion-item>
  </ion-list>

  <div *ngIf="!loading" >

    <div class="flex-between m-2">
      <a *ngIf="user.values['uren_hide_column']" class="btn" [ngClass]="showHidden ? 'btn-inverse-danger' : 'btn-inverse-success'" (click)="toggleHidden(!showHidden)" >
        <span>{{showHidden ? 'Items verbergen' : 'Item weergeven'}}</span>
      </a>
      <a class="btn btn-inverse-primary" (click)="yearModal = true;" slot="end" ><b>{{year}}</b></a>
    </div>

    <div >
      <ion-card >
        <ion-card-header>
          <ion-card-title>Totaal</ion-card-title>
        </ion-card-header>
        <ion-card-content class="flex-between">
          <table>
            <tr>
              <td width="200">Resterend verlofsaldo: </td>
              <td *ngIf="verlofsaldo != -999">{{verlofsaldo}} uur</td>
              <td *ngIf="verlofsaldo == -999">Niet gelukt om verlof op te halen</td>
            </tr>
            <tr>
              <td width="200">Totaal uren 100%: </td>
              <td>{{ round(yearTotal['totaaluren100']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['125']" >
              <td>Totaal uren 125%: </td>
              <td>{{ round(yearTotal['totaal_overuren125']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['150']" >
              <td>Totaal uren 150%: </td>
              <td>{{ round(yearTotal['totaal_overuren150']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['165']" >
              <td>Totaal uren 165%: </td>
              <td>{{ round(yearTotal['totaal_overuren165']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['200']" >
              <td>Totaal uren 200%: </td>
              <td>{{ round(yearTotal['totaal_overuren200']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['reisuren']" >
              <td>Reisuren: </td>
              <td>{{ round(yearTotal['totaal_reisuren']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['verlofuren']" >
              <td>Verlofuren: </td>
              <td>{{ round(yearTotal['verlof']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['bijzonderverlofuren']" >
              <td>Bijzonder verlofuren: </td>
              <td>{{ round(yearTotal['bijzonderverlof']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['feesturen']" >
              <td>Feesturen</td>
              <td>{{ round(yearTotal['feesturen']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['atv']" >
              <td>ATV uren</td>
              <td>{{ round(yearTotal['atv_uren']) }} uur</td>
            </tr>
            <tr *ngIf="!hide['atv']" >
              <td>ATV saldo</td>
              <td>{{ round(atv_uren - yearTotal['atv_uren']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['ziekteuren']" >
              <td>Ziekteuren</td>
              <td>{{ round(yearTotal['ziekteuren']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['tijdvoortijd']" >
              <td>Tijd voor tijd</td>
              <td>{{ round(yearTotal['tijdvoortijd']) }} uur</td>
            </tr>

            <tr *ngIf="!hide['kilometers']" >
              <td>Kilometers</td>
              <td>{{ roundHalf(yearTotal['kilometers']) }} km</td>
            </tr>

            <tr *ngIf="!hide['woonwerkKm']" >
              <td>Woonwerk km</td>
              <td>{{ roundHalf(yearTotal['woonwerk_reis']) }} km</td>
            </tr>

            <tr *ngIf="!hide['huisProjectKm']" >
              <td>Huisproject km</td>
              <td>{{ roundHalf(yearTotal['huisproject_reis']) }} km</td>
            </tr>
          </table>
        </ion-card-content>
      </ion-card>
    </div>

    <div *ngFor="let week of weken">
      <ion-card *ngIf="week.weeknummer != 0">
        <ion-card-header>
          <ion-card-title>Week {{ week.weeknummer }} - {{ week.maand }} {{ week.jaar }}</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <table>
            <tr>
              <td width="200">Totaal uren 100%: </td>
              <td>{{ round(week.totaaldaguren100) }} uur</td>
            </tr>

            <tr *ngIf="!hide['125']" >
              <td>Totaal uren 125%: </td>
              <td>{{ round(week.totaaloveruren125) }} uur</td>
            </tr>

            <tr *ngIf="!hide['150']" >
              <td>Totaal uren 150%: </td>
              <td>{{ round(week.totaaloveruren150) }} uur</td>
            </tr>

            <tr *ngIf="!hide['165']" >
              <td>Totaal uren 165%: </td>
              <td>{{ round(week.totaaloveruren165) }} uur</td>
            </tr>

            <tr *ngIf="!hide['200']" >
              <td>Totaal uren 200%: </td>
              <td>{{ round(week.totaaloveruren200) }} uur</td>
            </tr>

            <tr *ngIf="!hide['reisuren']" >
              <td>Reisuren: </td>
              <td>{{ round(week.reisuren) }} uur</td>
            </tr>

            <tr *ngIf="!hide['verlofuren']" >
              <td>Verlofuren: </td>
              <td>{{ round(week.verlofuren) }} uur</td>
            </tr>

            <tr *ngIf="!hide['bijzonderverlofuren']" >
              <td>Bijzonder verlofuren: </td>
              <td>{{ round(week.bijzonderverlofuren) }} uur</td>
            </tr>

            <tr *ngIf="!hide['feesturen']" >
              <td>Feesturen</td>
              <td>{{ round(week.feesturen) }} uur</td>
            </tr>

            <tr *ngIf="!hide['atv']" >
              <td>ATV</td>
              <td>{{ round(week.atv_uren) }} uur</td>
            </tr>

            <tr *ngIf="!hide['ziekteuren']" >
              <td>Ziekteuren</td>
              <td>{{ round(week.ziekteuren) }} uur</td>
            </tr>

            <tr *ngIf="!hide['tijdvoortijd']" >
              <td>Tijd voor tijd</td>
              <td>{{ round(week.tijdvoortijd) }} uur</td>
            </tr>

            <tr *ngIf="!hide['kilometers']" >
              <td>Kilometers</td>
              <td>{{ roundHalf(week.kilometers) }} km</td>
            </tr>

            <tr *ngIf="!hide['woonwerkKm']" >
              <td>Woonwerk km</td>
              <td>{{ roundHalf(week.heenreis_woonwerk) + roundHalf(week.terugreis_woonwerk) }} km</td>
            </tr>

            <tr *ngIf="!hide['huisProjectKm']" >
              <td>Huisproject km</td>
              <td>{{ roundHalf(week.heenreis_huisproject) + roundHalf(week.terugreis_huisproject)}} km</td>
            </tr>

          </table>

        </ion-card-content>
      </ion-card>
    </div>
  </div>

  <div class="modal-container" *ngIf="yearModal" (click)="yearModal = false;" >
    <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
      <div class="ion-padding overflow-auto mh-25-vh"  >
        <div *ngFor="let y of this.years">
          <div class="ion-text-center" >
            <ion-button (click)="getDates(y);" class="w-50" [ngClass]="(y === year) ? 'bg-inverse-primary rounded' : ''" fill="clear" ><ion-text class="text">{{y}}</ion-text></ion-button>
          </div>
        </div>
      </div>
    </ion-card>
  </div>

</ion-content>
