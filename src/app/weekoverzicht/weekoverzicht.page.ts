import { Component, OnInit } from '@angular/core';
import { Infordb } from "infordb";

@Component({
  selector: 'app-weekoverzicht',
  templateUrl: './weekoverzicht.page.html',
  styleUrls: ['./weekoverzicht.page.scss'],
	providers: [Infordb],
})
export class WeekoverzichtPage implements OnInit {



  public user = JSON.parse(window.localStorage.getItem('user'));
  public subdomain = window.localStorage.getItem('subdomain');
  public months = ['januari', 'februari', 'maart', 'april', 'mei', 'juni', 'juli', 'augustus', 'september', 'oktober', 'november', 'december'];
  public weken = [];
  public year = Number(new Date().getFullYear());
  public years = [];
  public loading = true;
  public yearModal = false;
  public yearTotal = [];
  public verlofsaldo = 0;
  public atv_uren = 0;

  public hide = {};
  public showHidden = false;

  constructor(
		private infordb: Infordb,
  ){}

  ngOnInit() {

    this.hide = this.user.values['uren_hide_column'] ? JSON.parse(this.user.values['uren_hide_column'].value) : {};

    this.getVerlofsaldo();
    this.getYearTotal();
    this.getAtvUren();

    this.getDates(this.year);
    for(let y = this.year; y >= (this.year - 5); y = y - 1){
      this.years.push(y);
    }
  }

  getDates(year){
    this.year = year;
    this.loading = true;
    this.yearModal = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/urenregistratie/overzicht`, {
	    token: this.user.api_token,
	    year: year.toString(),
    })
      .then((response) => {
        this.loading = false;
        this.weken = response.data;
      })
      .catch(this.infordb.handleError);

    this.getVerlofsaldo();
    this.getYearTotal();

  }


  toggleHidden(state = this.showHidden){
    this.showHidden = state;
    this.hide = this.user.values['uren_hide_column'] ? JSON.parse(this.user.values['uren_hide_column'].value) : {};
    if(state){
      this.hide = {};
    }
  }
  roundHalf(num) {
    return Math.round(num*2)/2;
  }

  round(num) {
    return Math.round(num * 100) / 100;
  }

  getYearTotal(){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/users/getyeartotal`, {
          token: this.user.api_token,
          year: String(this.year),
        })
          .then((response) => {
              this.yearTotal = response.data.totaal
          })
          .catch(this.infordb.handleError);
  }
  getVerlofsaldo(){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/users/getverlofsaldo`, {
      token: this.user.api_token,
      year: String(this.year),
    })
      .then((response) => {
        if (response.status != 404){
          this.verlofsaldo = response.data.saldo;
        }else{
          this.verlofsaldo = -999;
          alert("Er is iets misgegaan met het inladen van verlof probeer opnieuw of neem contact <NAME_EMAIL>");
        }
      })
      .catch(err => {
        this.verlofsaldo = -999;
        this.infordb.handleError(err);
      });
  }

  getAtvUren(){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/users/getAtvUren`, {
      token: this.user.api_token
    })
      .then((response) => {
        if (response.status == 200){
          this.atv_uren = response.data.atv_uren;
        }else{
          this.atv_uren = -999;
          alert("Er is iets misgegaan met het inladen van verlof probeer opnieuw of neem contact <NAME_EMAIL>");
        }
      })
      .catch(err => {
        this.atv_uren = -999;
        this.infordb.handleError(err);
      });
  }

}
