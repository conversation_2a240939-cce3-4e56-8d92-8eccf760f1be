.user-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.user-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  gap: 16px;
}

.user-info {
  overflow: hidden;
}
.user-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--ion-color-dark);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-email {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-actions {
  flex-shrink: 0;
}
.nfc-button {
  --border-radius: 20px;
  height: 36px;
  font-weight: 500;
}
