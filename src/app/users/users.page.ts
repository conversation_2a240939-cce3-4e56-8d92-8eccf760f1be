import { Component, OnInit } from '@angular/core';
import {Infordb} from "infordb";

@Component({
  selector: 'app-users',
  templateUrl: './users.page.html',
  styleUrls: ['./users.page.scss'],
  providers: [Infordb]
})
export class UsersPage implements OnInit {

  public loading = true;
  public users = [];

  constructor(
      private infordb: Infordb
  ) { }


  //Lifecycle
  async ngOnInit() {
    await this.initUsers();
  }

  //Users
  async initUsers(event?){
    this.loading = true;
    if(event){
      event.target.complete();
    }

    const { data } = await this.infordb.post('https://test.ikbentessa.app/api/users/get')
    this.users = data.users

    this.users[3].nfc = true;



    this.loading = false;
  }

  //NFC
  toggleNFC(user){
    
  }


}
