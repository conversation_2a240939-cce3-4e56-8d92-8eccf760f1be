<ion-header>
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
        </ion-buttons>
        <ion-title>Users</ion-title>
    </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">

    <!-- Loading state -->
    <div class="ion-text-center ion-padding-vertical" *ngIf="loading">
        <ion-spinner name="crescent" color="primary"></ion-spinner>
        <p class="ion-margin-top ion-color-medium">Loading users...</p>
    </div>

    <!-- Pull to refresh -->
    <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="initUsers($event)">
        <ion-refresher-content></ion-refresher-content>
    </ion-refresher>

    <!-- User cards -->
    <ion-card *ngFor="let user of users" class="user-card ion-no-margin ion-margin-bottom" button="true">
        <ion-card-content class="ion-no-padding">
            <div class="user-item">

                <div class="user-info">
                    <ion-card-title class="user-name">
                        {{user.name}} {{user.lastname}}
                    </ion-card-title>
                    <ion-card-subtitle class="user-email">
                        {{user.email}}
                    </ion-card-subtitle>
                </div>

                <!-- Action button -->
                <div class="user-actions">
                    <ion-button size="default" class="nfc-button"
                        [fill]="user.nfc ? 'solid' : 'outline'"
                        [color]="user.nfc ? 'success' : 'primary'"
                        (click)="toggleNFC(user)"
                    >
                        <ion-icon name="wifi" slot="start"></ion-icon>
                        NFC
                    </ion-button>
                </div>

            </div>
        </ion-card-content>
    </ion-card>

    <!-- Empty state -->
    <div class="ion-text-center py-5" *ngIf="!loading && users?.length === 0">
        <ion-icon name="people-outline" size="large" color="medium"></ion-icon>
        <h3 class="ion-color-medium">No users found</h3>
        <p class="ion-color-medium">Pull down to refresh the list</p>
    </div>

</ion-content>
