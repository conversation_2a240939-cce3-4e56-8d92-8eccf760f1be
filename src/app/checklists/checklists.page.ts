import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';
import {ClientStorageService} from "../services/ClientStorage/client-storage.service";

@Component({
  selector: 'app-checklists',
  templateUrl: './checklists.page.html',
  styleUrls: ['./checklists.page.scss'],
  providers: [Infordb]
})
export class ChecklistsPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public post = false;
  public local = [];

  public permissions = {};
  public templates = [];
  public checklists;
  public custom = {};

  public modals = {
    ck: null,
    delete: false,
  }

  public templatesModal = false;
  public localModal = false;


  constructor(
    private infordb: Infordb,
    private router: Router,
    private clientSorage: ClientStorageService
  ) { }

  ngOnInit(){
    for (const perm of this.user.permissions){
      this.permissions[perm.permission] = true;
    }
    if(this.user.values['checklists_index_show_keywords']){
      const template = JSON.parse(this.user.values['checklists_index_show_keywords'].value);
      for(const id in template){
        if(!this.custom[id]){
          this.custom[id] = [];
        }
        for(const keyword in template[id]){
          this.custom[id].push({
            keyword: keyword,
            name: template[id][keyword],
          })
        }
      }
    }
  };
  async ionViewWillEnter(){
    this.local = this.clientSorage.get('checklists_local_data', []);
    this.post = false;
    this.templatesModal = false;


    await this.initTemplates();
    await this.initChecklists();
    this.post = true;
  }
  initCheckists(){
    this.post

  }
  async initChecklists(){
    try{
      const form = {
        relations: ['_bv', 'klant', 'user', 'keywords', 'template', 'project'],
        active: 1,
      }
      if(localStorage.getItem('checklistProject')){
        form['project'] = localStorage.getItem('checklistProject');
        delete form['active'];

        localStorage.removeItem('checklistProject');
      }
      if(localStorage.getItem('checklistProjectTemplateId')){
        const template = localStorage.getItem('checklistProjectTemplateId');

        form['template'] = template;
        this.templates = this.templates.filter(row => row.id == template);

        localStorage.removeItem('checklistProjectTemplateId');
      }

      const { data } = await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/get`, form);
      this.checklists = data.checklists;
    }
    catch (e) {
      this.infordb.handleError(e);
    }
  }
  async initTemplates(){
    try{
      const { data } = await this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/templates`);
      this.templates = data.templates;
    }
    catch (e) {
      this.infordb.handleError(e);
    }
  }


  openTemplatesModal(){
    if(this.templates.length === 1){
      this.selectTemplate( this.templates[0].id );
      return;
    }

    this.templatesModal = true
  }
  selectTemplate(i){
    localStorage.setItem('checklistTemplateId', i);
    this.callPage('checklists/new');
  }
  selectLocal(i){
    localStorage.checklist_local_selected = i;
    localStorage.checklistTemplateId = this.local[i].template.id;

    this.localModal = false;
    this.callPage('checklists/new');
  }
  deleteLocal(i){
    this.local.splice(i, 1);
    this.clientSorage.set('checklists_local_data', this.local);
  }

  showCk(id){
    localStorage.setItem('checklistId', id);
    this.callPage('checklists/show');
  }
  editCk(id){
    const checklist = this.checklists.find(row => row.id == id);

    localStorage.setItem('checklistEditId', checklist.id);
    localStorage.setItem('checklistTemplateId', checklist.template_id);
    this.callPage('checklists/new');
  }
  copyCk(id){
    const checklist = this.checklists.find(row => row.id == id);

    localStorage.setItem('checklistCopyId', checklist.id);
    localStorage.setItem('checklistTemplateId', checklist.template_id);
    this.callPage('checklists/new');
  }
  pdfCk(id){
    const checklist = this.checklists.find(row => row.id == id);
		window.location.href = `https://${this.subdomain}.ikbentessa.nl/checklists/pdf/${checklist.token}`;
  }
  deleteCk(id){
    this.modals.ck = this.checklists.find(row => row.id == id);
    this.modals.delete = true
  }
  confirmDeleteCk(){
    this.post = false;
    this.modals.delete = false;

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/complete`, {
      checklist: this.modals.ck.id.toString()
    })
      .then(() => {
        for(const i in this.checklists){
          const checklist = this.checklists[i];
          if(checklist.id == this.modals.ck.id){
            this.checklists.splice(i, 1);
            break;
          }
        }
        this.post = true;
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }

  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  convertDate(d){
    const date = new Date(d);
    return ('0' + date.getDate()).slice(-2) + '-' + ('0' + (date.getMonth() + 1)).slice(-2) + '-' + date.getFullYear();
  }



}
