<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title *ngIf="checklist" >{{checklist.checklistnummer}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >

    <div *ngIf="!post" class="ion-text-center ion-margin">
      <ion-spinner></ion-spinner>
    </div>

    <div *ngIf="checklist && checklist.active === '1'" class="ion-text-center my-2" >
      <a class="btn btn-inverse-primary" (click)="editChecklist()" >Wijzigen</a>
    </div>

    <div *ngIf="checklist" >
      <ion-card class="bg-white"*ngIf="checklist.user" >
        <ion-list-header class="ion-padding-horizontal" (click)="showUser(!userList)" >
          <ion-label *ngIf="!userList" ><h2>User</h2></ion-label>
          <ion-label  *ngIf="userList" class="text-muted" ><h2>User</h2></ion-label>
          <ion-label *ngIf="!userList" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="userList" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <ion-list *ngIf="userList" lines="none" >

          <ion-item color="none" *ngIf="checklist.user.pnumber" >
            <ion-label>
              <h6 class="text-muted">Personeelsnummer</h6>
              <h2>{{checklist.user.pnumber}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none">
            <ion-label>
              <h6 class="text-muted">Naam</h6>
              <h2>{{checklist.user.name}} {{checklist.user.lastname}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none">
            <ion-label>
              <h6 class="text-muted">Role</h6>
              <h2>{{checklist.user.role.name}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.user.email" >
            <ion-label>
              <h6 class="text-muted">Email</h6>
              <h2>{{checklist.user.email}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.user.phone" >
            <ion-label>
              <h6 class="text-muted">Telefoonnummer</h6>
              <h2>{{checklist.user.phone}}</h2>
            </ion-label>
          </ion-item>

        </ion-list>
      </ion-card>
      <ion-card class="bg-white" *ngIf="checklist.klant">
        <ion-list-header class="ion-padding-horizontal" (click)="showKlant(!klantList)" >
          <ion-label *ngIf="!klantList" ><h2>Klant</h2></ion-label>
          <ion-label  *ngIf="klantList" class="text-muted" ><h2>Klant</h2></ion-label>
          <ion-label *ngIf="!klantList" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="klantList" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <ion-list *ngIf="klantList" lines="none" >

          <ion-item color="none" *ngIf="checklist.klant.soort_klant" >
            <ion-label>
              <h6 class="text-muted" >Soort klant</h6>
              <h2>{{checklist.klant.soort_klant}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.klant.naam" >
            <ion-label>
              <h6 class="text-muted" >Bedrijfsnaam</h6>
              <h2>{{checklist.klant.naam}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" >
            <ion-label>
              <h6 class="text-muted" >Contactpersoon</h6>
              <h2>{{checklist.klant.contactpersoon_voornaam}} {{checklist.klant.contactpersoon_achternaam}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.klant.email"  >
            <ion-label>
              <h6 class="text-muted">Email</h6>
              <h2>{{checklist.klant.email}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.klant.telefoonnummer" >
            <ion-label>
              <h6 class="text-muted">Telefoonnummer</h6>
              <h2>{{checklist.klant.telefoonnummer}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.klant.straat || checklist.klant.postcode || checklist.klant.plaats" >
            <ion-label>
              <h6 class="text-muted">Adres</h6>
              <h2>{{checklist.klant.straat}} {{checklist.klant.huisnummer}}{{checklist.klant.toevoeging}},</h2>
              <h2>{{checklist.klant.postcode}} {{checklist.klant.plaats}}</h2>
            </ion-label>
          </ion-item>

        </ion-list>
      </ion-card>

      <ion-card class="bg-white" *ngIf="checklist.template.klant_locatie">
        <ion-list-header class="ion-padding-horizontal" (click)="showLocatie(!locatieList)" >
          <ion-label *ngIf="!locatieList" ><h2>Locatie</h2></ion-label>
          <ion-label  *ngIf="locatieList" class="text-muted" ><h2>Locatie</h2></ion-label>
          <ion-label *ngIf="!locatieList" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="locatieList" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <ion-list *ngIf="locatieList" lines="none" >

          <ion-item color="none" *ngIf="this.checklist._locatie" >
            <ion-label>
              <h6 class="text-muted" >Adres</h6>
              <h2>{{this.checklist._locatie.straat ?? ''}} {{this.checklist._locatie.huisnummer ?? ''}}{{this.checklist._locatie.toevoeging ?? ''}}, {{this.checklist._locatie.plaats}}</h2>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card>

      <ion-card class="bg-white" *ngIf="checklist.project">
        <ion-list-header class="ion-padding-horizontal" (click)="showProject(!projectList)" >
          <ion-label *ngIf="!projectList" ><h2>Project</h2></ion-label>
          <ion-label  *ngIf="projectList" class="text-muted" ><h2>Project</h2></ion-label>
          <ion-label *ngIf="!projectList" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="projectList" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <ion-list *ngIf="projectList" lines="none" >

          <ion-item color="none" *ngIf="checklist.project.projectnaam && checklist.project.projectnr" >
            <ion-label>
              <h6 class="text-muted" >Project</h6>
              <h2>{{checklist.project.projectnaam ?? ''}} - {{checklist.project.projectnr ?? ''}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.project.adres && checklist.project.huisnummer && checklist.project.toevoeging" >
            <ion-label>
              <h6 class="text-muted" >Adres</h6>
              <h2>{{checklist.project.adres}} {{checklist.project.huisnummer}} {{checklist.project.toevoeging}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.project.postcode && checklist.project.woonplaats" >
            <ion-label>
              <h6 class="text-muted" >Postcode</h6>
              <h2>{{checklist.project.soort_klant}} {{checklist.project.woonplaats}}</h2>
            </ion-label>
          </ion-item>
          <ion-item color="none" *ngIf="checklist.project.projectleider" >
            <ion-label>
              <h6 class="text-muted" >Projectleider</h6>
              <h2>{{checklist.project.projectleider}}</h2>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card>
      <ion-card class="bg-white" *ngIf="ckAanvragen && ckAanvragen.length" >
        <ion-list-header class="ion-padding-horizontal" (click)="showAanvragen(!aanvragenList)" >
          <ion-label *ngIf="!aanvragenList" ><h2>Aanvragen</h2></ion-label>
          <ion-label  *ngIf="aanvragenList" class="text-muted" ><h2>Aanvragen</h2></ion-label>
          <ion-label *ngIf="!aanvragenList" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="aanvragenList" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <ion-list *ngIf="aanvragenList" lines="none" >

          <ion-item color="none" *ngFor="let id of ckAanvragen" >
            <ion-icon color="primary" name="checkbox-outline"></ion-icon>
            <ion-label class="ion-margin-horizontal" >{{aanvragen[id].aanvraagnummer}}</ion-label>
          </ion-item>

        </ion-list>
      </ion-card>
      <ion-card class="bg-white" *ngIf="checklist.files.length" >
        <ion-list-header class="ion-padding-horizontal" (click)="showFiles(!filesList)" >
          <ion-label *ngIf="!filesList" ><h2>Bestanden</h2></ion-label>
          <ion-label  *ngIf="filesList" class="text-muted" ><h2>Bestanden</h2></ion-label>
          <ion-label *ngIf="!filesList" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="filesList" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <div *ngIf="filesList" >

          <div class="m-2 text">
            <ion-button (click)="download(file.src)" class="my-1 w-100" *ngFor="let file of checklist.files" >
              <ion-text>{{file.name}}</ion-text>
            </ion-button>
          </div>

        </div>
      </ion-card>

      <div class="text mx-2 mt-3" *ngIf="checklist.details.length" >
        <ion-text >Details</ion-text>
      </div>
      <ion-card *ngFor="let detail of checklist.details; let index = index;" >
        <ion-list-header class="ion-padding-horizontal" (click)="showDetail(index)" >
          <ion-label *ngIf="!showDetails[index]" ><h2>{{detail.title}}</h2></ion-label>
          <ion-label  *ngIf="showDetails[index]" class="text-muted" ><h2>{{detail.title}}</h2></ion-label>
          <ion-label *ngIf="!showDetails[index]" class="ion-text-right" ><h2><ion-icon name="chevron-down-outline"></ion-icon></h2></ion-label>
          <ion-label *ngIf="showDetails[index]" class="ion-text-right" ><h2><ion-icon name="chevron-up-outline"></ion-icon></h2></ion-label>
        </ion-list-header>
        <section *ngIf="showDetails[index]" >
          <ion-list>
            <ion-item *ngIf="detail.template.img === '1' && detail.image">
              <ion-label>
                <h4 class="text-muted" >Afbeelding</h4>
                <img class="rounded" src="https://{{subdomain}}.ikbentessa.nl/api/file/{{detail.image}}" >
              </ion-label>
            </ion-item>
            <ion-item *ngIf="detail.template.description === '1'">
              <ion-label>
                <h4 class="text-muted" >Omschrijving</h4>
                <h2 [innerHTML]="detail.description"></h2>
              </ion-label>
            </ion-item>
            <ion-item *ngIf="detail.template.adjustment === '1'">
              <ion-label>
                <h4 class="text-muted" >Commerciële aanpassing</h4>
                <h2>€ {{detail.adjustment }}</h2>
              </ion-label>
            </ion-item>
            <ion-item *ngIf="detail.template.price === '1'">
              <ion-label>
                <h4 class="text-muted" >Prijs</h4>
                <h2>€ {{detail.price }}</h2>
              </ion-label>
            </ion-item>
          </ion-list>
          <span class="d-block mx-5 my-2 h-1 bg-reverse" ></span>
          <ion-list>
            <ion-item *ngFor="let value of detail.values" >
              <ion-label>
                <h4 class="text-muted" >{{value.name}}</h4>
                <h2 [innerHTML]="value.value"></h2>
              </ion-label>
            </ion-item>
          </ion-list>
        </section>
      </ion-card>

      <div class="bg-white">
        <ion-list>
          <ion-list-header>
            <ion-label><h2>Checklist</h2></ion-label>
          </ion-list-header>
          <div *ngFor="let item of checklist.keywords" [ngClass]="item.type !== 'header' ? 'border-bottom' : ''" >

            <!--text, number, select, select_edit & time-->
            <ion-item lines="none"  *ngIf="item.type === 'text' || item.type === 'number' || item.type === 'select' || item.type === 'select_edit' || item.type === 'time'" >
              <ion-label position="floating" >{{item.name}}</ion-label>
              <ion-input [readonly]="true" [value]="item.value" ></ion-input>
            </ion-item>

            <ion-item lines="none"  *ngIf="item.type === 'timestamp_button'" >
              <ion-label position="floating" >{{item.name}}</ion-label>
              <ion-input [readonly]="true" [value]="convertDate(item.value).date + ' ' + convertDate(item.value).time" ></ion-input>
            </ion-item>

            <!--textarea-->
            <div *ngIf="item.type === 'textarea'" class="p-2 text-dark font-size-1 ">
              <span>{{item.name}}</span>
              <div [innerHTML]="item.value" ></div>
            </div>

            <!--date-->
            <ion-item lines="none"  *ngIf="item.type === 'date'" >
              <ion-label position="floating" >{{item.name}}</ion-label>
              <ion-input [readonly]="true" [value]="convertDate(item.value).date" ></ion-input>
            </ion-item>

            <!--user_select-->
            <ion-item lines="none"  *ngIf="item.type === 'user_select'" >
              <ion-label position="floating" >{{item.name}}</ion-label>
              <ion-input [readonly]="true" [value]="item.user ? (item.user.name+' '+item.user.lastname) : ''" ></ion-input>
            </ion-item>

            <!--Image-->
            <ion-item *ngIf="item.type == 'image'">
              <div>
                <ion-label>{{item.name}}</ion-label>
                <div class="flex-align overflow-auto my-2">
                  <div *ngFor="let file of item.files" class="mx-1 bg-img-center rounded-5 w-px-75 h-px-75 min-w-75 mx-1 position-relative" (click)="openFile(file.src)" [ngStyle]="{background: 'url(https://'+subdomain+'.ikbentessa.nl/api/file/explorer/files/'+file.src+')'}"  >
                    <div class="position-absolute" ></div>
                  </div>
                </div>
              </div>
            </ion-item>

            <!--Card break-->
            <div class="py-2 bg-light-grey" *ngIf="item.type == 'card_break'" ></div>

            <!--header-->
            <div class="bg-white p-2" *ngIf="item.type == 'header'" >
              <span style="font-size: {{item.data.size}}; color: {{item.data.color}}; font-weight: {{item.data.weight}}" >{{item.name}}</span>
            </div>

            <!--Custom row-->
            <div class="bg-white" *ngIf="item.type == 'custom_row'">
              <div *ngFor="let row of item.value; let crIndex = index;" class="border-bottom border-dark my-2" >
                <div *ngFor="let value of row" >

                  <div class="flex-between p-2 text-dark font-size-1" *ngIf="value.type == 'header'">
                    <span>{{value.name}}:</span>
                    <span>{{value.value}}</span>
                  </div>

                  <ion-item *ngIf="value.type == 'text'" >
                    <ion-label>{{value.name}}</ion-label>
                    <ion-input [readonly]="true" class="ion-text-right" [(ngModel)]="value.value" ></ion-input>
                  </ion-item>

                  <ion-item *ngIf="value.type == 'select'" >
                    <ion-label>{{value.name}}</ion-label>
                    <ion-input [readonly]="true" class="ion-text-right" [(ngModel)]="value.value" ></ion-input>
                  </ion-item>

                  <ion-item *ngIf="value.type == 'date'" >
                    <ion-label>{{value.name}}</ion-label>
                    <ion-datetime [readonly]="true" pickerFormat="DD-MM-YYYY" displayFormat="DD-MM-YYYY" class="ion-text-right" [(ngModel)]="value.value" ></ion-datetime>
                  </ion-item>

                </div>
              </div>
            </div>

            <!--Count attributes-->
            <div class="bg-white p-2 text-black font-size-1" *ngIf="item.type == 'count_data_attribute'">
              <span>{{item.name}}</span>
              <span class="mx-1" [innerHTML]="countAttributes(item)"></span>
            </div>

            <!--Non expected values-->
            <div class="bg-white px-2 py-1 text-black font-size-1" *ngIf="item.type == 'non_expected_values'">
              <div *ngFor="let row of checklist.keywords" >
                <div class="pb-1" *ngIf="nonExpectedValue(row)">
                  <span>• {{row.name}}</span>
                  <div *ngFor="let attr of (item.data.additional_data ? item.data.additional_data : [])">
                    <span *ngIf="row.data && row.data[attr.attribute]" class="ml-1" >- {{attr.name}}: {{row.data[attr.attribute]}}</span>
                  </div>
                </div>
              </div>
            </div>

            <!--Signature-->
            <div *ngIf="item.type == 'signature'" class="bg-white">
              <div class="font-size-1 text-black p-2" >{{item.name}}</div>
              <img src="https://{{subdomain}}.ikbentessa.nl/api/file/{{item.value}}" alt="" class="w-100" style="background-color: #00000005;">
            </div>

            <!--file & opmerkingen-->
            <div *ngIf="item.file" class="px-2 py-1" >
              <img class="w-100 rounded shadow" src="https://{{subdomain}}.ikbentessa.nl/api/file/{{item.file}}" >
            </div>
            <div *ngIf="item.opmerking" class="px-2 py-1" >
              <span class="text-muted">{{item.opmerking}}</span>
            </div>

            <!--dataset-->
            <ion-list *ngIf="item.type === 'dataset'">
              <ion-item *ngFor="let row of item.value" >
                <ion-label>
                  <h4>{{row.name}}</h4>
                  <h2>{{row.value}}</h2>
                </ion-label>
              </ion-item>
            </ion-list>


          </div>
        </ion-list>
      </div>

    </div>

  </ion-card>
</ion-content>
