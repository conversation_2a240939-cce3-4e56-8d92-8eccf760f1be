import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb } from 'infordb';
import {locate} from "ionicons/icons";

@Component({
  selector: 'app-show',
  templateUrl: './show.page.html',
  styleUrls: ['./show.page.scss'],
	providers: [Infordb]
})
export class ShowPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public post = false;

  public checklist;
  public aanvragen;
  public ckAanvragen;
  public showDetails = {};

  public klantList = false;
  public locatieList = false;
  public userList = false;
  public aanvragenList = false;
  public filesList = false;
  public projectList = false;

  constructor(
    private router: Router,
    private infordb: Infordb,
  ) { }

  ngOnInit() {}
  ionViewWillEnter() {
    const id = localStorage.getItem('checklistId');

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/show`, {
      checklist: id.toString(),
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          const data = response.data
          this.checklist = data.checklist;
          this.aanvragen = data.aanvragen;
          this.ckAanvragen = JSON.parse(this.checklist.aanvragen);
          this.postLoops()
        }
      })
      .catch(err => {
        this.post = true;
        alert('Er is iets misgegaan, probeer opnieuw of stuur deze <NAME_EMAIL>. ' + JSON.stringify(err.status));
      });
  }

  postLoops(){
    for(const item of this.checklist.keywords){
      item.data = JSON.parse(item.data || '[]');

      if(item.type === 'dataset'){
        const json = JSON.parse(item.value);
        item.value = []
        for(let n in json){
          item.value.push({name: n, value:json[n]});
        }
      }
      if(item.type === 'custom_row'){
        item.value = JSON.parse(item.value || []);
      }
    }
  }

  countAttributes(item){
    const attr = item.data.attribute;
    let count = 0;

    for(const row of this.checklist.keywords){
      if(!row.data || !row.data.expected_value || !row.data[attr]){continue;}
      if(row.data[attr] != item.data.attribute_value){continue;}
      if(!row.value){continue;}
      if(this.nonExpectedValue(row)){
        count++;
      }
    }
    return count;
  }
  verifyParent(item){
    for(const row of this.checklist.keywords){
      if(row.keyword == item.parent_keyword && row.value == item.parent_value){return true;}
    }
    return false;
  }
  nonExpectedValue(item){
    if(!item.value || !item.data || !item.data.expected_value){return false;}

    for(const v of item.data.expected_value){
      if(v == item.value){return false;}
    }

    return true;
  }

  showUser(state){
    this.userList = state;

    this.klantList = false;
    this.locatieList = false;
    this.aanvragenList = false;
    this.filesList = false;
    this.projectList = false;
  }
  showKlant(state){
    this.klantList = state;

    this.userList = false;
    this.locatieList = false;
    this.aanvragenList = false;
    this.filesList = false;
  }
  showLocatie(state){
    this.locatieList = state;

    this.userList = false;
    this.klantList = false;
    this.aanvragenList = false;
    this.filesList = false;
    this.projectList = false;
  }
  showProject(state){
    this.projectList = state;

    this.userList = false;
    this.klantList = false;
    this.aanvragenList = false;
    this.filesList = false;
  }
  showAanvragen(state){
    this.aanvragenList = state;

    this.klantList = false;
    this.locatieList = false;
    this.userList = false;
    this.filesList = false;
    this.projectList = false;
  }
  showFiles(state){
    this.filesList = state;

    this.klantList = false;
    this.locatieList = false;
    this.userList = false;
    this.aanvragenList = false;
    this.projectList = false;
  }
  showDetail(index){
    this.showDetails[index] = !this.showDetails[index];
  }

  editChecklist(){
    localStorage.setItem('checklistEditId', this.checklist.id);
    localStorage.setItem('checklistTemplateId', this.checklist.template_id);
    this.callPage('checklists/new');
  }

  convertDate(date){
    let d = new Date(date);
    if (isNaN(d.getTime())) {
      date = Number(date);
      d = new Date(date);
    }
    const datum = ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + d.getFullYear();
    const tijd = ('0' + d.getHours()).slice(-2) + ':' + ('0' + d.getMinutes()).slice(-2);
    const reverse = d.getFullYear()+'-'+`0${d.getMonth()+1}`.slice(-2)+'-'+`0${d.getDate()}`.slice(-2);
    return {date: datum, time: tijd, reverse: reverse};
  }
  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }
  download(src){
    const link = 'https://' + this.subdomain + '.ikbentessa.nl/api/file/' + src;
    window.open(link);
  }

  openFile(src){
    window.location.href = `https://${this.subdomain}.ikbentessa.nl/api/file/explorer/files/${src}`
  }

}
