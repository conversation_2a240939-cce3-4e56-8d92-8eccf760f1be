<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Checklists</ion-title>
    <div slot="end" class="mx-4" *ngIf="!post" ><ion-spinner slot="end" ></ion-spinner></div>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-card class="m-0 bg-inverse-secondary m-h-100" >


  <div class="ion-text-center my-2">
    <a class="btn-sm btn-inverse-primary mx-1" *ngIf="local.length" (click)="localModal = true;" >Lokale checklists ( {{local.length}} )</a>
    <a class="btn-sm btn-inverse-success mx-1" *ngIf="permissions['Checklists aanmaken']" (click)="openTemplatesModal()" >Nieuwe checklist</a>
  </div>

  <div *ngIf="post && checklists && !checklists.length" class="ion-text-center ion-margin" >
    <h4>Geen checklists gevonden</h4>
  </div>

  <ion-card *ngFor="let ck of checklists">
    <ion-list lines="none">
      <ion-item>
        <ion-label>
          <h1>{{ck.checklistnummer}}</h1>
        </ion-label>
        <ion-text class="ion-text-right ion-align-self-start ion-margin-vertical text-muted" >{{convertDate(ck.datum)}}</ion-text>
      </ion-item>
      <div class="p-2" >
        <div class="my-2" *ngIf="ck.klant" >
          <ion-label class="d-block" >Klant / Opdrachtgever</ion-label>
          <ion-text class="font-size-1125 text-dark" >{{ck.klant.naam || (ck.klant.contactpersoon_voornaam+' '+ck.klant.contactpersoon_achternaam)}}</ion-text>
        </div>
        <div class="my-2" *ngIf="ck.project" >
          <ion-label class="d-block" >Project</ion-label>
          <ion-text class="font-size-1125 text-dark" >{{ck.project.projectnr}} | {{ck.project.projectnaam}}</ion-text>
        </div>
        <div *ngIf="custom[ck.template_id] && custom[ck.template_id].length" >
          <div class="my-2" *ngFor="let row of custom[ck.template_id]" >
            <ion-label class="d-block" >{{row.name}}</ion-label>
            <ion-text class="font-size-1125 text-dark" *ngIf="ck.keywords_by[row.keyword]" [innerHTML]="ck.keywords_by[row.keyword].value" ></ion-text>
          </div>
        </div>
        <div class="my-2" *ngIf="ck.user" >
          <ion-label class="d-block" >Auditor</ion-label>
          <ion-text class="font-size-1125 text-dark" >{{ck.user.name+' '+ck.user.lastname}}</ion-text>
        </div>

        <div class="flex-between overflow-auto">
          <div class="w-100 ion-text-center mr-1">
            <a class="btn btn-inverse-success w-100" (click)="showCk(ck.id)" ><ion-icon name="eye-outline"></ion-icon></a>
            <small class="text-success" >Inzien</small>
          </div>
          <div class="w-100 ion-text-center mx-1" *ngIf="ck.active === '1'" >
            <a class="btn btn-inverse-primary w-100" (click)="editCk(ck.id)" ><ion-icon name="create-outline"></ion-icon></a>
            <small class="text-primary" >Wijzigen</small>
          </div>
          <div class="w-100 ion-text-center mx-1">
            <a class="btn btn-inverse-primary w-100" (click)="copyCk(ck.id)" ><ion-icon name="copy-outline"></ion-icon></a>
            <small class="text-primary" >Kopiëren</small>
          </div>
          <div class="w-100 ion-text-center mx-1" *ngIf="ck.template && ck.template.pdf === '1'" >
            <a class="btn btn-inverse-warning w-100" (click)="pdfCk(ck.id)" ><ion-icon name="document-outline"></ion-icon></a>
            <small class="text-warning" >PDF</small>
          </div>
          <div class="w-100 ion-text-center ml-1" *ngIf="ck.active === '1'" >
            <a class="btn btn-inverse-danger w-100" (click)="deleteCk(ck.id)" ><ion-icon name="close-outline"></ion-icon></a>
            <small class="text-danger" >Afronden</small>
          </div>
        </div>

      </div>
    </ion-list>
  </ion-card>

  </ion-card>
</ion-content>

<!--  Template modal-->
<div class="modal-container" *ngIf="templatesModal" (click)="templatesModal = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item lines="none" >
      <ion-label>Selecteer template</ion-label>
    </ion-item>
    <div *ngFor="let template of templates" class="my-2" >
      <div class="ion-text-center my-2"  >
        <ion-button color="primary" (click)="selectTemplate(template.id)" class="text" fill="clear" ><ion-text>{{template.name}}</ion-text></ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--  Local modal-->
<div class="modal-container" *ngIf="localModal" (click)="localModal = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-item lines="none" >
      <ion-label>Selecteer checklist</ion-label>
    </ion-item>
    <div *ngFor="let row of local; let i = index" class="my-2" >
      <div class="flex-between w-100 my-2"  >
        <ion-button class="mx-2" color="primary" fill="clear" (click)="selectLocal(i)" >
          <div class="flex-between mx--1">
            <div class="ion-text-left mx-1" >{{row.name}}</div>
            <span>|</span>
            <div class="mx-2 ion-text-left" >
              <small class="d-block" >{{row.time.date}} {{row.time.time}}</small>
              <small class="d-block" >{{row.template.name}}</small>
            </div>
          </div>
        </ion-button>

        <ion-button class="mx-1" fill="clear" color="danger" (click)="deleteLocal(i)" *ngIf="!this.user.values['app_local_delete_off']"><ion-icon name="close-outline"></ion-icon></ion-button>

      </div>
    </div>
  </ion-card>
</div>

<!--  Local modal-->
<div class="modal-container" *ngIf="modals.delete" (click)="modals.delete = false;" >
  <ion-card class="p-2" (click)="$event.stopPropagation()" >
    <div class="font-size-1 py-2" >Weet je zeker dat je de checklist <b>{{modals.ck.checklistnummer}}</b> wilt afronden</div>
    <div class="ion-text-center pt-2">
      <a class="btn btn-danger" (click)="confirmDeleteCk()" >Afronden</a>
    </div>
  </ion-card>
</div>
