<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">

      <ion-button>
        <ion-back-button text="Terug" defaultHref="/projecten"></ion-back-button>
      </ion-button>

    </ion-buttons>

    <ion-title>Project {{project?.projectnr ?? ''}} Checklists</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div *ngIf="templates.length" >
    <div *ngFor="let row of templates;">
      <ion-card>
        <ion-card-header>
          <ion-card-title>{{row.name}}</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <div class="flex-between overflow-auto" *ngIf="row.checklist">
            <div class="w-100 ion-text-center mr-1">
              <a class="btn btn-inverse-success w-100" (click)="showCk(row.checklist.id)" ><ion-icon name="eye-outline"></ion-icon></a>
              <small class="text-success" >inzien</small>
            </div>
            <div class="w-100 ion-text-center mx-1">
              <a class="btn btn-inverse-warning w-100" (click)="pdfCk(row.checklist.id)" ><ion-icon name="document-outline"></ion-icon></a>
              <small class="text-warning" >PDF</small>
            </div>
          </div>
          <div class="overflow-auto" *ngIf="!row.checklist && row.canBeMade">
            <div class="w-100 ion-text-center">
              <a class="btn btn-inverse-primary w-100" (click)="row.local !== null ? localCk(row.local) : createCk(row.id)" ><ion-icon name="add-outline"></ion-icon></a>
              <small class="text-primary" >{{ row.local !== null ? 'Lokaal opgeslagen checklist afmaken' : 'Checklist maken' }}</small>
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
  <ion-card>
    <ion-card-header>
      <ion-card-title>Samenvatting</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <div class="flex-between overflow-auto">
        <div class="w-100 ion-text-center mx-1">
          <a class="btn btn-inverse-warning w-100" (click)="pdfCk('project')" ><ion-icon name="document-outline"></ion-icon></a>
          <small class="text-warning" >PDF</small>
        </div>
      </div>
    </ion-card-content>
  </ion-card>
</ion-content>
