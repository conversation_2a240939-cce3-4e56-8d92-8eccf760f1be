import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {Infordb} from 'infordb';
import {ClientStorageService} from "../../services/ClientStorage/client-storage.service";


@Component({
  selector: 'app-new',
  templateUrl: './project.page.html',
  styleUrls: ['./project.page.scss'],
  providers: [Infordb]
})
export class ProjectPage implements OnInit {

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public templates = [];
  public setting = JSON.parse(this.user.values['checklist_custom_project_flow_stappen'].value);
  public local = null;
  public project = null;

  constructor(
    private infordb: Infordb,
    private router: Router,
    private clientSorage: ClientStorageService
  ) { }

  ngOnInit() {

  }
  async ionViewWillEnter(){
    this.local = this.clientSorage.get('checklists_local_data', [])
    await this.infordb.post(`https://${localStorage.subdomain}.ikbentessa.nl/api/projecten/get`, {id: localStorage.getItem('checklistProject'), relations: ['checklists.keywords']})
      .then(response => {
        if (response.status === 200) {
          this.project = response.data;
        }
      })
      .catch(err => {
        this.infordb.handleError(err);
      });
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/templates`)
      .then(response => {
        if (response.status === 201) {
          this.templates = response.data.templates;
          this.handleTemplates();
        }
      })
      .catch(err => {
        this.infordb.handleError(err);
      });
  }

  handleTemplates() {
    let ordered = [];
    for (const set of this.resetIndex(this.setting)) {
      let template = this.templates.find( template => template.id == set.template);
      template.checklist = this.project.checklists.find( checklist => checklist.template_id == template.id) ?? null;
      if (!template) {
        continue;
      }
      ordered.push(template);
    }
    this.templates = ordered;
    for (const temp of this.templates) {
      temp.canBeMade = this.canBeMade(temp);
      const local = this.local.findIndex( local => local.template.id == temp.id && local.forcedProject == this.project.id);
      temp.local = local > -1 ? local : null;
    }
  }

  resetIndex(arr){
    const rtn = [];
    for(const index in arr){
      rtn.push(arr[index]);
    }
    return rtn;
  }

  canBeMade(template){
    let set = this.resetIndex(this.setting).find( set => set.template == template.id);
    if (!set || (!set.vereiste_templates && !set.vereist_antwoord)){return true;}

    if(set.vereiste_templates){
      let foundAll = true;
      for (const temp of set.vereiste_templates) {
        let found = this.project.checklists.find( checklist => checklist.template_id == temp);
        if (!found) {
          foundAll = false;
          break;
        }
      }
      if (!foundAll) {
        return false;
      }
    }

    if(set.vereist_antwoord){
      const vereist = JSON.parse(set.vereist_antwoord);
      const template = this.templates.find( template => template.id == vereist.template);
      if(!template.checklist){return false;}
      const keyword = template.checklist.keywords.find( keyword => keyword.keyword_item.id == vereist.keyword);
      if(!keyword || keyword.value != vereist.answer){return false;}
    }
    return true;
  }


  pdfCk(id){
    if (id == 'project'){
      window.location.href = `https://${this.subdomain}.ikbentessa.nl/checklists/pdf/project/${this.project.id}`;
    }


    const checklist = this.project.checklists.find(row => row.id == id);
    window.location.href = `https://${this.subdomain}.ikbentessa.nl/checklists/pdf/${checklist.token}`;
  }
  showCk(id){
    localStorage.setItem('checklistId', id);
    this.callPage('checklists/show');
  }
  createCk(id){
    localStorage.setItem('checklistTemplateId', id);
    localStorage.setItem('checklistForcedProject', this.project.id);
    this.callPage('checklists/new');
  }
  localCk(index){
    localStorage.checklist_local_selected = index;
    localStorage.checklistTemplateId = this.local[index].template.id;
    localStorage.setItem('checklistForcedProject', this.project.id);
    this.callPage('checklists/new');
  }

  backButton(){
    this.callPage('projecten');
  }
  callPage(page){
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }




}
