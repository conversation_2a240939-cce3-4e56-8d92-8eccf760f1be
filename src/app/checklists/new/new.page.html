<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">

      <ion-button (click)="backButton()">
        <ion-icon name="arrow-back-outline" class="mr-1"></ion-icon>
        <span>Terug</span>
      </ion-button>

    </ion-buttons>

    <ion-title *ngIf="!checklist">Checklist</ion-title>
    <ion-title *ngIf="checklist">{{checklist.checklistnummer}} <span *ngIf="copy">(kopie)</span></ion-title>

    <div slot="end" class="mx-4" *ngIf="settings.silent_save_loader"><ion-spinner slot="end"></ion-spinner></div>
    <div slot="end" class="mx-3" *ngIf="!settings.silent_save_loader"><ion-button fill="clear" color="dark"
        (click)="silentSubmit()"><ion-icon name="save-outline"></ion-icon></ion-button></div>

  </ion-toolbar>
</ion-header>

<ion-content (click)="settings.page_loaded = true">
  <ion-card class="m-0 bg-inverse-secondary m-h-100">

    <div *ngIf="!post" class="ion-text-center ion-margin">
      <ion-spinner></ion-spinner>
    </div>

    <div *ngIf="post">

      <div *ngIf="local.index !== null && local.data[local.index]" class="mx-2 py-1">
        <div class="alert alert-success m-0 d-flex">Lokale versie: <b>{{local.data[local.index].name}}</b></div>
      </div>

      <ion-card class="bg-white mx-2 px-2 mb-0">
        <div class="flex-between">
          <h6 class="text-dark m-0 py-2">{{selectedKlant ? selectedKlant.titel : 'Klant selecteren'}}</h6>
          <ion-button fill="clear" (click)="modals.klant = true;" *ngIf="!forcedProject"><ion-icon
              name="swap-horizontal-outline"></ion-icon></ion-button>
        </div>
      </ion-card>
      <ion-card *ngIf="template.klant_locatie != 0" class="bg-white mx-2 px-2 mb-0">
        <div class="flex-between">
          <h6 class="text-dark m-0">{{selectedLocatie ? selectedLocatie.adres : 'Locatie selecteren'}}</h6>
          <ion-button fill="clear" (click)="modals.locatie = true;"><ion-icon
              name="swap-horizontal-outline"></ion-icon></ion-button>
        </div>
      </ion-card>

      <ion-card *ngIf="template.project != 0 && !forcedProject" class="bg-white mx-2 px-2 mb-0">
        <div class="flex-between">
          <h6 class="text-dark m-0">{{selectedProject ? selectedProject.projectnaam : 'Project selecteren'}}</h6>
          <ion-button fill="clear" (click)="modals.project = true;"><ion-icon
              name="swap-horizontal-outline"></ion-icon></ion-button>
        </div>
      </ion-card>

      <ion-list *ngIf="modules['Aanvragen'] && selectedKlant && aanvragen[selectedKlant.id]" class="bg-unset">
        <ion-card class="bg-white">
          <span class="d-block p-2 font-size-1 text-black">Aanvragen</span>
          <ion-item *ngFor="let anv of aanvragen[selectedKlant.id]" lines="none">
            <ion-checkbox [(ngModel)]="selectedAanvragen[anv.id]"></ion-checkbox>
            <ion-label class="ion-margin-horizontal">{{anv.aanvraagnummer}}</ion-label>
          </ion-item>
        </ion-card>
      </ion-list>


      <div class="flex-between mx-1 text-dark font-size-1">
        <div class="d-flex overflow-auto py-3" [ngClass]="settings.silent_save_loader ? 'opacity-25' : ''" id="top-nav">
          <div *ngFor="let list of pages.list" disabled="" (click)="navSelect(list.title);" id="top-nav-{{list.title}}"
            style="border-radius: 1rem"
            class="bg-white px-2 py-1 my-1 mx-2 shadow nobr  {{pages.index == list.title ? '' : 'opacity-50'}}">
            <div class="ion-text-center">{{list.title}}</div>
            <div class="font-size-075 text-muted ion-text-center px-1">#{{list.lowest_order}} <span
                style="margin: 0 .15rem">/</span> #{{list.highest_order}}</div>
          </div>
        </div>
      </div>


      <!--    Details-->
      <ion-card class="bg-white" *ngFor="let detail of details; let index = index;">
        <div class="px-2 d-flex ion-justify-content-between ion-align-items-center">
          <h5 class="m-0 text">{{detail.name}}</h5>
          <div>
            <ion-button (click)="redoDetail(index)" *ngIf="detail.confirmed" fill="clear" color="warning"><ion-icon
                name="refresh-outline"></ion-icon></ion-button>
            <ion-button (click)="removeDetail(index)" fill="clear" color="danger"><ion-icon
                name="close-outline"></ion-icon></ion-button>
          </div>
        </div>
        <section *ngIf="!detail.confirmed">
          <ion-list lines="none" class="content-bg-unset">
            <ion-item class="my-2">
              <ion-label>Titel*</ion-label>
              <ion-input [(ngModel)]="detail.title" [value]="detail.title" class="ion-text-right"></ion-input>
            </ion-item>
            <ion-item class="my-2" *ngIf="detail.detailTemplate.img === '1'">
              <div class="w-100">
                <div class="my-1 d-flex ion-align-items-end ion-justify-content-between">
                  <ion-label>Afbeelding</ion-label>
                  <ion-button *ngIf="detail.image.src" (click)="redoDetailImage(index)" fill="clear"
                    class="text m-0"><ion-icon name="refresh-outline"></ion-icon></ion-button>
                </div>
                <div class="p-2 m-1 shadow rounded ion-text-center" (click)="selectDetailImage(index)"
                  *ngIf="!detail.image.src">
                  <h4>Selecteer afbeelding</h4>
                </div>
                <div *ngIf="detail.image.src">
                  <img class="w-100 rounded shadow"
                    src="https://{{subdomain}}.ikbentessa.nl/api/file/{{detail.image.src}}">
                </div>
              </div>
            </ion-item>
            <ion-item class="my-2" *ngIf="detail.detailTemplate.description === '1'">
              <div class="w-100">
                <ion-label class="my-1">Omschrijving</ion-label>
                <div>
                  <textarea [froalaEditor] [(ngModel)]="detail.description" [innerHtml]="detail.description"></textarea>
                </div>
              </div>
            </ion-item>
            <ion-item class="my-2" *ngIf="detail.detailTemplate.adjustment === '1'">
              <ion-label>Commerciële aanpassing</ion-label>
              <ion-input [(ngModel)]="detail.adjustment" [value]="detail.adjustment"
                (change)="calculateDetailPrice(index)" type="number" class="ion-text-right"></ion-input>
            </ion-item>
            <ion-item class="my-2 opacity-50" *ngIf="detail.detailTemplate.price === '1'">
              <ion-label>Prijs</ion-label>
              <ion-input [(ngModel)]="detail.price" [value]="detail.price" type="number" class="ion-text-right"
                readonly></ion-input>
            </ion-item>
          </ion-list>
          <span class="d-block mx-5 my-2 h-1 bg-reverse"></span>
          <div class="my-2">
            <div *ngFor="let row of detail.fields; let fieldIndex = index;">
              <ion-item *ngIf="row.type == 'text' ">
                <ion-label position="floating">{{row.name}}</ion-label>
                <ion-input [(ngModel)]="row.value" [value]="row.value"></ion-input>
              </ion-item>
              <ion-item *ngIf="row.type == 'number' ">
                <ion-label position="floating">{{row.name}}</ion-label>
                <ion-input [(ngModel)]="row.value" [value]="row.value" type="number"></ion-input>
              </ion-item>
              <ion-item *ngIf="row.type == 'date' ">
                <ion-label position="floating">{{row.name}}</ion-label>
                <ion-input [(ngModel)]="row.value" [value]="row.value" type="date"></ion-input>
              </ion-item>
              <ion-item *ngIf="row.type == 'select' || row.type == 'select_edit'">
                <ion-item class="w-100 ion-no-padding" lines="none">
                  <ion-label position="floating">{{row.name}}</ion-label>
                  <ion-select *ngIf="!detailAnders[index+'_'+row.id]" [(ngModel)]="row.value" [value]="row.value">
                    <ion-select-option *ngFor="let option of row.select"
                      [value]="option.value">{{option.name}}</ion-select-option>
                  </ion-select>
                  <ion-input *ngIf="detailAnders[index+'_'+row.id]" [(ngModel)]="row.value" [value]="row.value"
                    type="text"></ion-input>
                </ion-item>
                <div *ngIf="row.type === 'select_edit'" class="ion-text-right">
                  <ion-button fill="clear" (click)="detailAnders[index+'_'+row.id] = !detailAnders[index+'_'+row.id];">
                    <ion-text class="d-flex ion-align-items-center ion-justify-content-center text-success"
                      *ngIf="!detailAnders[index+'_'+row.id]"><ion-icon name="menu-outline"></ion-icon></ion-text>
                    <ion-text class="d-flex ion-align-items-center ion-justify-content-center text-danger"
                      *ngIf="detailAnders[index+'_'+row.id]"><ion-icon name="menu-outline"></ion-icon></ion-text>
                  </ion-button>
                </div>
              </ion-item>
              <ion-item *ngIf="row.type == 'prefill' ">
                <div class="w-100">
                  <ion-label class="my-1">{{row.name}}</ion-label>
                  <div>
                    <textarea [froalaEditor] [(ngModel)]="row.value" [innerHtml]="row.value"></textarea>
                  </div>
                </div>
              </ion-item>
            </div>
          </div>
          <div class="my-2 px-2 ion-text-right">
            <div class="ion-text-left alert alert-warning" *ngIf="detail.messages.length">
              <span class="d-block" *ngFor="let message of detail.messages">{{message}}</span>
            </div>
            <ion-button fill="clear" color="success" class="shadow rounded" (click)="confirmDetail(index)">Bevestigen
              <ion-icon name="checkmark-outline"></ion-icon></ion-button>
          </div>
        </section>
      </ion-card>

      <!--    Detail buttons-->
      <div class="m-2" *ngIf="template.details.length">
        <div class="d-flex ion-justify-content-center ion-align-items-center">
          <a *ngFor="let detail of template.details" (click)="addDetail(detail.id)"
            class="btn btn-inverse-primary">{{detail.name}}</a>
        </div>
      </div>

      <!--    Keywords-->
      <section *ngIf="template && template.keywords">

        <div *ngFor="let list of pages.list">
          <div *ngIf="pages.index == list.title">
            <div *ngFor="let item of list.pages;">
              <div *ngIf="(!item.parent_keyword || item._hide === false) && item.data?.app !== false"
                [ngClass]="!excludeBorder[item.type] ? 'border-bottom' : ''" class="bg-white">
                <div>

                  <!--Text-->
                  <ion-item lines="none" *ngIf="item.type === 'text'">
                    <ion-label position="floating">#{{item._order}} {{item.name}}<span
                        *ngIf="item.required === '1'">*</span></ion-label>
                    <ion-input (ionChange)="inputChange(item)" [(ngModel)]="item.value" [value]="item.value"
                      type="text"></ion-input>
                  </ion-item>

                  <!--Textarea-->
                  <div *ngIf="item.type === 'textarea'" class="p-2">
                    <span class="font-size-1 text-black">#{{item._order}} {{item.name}}<span
                        *ngIf="item.required === '1'">*</span></span>
                    <textarea [froalaEditor] [(ngModel)]="item.value" [innerHtml]="item.value"></textarea>
                  </div>

                  <!--Number-->
                  <ion-item lines="none" *ngIf="item.type === 'number'">
                    <ion-label position="floating">#{{item._order}} {{item.name}}<span
                        *ngIf="item.required === '1'">*</span></ion-label>
                    <ion-input (ionChange)="inputChange(item)" [(ngModel)]="item.value" [value]="item.value"
                      type="number"></ion-input>
                  </ion-item>

                  <!--Date-->
                  <ion-item lines="none" *ngIf="item.type === 'date'">
                    <ion-label>#{{item._order}} {{item.name}}<span *ngIf="item.required === '1'">*</span></ion-label>
                    <div class="px-2 py-1 hover-shadow rounded" slot="end" (click)="dateSelect.select(item, 'value')">
                      {{item.value ? convertDate(item.value).date : 'Datum selecteren'}}</div>
                  </ion-item>

                  <!--Time-->
                  <ion-item lines="none" *ngIf="item.type === 'time'">
                    <ion-label>#{{item._order}} {{item.name}}<span *ngIf="item.required === '1'">*</span></ion-label>
                    <div class="px-2 py-1 hover-shadow rounded" slot="end"
                      (click)="timeSelect.select(item, 'value', 1)">{{item.value || 'Tijd selecteren'}}</div>
                  </ion-item>

                  <!--Timestamp button-->
                  <ion-item lines="none" *ngIf="item.type === 'timestamp_button'">
                    <ion-label>#{{item._order}} {{item.name}}<span *ngIf="item.required === '1'">*</span></ion-label>
                    <ion-button fill="clear" color="primary" disabled="{{item.timelock ? 'true' : 'false'}}"
                      (click)="item.value = timestamp()">{{item.value ? convertDate(item.value).time : 'Tijd
                      vastleggen'}}</ion-button>
                  </ion-item>

                  <!--Select ( edit )-->
                  <div *ngIf="item.type === 'select' || item.type === 'select_edit'" class="flex-between">
                    <ion-item lines="none" class="w-100">
                      <ion-label position="floating">#{{item._order}} {{item.name}}<span
                          *ngIf="item.required === '1'">*</span></ion-label>

                      <ion-select (ionChange)="inputChange(item)" *ngIf="!anders[item.id]" [(ngModel)]="item.value">
                        <ion-select-option *ngFor="let option of item.data.options"
                          value="{{option.value}}">{{option.name}}</ion-select-option>
                      </ion-select>
                      <ion-input (ionChange)="inputChange(item)" *ngIf="anders[item.id]" [(ngModel)]="item.value"
                        [value]="item.value" type="text"></ion-input>
                    </ion-item>

                    <ion-button *ngIf="item.type === 'select_edit'" fill="clear" color="secondary"
                      (click)="anders[item.id] = !anders[item.id];">
                      <ion-text class="d-flex ion-align-items-center ion-justify-content-center text-success"
                        [ngClass]="anders[item.id] ? 'text-success' : 'text-danger'"><ion-icon
                          name="menu-outline"></ion-icon></ion-text>
                    </ion-button>
                  </div>

                  <!--User select-->
                  <ion-item lines="none" *ngIf="item.type === 'user_select'">
                    <ion-label position="floating">#{{item._order}} {{item.name}}<span
                        *ngIf="item.required === '1'">*</span></ion-label>
                    <ion-select (ionChange)="inputChange(item)" [(ngModel)]="item.value">
                      <ion-select-option *ngFor="let user of user.users" value="{{user.id}}">{{user.name}}
                        {{user.lastname}}</ion-select-option>
                    </ion-select>
                  </ion-item>

                  <!--Image-->
                  <ion-item *ngIf="item.type == 'image'" class="overflow-auto">
                    <div>
                      <ion-label>#{{item._order}} {{item.name}}<span *ngIf="item.required === '1'">*</span></ion-label>

                      <div *ngIf="!item.timelock" class="flex-align overflow-auto my-2">
                        <div *ngFor="let file of item.value" class="mx-1 text-center">
                          <div (click)="removeKeywordImage(item, file.id)"
                            class="bg-img-center rounded-5 w-px-75 h-px-75 min-w-75 mx-1 position-relative"
                            [ngStyle]="{background: 'url(https://'+subdomain+'.ikbentessa.nl/api/file/explorer/files/'+file.src+')', backgroundSize: 'cover', backgroundPosition: 'center'}">
                            <div class="position-absolute top-0 end-0 m-1 font-size-15 text-danger"><ion-icon
                                name="close-outline"></ion-icon></div>
                          </div>
                          <div *ngIf="item.data?.canEdit">
                            <button (click)="editImage(item, file.id)"
                              class="btn btn-sm btn-inverse-primary rounded-pill px-2 py-0 mt-1">Bewerken</button>
                          </div>
                        </div>

                        <div *ngIf="!item.data?.multiple || item.data?.multiple"
                          class="border-2 border-dashed cursor-pointer flex-center font-size-15 hover-mark rounded-5 text-muted w-px-75 h-px-75 min-w-75 mx-1"
                          (click)="selectKeywordImage(item)">
                          <ion-icon
                            [name]="(!item.data?.multiple && item.value.length !== 0) ? 'refresh-outline' : 'add-outline'"></ion-icon>
                        </div>

                      </div>

                    </div>
                  </ion-item>

                  <!--Dataset-->
                  <div *ngIf="item.type === 'dataset'">
                    <ion-item lines="none">
                      <ion-label position="floating">#{{item._order}} {{datasets[item.data] ? item.name : 'Onjuiste
                        dataset ID'}}</ion-label>
                      <ion-select (ionChange)="inputChange(item)" *ngIf="datasets[item.data]" [(ngModel)]="item.value">
                        <ion-select-option *ngFor="let row of datasets[item.data].items"
                          value="{{row.value}}">{{row.name}}</ion-select-option>
                      </ion-select>
                    </ion-item>
                    <ion-list *ngIf="item.value">
                      <ion-item *ngFor="let row of jsonDatasetItems[item.value]" lines="none">
                        <ion-label>
                          <h4 class="text-muted">{{row.index}}</h4>
                          <h2>{{row.value}}</h2>
                        </ion-label>
                      </ion-item>
                    </ion-list>
                  </div>

                  <!--Card break-->
                  <div class="py-2 bg-light-grey" *ngIf="item.type == 'card_break'"></div>

                  <!--Header-->
                  <div class="p-2" *ngIf="item.type == 'header'">
                    <span
                      style="font-size: {{item.data.size}}; color: {{item.data.color}}; font-weight: {{item.data.weight}}">{{item.name}}</span>
                  </div>

                  <!--Custom row-->
                  <div *ngIf="item.type == 'custom_row'">
                    <div *ngFor="let row of item.value; let crIndex = index;" class="pt-4" >
                      <div *ngFor="let value of row">
                        <div *ngIf="value.app !== false" >

                          <div class="flex-between p-2 text-dark font-size-1" *ngIf="value.type == 'header'">
                            <span>{{value.name}}:</span>
                            <span>{{value.value}}</span>
                          </div>

                          <ion-item *ngIf="value.type == 'label'" lines="none" class="text-muted" >{{value.value}}</ion-item>

                          <ion-item *ngIf="value.type == 'text'">
                            <ion-label position="floating" >{{value.name}}</ion-label>
                            <ion-input (ionChange)="inputChange(item)" [(ngModel)]="value.value"></ion-input>
                          </ion-item>

                          <ion-item *ngIf="value.type == 'checkbox'">
                            <ion-label>{{value.name}}</ion-label>
                            <ion-checkbox (ionChange)="inputChange(item)" class="ion-text-right"
                                          [(ngModel)]="value.value"></ion-checkbox>
                          </ion-item>

                          <ion-item *ngIf="value.type == 'select'">
                            <ion-label position="floating" >{{value.name}}</ion-label>
                            <ion-select (ionChange)="inputChange(item)" [(ngModel)]="value.value">
                              <ion-select-option *ngFor="let option of value.data.options"
                                                 [value]="option.value">{{option.name}}</ion-select-option>
                            </ion-select>
                          </ion-item>

                          <ion-item *ngIf="value.type == 'date'">
                            <ion-label>{{value.name}}</ion-label>
                            <div class="px-2 py-1 hover-shadow rounded" slot="end"
                                 (click)="dateSelect.select(value, 'value')">{{value.value ? convertDate(value.value).date :
                              'Datum selecteren'}}</div>
                          </ion-item>

                        </div>
                      </div>
                      <div class="ion-text-center py-2" *ngIf="item.data.multiple" >
                        <a class="btn btn-inverse-danger" (click)="removeCustomRow(item.id, crIndex)"><ion-icon
                            name="close-outline"></ion-icon></a>
                      </div>
                    </div>
                    <div class="ion-text-center py-2" *ngIf="item.data.multiple">
                      <a class="btn btn-inverse-primary" (click)="addCustomRow(item.id)"><ion-icon
                          name="add-outline"></ion-icon></a>
                    </div>
                  </div>

                  <!--Count attributes-->
                  <div class="p-2 text-black font-size-1" *ngIf="item.type == 'count_data_attribute'">
                    <span>{{item.name}}</span>
                    <span class="mx-1">{{item.value || 0}}</span>
                  </div>

                  <!--Non expected values-->
                  <div class="px-2 py-1 text-black font-size-1" *ngIf="item.type == 'non_expected_values'">
                    <div *ngFor="let row of nonExpectedValuesArray" class="pb-1">
                      <span>#{{row._order}} {{row.name}}</span>
                      <div *ngFor="let attr of (item.data.additional_data ? item.data.additional_data : [])">
                        <span *ngIf="row.data && row.data[attr.attribute]" class="ml-1">- {{attr.name}}:
                          {{row.data[attr.attribute]}}</span>
                      </div>
                    </div>
                  </div>

                  <!--Signature-->
                  <div *ngIf="item.type == 'signature'">
                    <div class="p-2 flex-between">
                      <span class="font-size-1 text-black">#{{item._order}} {{item.name}}<span
                          *ngIf="item.required === '1'">*</span></span>
                      <a class="btn text-danger" (click)="clearCanvas(item)"><ion-icon
                          name="refresh-outline"></ion-icon></a>
                    </div>
                    <div class="bg-inverse-secondary ion-text-center">
                      <canvas id="signature-item-{{item.keyword}}" class="mt-1 bg-white border rounded"
                        style="background-color: #00000005;" [height]="canvas.height" [width]="canvas.width"
                        (mousedown)="startDrawing($event)" (touchstart)="startDrawing($event)"
                        (touchmove)="moved($event)" (mousemove)="moved($event)" (mouseup)="endDrawing(item)"
                        (touchend)="endDrawing(item)"></canvas>
                    </div>
                  </div>

                  <!--File & opmerking ( on value )-->
                  <div *ngIf="item.file === '1'">
                    <div *ngIf="item.src" class="p-2">
                      <div class="ion-text-right">
                        <ion-button (click)="item.src = null" fill="clear" class="text m-0"><ion-icon
                            name="refresh-outline"></ion-icon></ion-button>
                      </div>
                      <div class="ion-text-center border rounded bg-inverse-secondary">
                        <img style="max-height: 45vh" class="rounded shadow"
                          src="https://{{subdomain}}.ikbentessa.nl/api/file/{{item.src}}">
                      </div>
                    </div>
                    <div
                      *ngIf="item.file_on_value && item.value === item.file_on_value && !item.src || item.file_show && !item.src"
                      class="p-2">
                      <div class="btn-inverse-primary ion-text-center p-2 rounded shadow" (click)="selectImage(item)">
                        <h4 class="my-1">Selecteer afbeelding</h4>
                        <small>{{item.name}}</small>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="item.opmerking === '1'">
                    <div
                      *ngIf="(item.opmerking_on_value && item.value === item.opmerking_on_value) || item.opmerking_show">
                      <ion-item lines="none">
                        <ion-label position="floating">Opmerking</ion-label>
                        <ion-input [(ngModel)]="item.opmerking_value"></ion-input>
                      </ion-item>
                    </div>
                  </div>



                </div>
                <div class="ion-text-center"
                  *ngIf="item.opmerking === '1' && !item.opmerking_on_value || item.file === '1' && !item.file_on_value">
                  <ion-button fill="clear" color="primary" (click)="modalAddons(item)"><ion-icon
                      name="add-outline"></ion-icon></ion-button>
                </div>
              </div>
            </div>
          </div>
        </div>

      </section>

      <!-- Files -->

      <div class="bg-white p-2 my-2">
        <h6>Bestanden</h6>
        <div class="flex-align overflow-auto my-2">
          <div *ngFor="let file of files; let i = index" class="mx-1 text-center">

            <div (click)="deleteFile(i)"
              class="bg-img-center rounded-5 w-px-75 h-px-75 min-w-75 mx-1 position-relative"
              [ngStyle]="{
                background: 'url(https://'+subdomain+'.ikbentessa.nl/api/file/'+file.src+')',
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }">
              <div class="position-absolute top-0 end-0 m-1 font-size-15 text-danger">
                <ion-icon name="close-outline"></ion-icon>
              </div>
            </div>

            <div *ngIf="file.uploaded !== null">
              <button (click)="editFile(i)"
                class="btn btn-sm btn-inverse-primary rounded-pill px-2 py-0 mt-1">Bewerken</button>
            </div>
          </div>

          <div class="border-2 border-dashed cursor-pointer flex-center font-size-15 hover-mark rounded-5 text-muted w-px-75 h-px-75 min-w-75 mx-1"
            (click)="addFile()">
            <ion-icon name="add-outline"></ion-icon>
          </div>
        </div>
      </div>

      <div class="flex-between mx-1 text-dark font-size-1">
        <div class="d-flex overflow-auto py-3" [ngClass]="settings.silent_save_loader ? 'opacity-25' : ''" id="bot-nav">
          <div *ngFor="let list of pages.list" disabled="" (click)="navSelect(list.title);" id="top-nav-{{list.title}}"
            style="border-radius: 1rem"
            class="bg-white px-2 py-1 my-1 mx-2 shadow nobr {{pages.index == list.title ? '' : 'opacity-50'}}">
            <div class="ion-text-center">{{list.title}}</div>
            <div class="font-size-075 text-muted ion-text-center px-1">#{{list.lowest_order}} <span
                style="margin: 0 .15rem">/</span> #{{list.highest_order}}</div>
          </div>
        </div>
      </div>

      <div class="ion-text-center my-3">
        <div *ngIf="button">
          <a class="btn btn-inverse-primary mx-1" *ngIf="local.index === null" (click)="saveLocalModal()">Lokaal
            opslaan</a>
          <a class="btn btn-inverse-primary mx-1" *ngIf="local.index !== null" (click)="saveLocal()">Lokale versie
            opslaan</a>
          <a class="btn btn-inverse-success mx-1" (click)="valueIfNullCheck()">Opslaan</a>
        </div>
        <ion-spinner color="success" *ngIf="!button"></ion-spinner>
      </div>
    </div>

  </ion-card>
</ion-content>

<!--Klant select-->
<div class="modal-container" *ngIf="modals.klant" (click)="modals.klant = false;">
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
    <ion-item>
      <ion-label position="floating">Zoeken</ion-label>
      <ion-input [(ngModel)]="modals.search" class="ion-text-left"></ion-input>
    </ion-item>
    <div class="mh-25-vh overflow-auto">
      <div *ngFor="let klant of klanten;">
        <div class="ion-text-center"
          *ngIf="modals.search && klant.titel.toLowerCase().includes(modals.search.toLowerCase())">
          <ion-button (click)="selectKlant(klant.id)" class="text" fill="clear"><ion-text
              class="text">{{klant.titel}}</ion-text></ion-button>
        </div>
      </div>
    </div>
  </ion-card>
</div>

<!--Locatie select-->
<div class="modal-container" *ngIf="modals.locatie && template.klant_locatie != 0" (click)="modals.locatie = false;">
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
    <ion-item>
      <ion-label position="floating">Zoeken</ion-label>
      <ion-input [(ngModel)]="modals.locatieSearch" class="ion-text-left"></ion-input>
    </ion-item>
    <div class="mh-25-vh overflow-auto">
      <div *ngIf="this?.selectedKlant?.Bezoekadres">
        <div class="ion-text-center"
          *ngIf="modals.locatieSearch && this?.selectedKlant?.Bezoekadres?.toLowerCase()?.includes(modals.locatieSearch?.toLowerCase())">
          <ion-button (click)="selectLocatie('Bezoekadres')" class="text" fill="clear">
            <ion-text class="text">{{this?.selectedKlant?.Bezoekadres}}</ion-text>
          </ion-button>
        </div>
      </div>
      <div *ngIf="this?.selectedKlant?.postadres">
        <div class="ion-text-center"
          *ngIf="modals.locatieSearch && this?.selectedKlant?.postadres?.toLowerCase()?.includes(modals.locatieSearch?.toLowerCase())">
          <ion-button (click)="selectLocatie('postadres')" class="text" fill="clear">
            <ion-text class="text">{{this?.selectedKlant?.postadres}}</ion-text>
          </ion-button>
        </div>
      </div>
      <div *ngFor="let locatie of this?.selectedKlant?.locaties;">
        <div class="ion-text-center"
          *ngIf="modals.locatieSearch && locatie?.adres?.toLowerCase()?.includes(modals.locatieSearch?.toLowerCase())">
          <ion-button (click)="selectLocatie(locatie.id)" class="text" fill="clear">
            <ion-text class="text">{{locatie?.adres}}</ion-text>
          </ion-button>
        </div>
      </div>
      <div class="ion-text-center"
        *ngIf="!this?.selectedKlant?.locaties && !this?.selectedKlant?.Bezoekadres && !this?.selectedKlant?.postadres">
        <ion-button class="text" fill="clear">
          <ion-text class="text">Geen locaties</ion-text>
        </ion-button>
      </div>
    </div>
  </ion-card>
</div>

<!--project select-->
<div class="modal-container" *ngIf="modals.project && template.project != 0" (click)="modals.project = false;">

  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()">
    <ion-item>
      <ion-label position="floating">Zoeken</ion-label>
      <ion-input [(ngModel)]="modals.projectSearch" class="ion-text-left"></ion-input>
    </ion-item>
    <div class="mh-25-vh overflow-auto">
      <div *ngFor="let project of projecten;">
        <div class="ion-text-center"
          *ngIf="modals.search && project?.projectnaam && project.projectnaam.toLowerCase().includes(modals.projectSearch.toLowerCase())">
          <ion-button (click)="selectProject(project.id)" class="text" fill="clear"><ion-text
              class="text">{{project.projectnaam}}</ion-text></ion-button>
        </div>
      </div>
    </div>
  </ion-card>
</div>

<!--Local save-->
<div class="modal-container" *ngIf="modals.local" (click)="modals.local = false;">
  <ion-card (click)="$event.stopPropagation()" class="w-100 bg-white">
    <div class="p-2 flex-between text-dark font-size-1">
      <ion-text>{{template.name}}</ion-text>
      <ion-text>{{today.date}}</ion-text>
    </div>
    <ion-item>
      <ion-label position="floating">Naam</ion-label>
      <ion-input [(ngModel)]="modals.name"></ion-input>
    </ion-item>
    <div class="ion-text-center my-2 mx--1">
      <a class="btn btn-inverse-danger mx-1" *ngIf="local.back"
        (click)="modals.local = false; !forcedProject ? callPage('checklists') : callPage('checklists/project')">Niet
        opslaan</a>
      <a class="btn btn-inverse-success mx-1" (click)="saveLocal()">Lokaal opslaan</a>
    </div>
  </ion-card>
</div>

<!--Values if null-->
<div class="modal-container" *ngIf="modals.value_if_null" (click)="modals.value_if_null = false;">
  <ion-card (click)="$event.stopPropagation()" class="w-100 p-2 bg-white">

    <div class="text-dark font-size-1">
      <ion-text>Volgende vragen zijn niet beantwoord:</ion-text>
    </div>

    <div class="text-dark overflow-auto my-3 font-size-075" style="max-height: 40vh">
      <div class="py-1">
        <table class="w-100">
          <thead>
            <tr>
              <th class="ion-text-left p-1">Vraag</th>
              <th class="ion-text-left p-1">Standaard antwoord</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of template.keywords">
              <td class="p-1 border"
                *ngIf="(!item.parent_keyword || item._hide === false) && (!item.value && item.value_if_null)">
                #{{item._order}} {{item.name}}</td>
              <td class="p-1 border"
                *ngIf="(!item.parent_keyword || item._hide === false) && (!item.value && item.value_if_null)">
                {{item.value_if_null}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="flex-between text-dark font-size-1">
      <ion-text>Wilt u standaard antwoorden gebruiken:</ion-text>
      <div class="mx--1 nobr">
        <a class="btn btn-inverse-danger mx-1" (click)="valueIfNull(false)">Nee</a>
        <a class="btn btn-inverse-success mx-1" (click)="valueIfNull(true)">Ja</a>
      </div>
    </div>
  </ion-card>
</div>

<!--Addons-->
<div class="modal-container" *ngIf="modals.addons" (click)="modals.addons = false;">
  <ion-card class="modal-select-bottom p-2" (click)="$event.stopPropagation()">
    <div class="text-dark my-2">
      {{modals.item.name}}
    </div>
    <div class="my-2 mx--1 flex-between">
      <a *ngIf="modals.item.opmerking === '1'" [ngClass]="modals.item.opmerking_show ? 'btn-dark' : 'btn-secondary'"
        (click)="modals.item.opmerking_show = !modals.item.opmerking_show; modals.addons = false;"
        class="btn w-100 mx-1">Opmerking</a>
      <a *ngIf="modals.item.file === '1'" [ngClass]="modals.item.file_show ? 'btn-dark' : 'btn-secondary'"
        (click)="modals.item.file_show = !modals.item.file_show; modals.addons = false;" class="btn w-100 mx-1">Foto</a>
    </div>
  </ion-card>
</div>

<!--Date-->
<div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;">
  <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()">

    <div class="flex-between my-1 mx--2">
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)"><ion-icon
          name="chevron-back-outline"></ion-icon></ion-button>
      <span class="mx-2">{{dateSelect.vars.date.year}}</span>
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)"><ion-icon
          name="chevron-forward-outline"></ion-icon></ion-button>
    </div>

    <div class="flex-between my-1 mx--2">
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)"><ion-icon
          name="chevron-back-outline"></ion-icon></ion-button>
      <span class="mx-2">{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
      <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)"><ion-icon
          name="chevron-forward-outline"></ion-icon></ion-button>
    </div>

    <div class="flex-between custom-date-picker-days">
      <div>ma</div>
      <div>di</div>
      <div>wo</div>
      <div>do</div>
      <div>vr</div>
      <div>za</div>
      <div>zo</div>
    </div>
    <div class="custom-date-picker">
      <div *ngFor="let day of dateSelect.vars.completeDays" [ngClass]="{
          'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
          'prev-month': day.type === 'prev',
          'current-month': day.type === 'current',
          'next-month': day.type === 'next'
        }" (click)="dateSelect.confirmDate(day)">
        {{ day.day }}
      </div>
    </div>

  </ion-card>
</div>

<!--Time-->
<div class="modal-container" *ngIf="timeSelect.modal" (click)="timeSelect.modal = false;">
  <ion-card class="p-2 text-dark" style="width: 90vw; max-width: 750px;" (click)="$event.stopPropagation()">
    <div class="d-flex ion-justify-content-around font-size-125">

      <div class="flex-between w-100">
        <div class="bg-secondary h-1 w-100 px-2"></div>
      </div>

      <!--      Hours-->
      <div (scroll)="timeSelect.scrollTime('hour')" style="max-height: 150px;" class="overflow-auto w-100"
        id="custom-time-hour">
        <div style="height: 39px"></div>
        <div class="ion-text-center my-4" id="custom-time-hour-{{h}}" *ngFor="let h of timeSelect.vars.hours"
          (click)="timeSelect.selectTimeValue('hour', h)">
          <span class="{{timeSelect.vars.time.hour == h ? 'text-primary' : ''}}">{{('0'+h).slice(-2)}}</span>
        </div>
        <div style="height: 39px"></div>
      </div>

      <div class="flex-between w-50">
        <div class="bg-secondary h-1 w-100 px-2"></div>
      </div>

      <!--      Minutes-->
      <div (scroll)="timeSelect.scrollTime('min')" style="max-height: 150px;" class="overflow-auto w-100"
        id="custom-time-min">
        <div style="height: 39px"></div>
        <div class="ion-text-center my-4" id="custom-time-min-{{m}}" *ngFor="let m of timeSelect.vars.minutes"
          (click)="timeSelect.selectTimeValue('min', m)">
          <span class="{{timeSelect.vars.time.min == m ? 'text-primary' : ''}}">{{('0'+m).slice(-2)}}</span>
        </div>
        <div style="height: 39px"></div>
      </div>

      <div class="flex-between w-100">
        <div class="bg-secondary h-1 w-100 px-2"></div>
      </div>

    </div>
    <div class="mt-1 pt-1 ion-text-right border-top">
      <ion-button fill="clear" (click)="timeSelect.confirmTime()">OK</ion-button>
    </div>
  </ion-card>
</div>

<!--Explorer-->
<div class="modal-container" *ngIf="modals.explorer" (click)="modals.explorer = false;" >
  <ion-card class="modal-select-bottom" (click)="$event.stopPropagation()" >
    <ion-list class="overflow-auto" style="max-height: 70vh;" >
      <ion-item (click)="modals.explorer = false; selectKeywordImage(explorerFiles['item'], true)">
        <ion-icon name="camera-outline" slot="start"></ion-icon>
        <ion-label>Foto maken of kiezen uit galerij</ion-label>
      </ion-item>
      <ion-item *ngFor="let file of explorerFiles['files']" (click)="selectExplorerImage(explorerFiles['item'], file)">
        <div class="bg-img-center rounded-5 w-px-75 h-px-75 min-w-75 mx-1 position-relative"
          [ngStyle]="{background: 'url(https://'+subdomain+'.ikbentessa.nl/api/file/explorer/files/'+file.src+')', backgroundSize: 'cover', backgroundPosition: 'center'}">
        </div>
        <ion-label>{{file.name}}</ion-label>
      </ion-item>
    </ion-list>
  </ion-card>
</div>

<div class="simple-alert {{settings.alert_state ? 'active' : ''}}">
  <span>{{settings.alert}}</span>
</div>
