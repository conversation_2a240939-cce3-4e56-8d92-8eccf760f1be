import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ActionSheetController, LoadingController, Platform, ToastController, ModalController } from '@ionic/angular';
import { dateSelect, timeSelect, Infordb } from 'infordb';
import { ClientStorageService } from "../../services/ClientStorage/client-storage.service";
import { CanvasPage } from '../../draw/canvas/canvas.page';

@Component({
  selector: 'app-new',
  templateUrl: './new.page.html',
  styleUrls: ['./new.page.scss'],
  providers: [Infordb]

})
export class NewPage implements OnInit {

  public dateSelect = dateSelect
  public timeSelect = timeSelect

  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public modules = {};
  public projecten = [];
  public post = false;
  public button = true;
  public edit = false;
  public copy = false;
  public prefill = false;
  public today = this.convertDate(new Date());
  public local = {
    back: false,
    index: null,
    data: [],
  }

  public checklist;
  public template;
  public klanten;
  public aanvragen;
  public datasets;
  public datasetItems;
  public jsonDatasetItems = {};
  public anders = {};
  public detailAnders = {};

  public detailTemplates = {};
  public details = [];

  public selectedKlant;
  public selectedLocatie;
  public selectedProject;
  public forcedProject;
  public selectedAanvragen = {};
  public explorerFiles = [];

  public modals = {
    item: null,
    keyword: null,
    search: '',
    locatieSearch: '',
    projectSearch: '',
    name: '',
    index: 0,
    klant: false,
    locatie: false,
    project: false,
    local: false,
    value_if_null: false,
    addons: false,
    explorer: false,
  }
  public canvas = {
    drawing: false,
    width: (this.plt.width() - 10),
    height: (this.plt.width() - 10) * 0.35,
    saveX: 0,
    saveY: 0,
    lineWidth: 5,
  }
  public excludeBorder = {
    header: true,
    card_break: true,
    signature: true,
  }
  public pages = {
    index: 'Start',
    list: [],
  }

  public settings = {
    page_loaded: false,
    pause_listener: null,
    resume_listener: null,
    silent_save: false,
    silent_save_loader: false,
    alert: null,
    alert_state: false,
  }

  public files = [];
  public nonExpectedValuesArray = [];

  constructor(
    private router: Router,
    private infordb: Infordb,
    private plt: Platform,

    // camera vars
    private actionSheetController: ActionSheetController,
    private toastController: ToastController,
    private loadingController: LoadingController,
    private clientStorage: ClientStorageService,
    private modalCtrl: ModalController,
  ) {
    this.localSubscribe();
  }

  ngOnInit() {
    if (this.canvas.width > 650) {
      this.canvas.width = 650;
      this.canvas.height = 228;
    }
  }

  ionViewWillLeave() {
    this.localUnsubscribe()
  }
  ionViewWillEnter() {
    this.settings.page_loaded = false;
    this.settings.silent_save = false;
    this.settings.silent_save_loader = true;

    for (const module of this.user.modules) {
      this.modules[module.module.name] = true;
    }

    const templateId = localStorage.getItem('checklistTemplateId');
    // localStorage.removeItem('checklistTemplateId');

    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/new`, {
      template: templateId.toString(),
    })
      .then(response => {
        if (response.status === 201) {
          const data = response.data;
          this.template = data.template;
          this.klanten = data.klanten;
          this.projecten = data.projecten;

          this.aanvragen = data.aanvragen;
          this.datasets = data.datasets;
          this.datasetItems = data.datasetItems;

          this.postLoops();
          this.aanvragenInit();
          this.editInit();
          this.copyInit();
          this.klantInit()
          this.localInit();
          this.forcedProjectInit();
          this.pagesInit();
          this.timelockInit();

          this.post = true;

          this.redrawSignatures();
          this.settings.silent_save_loader = false;
        }
      })
      .catch(err => {
        this.infordb.handleError(err);
        this.settings.silent_save_loader = false;
      });
  }

  silentSubmit() {
    this.settings.silent_save = true;
    this.onSubmit();
  }
  onSubmit() {
    this.button = false;

    const anv = [];

    this.pagesToKeywords();

    if (!this.selectedKlant) {
      if (!confirm('Klant is niet geselecteerd, wilt u evengoed doorgaan?')) {
        this.button = true;
        return false;
      }
    }
    if (!this.settings.silent_save) {
      for (const item of this.template.keywords) {
        if(item._parent_verified === false){ continue; }

        let value_set = !!item.value;
        let value_message = `#${item._order} ${item.name} is verplicht!`

        if (item.type == 'image') {
          value_set = !!item.value.length
        }
        else if(item.type == 'custom_row'){
          custom_rows: for(const r in item.value){
            const row = item.value[r];
            for(const column of row){
              if(!column.required || column.value){ continue; }

              value_message = `${item.name}: ${column.name} is verplicht!`
              value_set = false;
              break custom_rows;
            }
          }
        }

        if (item.required == 1 && !value_set) {
          this.showAlert(value_message)
          this.button = true;
          return false;
        }
      }
    }
    for (const i in this.files) {
      const file = this.files[i];
      if (!file.name) {
        this.files.splice(Number(i), 1);
        continue;
      }
      if (!file.src && !confirm(`Bestand ${file.name} is nog niet geüpload, wilt u evengoed doorgaan?`)) {
        this.button = true;
        return false;
      }
    }
    for (const a in this.selectedAanvragen) {
      if (this.selectedAanvragen[a]) {
        anv.push(a);
      }
    }
    for (const detail of this.details) {
      if (!detail.confirmed) {
        alert('Bevestig alle details!');
        this.button = true;
        return false;
      }
    }
    if (this.settings.silent_save) {
      this.settings.silent_save_loader = true;
    }
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/store`, {
      edit: this.edit,
      checklist: this.checklist && !this.copy && !this.prefill ? this.checklist.id : null,
      user: this.user.user_id,
      klant: this.selectedKlant ? this.selectedKlant.id : null,
      klant_locatie_id: this.selectedLocatie ? `${this.selectedLocatie.id}` : null,
      project: this.forcedProject ?? (this.selectedProject ? this.selectedProject.id : null),
      template: this.template.id,
      items: JSON.stringify(this.template.keywords),
      aanvragen: JSON.stringify(anv),
      details: JSON.stringify(this.details),
      bestanden: JSON.stringify(this.files),
      silent_save: this.settings.silent_save ? 1 : 0,
    })
      .then(response => {
        this.button = true;
        this.copy = false;
        if (response.status === 201) {
          const { checklist } = response.data;

          if (this.local.index !== null) {
            this.local.data.splice(this.local.index, 1)
            this.local.index = null;
            this.clientStorage.set('checklists_local_data', this.local.data);
          }

          if (!this.settings.silent_save) {
            if (this.checklist) {
              this.showAlert(`Checklist ${this.checklist.checklistnummer} gewijzigd!`)
            }
            else {
              this.showAlert(`Checklist ${checklist.checklistnummer} opgeslagen!`)
            }
            if (this.forcedProject) {
              this.callPage('checklists/project');
              return;
            }
            this.callPage('checklists');
            return;
          }

          this.checklist = checklist;
          this.showAlert(`Checklist ${checklist.checklistnummer} opgeslagen!`)

          this.settings.silent_save = false;
          this.settings.silent_save_loader = false;
        }
      })
      .catch(err => {
        this.settings.silent_save = false;
        this.settings.silent_save_loader = false;
        this.button = true;
        this.infordb.handleError(err);
      });
  }

  valueIfNullCheck() {
    for (const item of this.template.keywords) {
      if (!item.value && item.value_if_null) {
        this.button = true;
        this.modals.value_if_null = true;
        return false;
      }
    }
    this.onSubmit();
  }
  valueIfNull(state) {
    this.modals.value_if_null = false;

    if (!state) {
      this.onSubmit();
      return;
    }

    for (const item of this.template.keywords) {
      if (!item.value && item.value_if_null) { item.value = item.value_if_null; }
    }
    this.onSubmit();
  }

  countAttributes() {
    for (const item of this.template.keywords) {
      if (item.type != 'count_data_attribute') { continue; }

      const attr = item.data.attribute;
      let count = 0;

      for (const row of this.template.keywords) {
        if (!row.data || !row.data.expected_value || !row.data[attr]) { continue; }
        if (row.data[attr] != item.data.attribute_value) { continue; }
        if (!row.value) { continue; }
        if (this.nonExpectedValue(row)) {
          count++;
        }
      }
      item.value = count;
    }

  }
  verifyParent(parent) {
    if(!parent.keyword){ return; }

    for (const item of this.template.keywords) {
      if (item.parent_keyword != parent.keyword) { continue; }

      item._hide = false;
      item._parent_verified = true;

      if (!item.parent_value && !parent.value) {
        item._hide = true;
        item._parent_verified = false;
      }
      if (item.parent_value && item.parent_value != parent.value) {
        item._hide = true;
        item._parent_verified = false;
      }
    }
    return false;
  }
  copyKeywordValue(item) {
    for (const row of this.template.keywords) {
      if (!row.copy_keyword_value || row.copy_keyword_value != item.keyword) { continue; }
      row.value = item.value;
    }
  }
  nonExpectedValue(item) {
    if (!item.value || !item.data || !item.data.expected_value) { return false; }

    for (const v of item.data.expected_value) {
      if (v == item.value) { return false; }
    }

    return true;
  }
  nonExpectedValues() {
    this.nonExpectedValuesArray = [];

    for (const item of this.template.keywords) {
      if (this.nonExpectedValue(item)) { this.nonExpectedValuesArray.push(item); }
    }
  }
  inputChange(item) {
    if (!this.settings.page_loaded) { return; }

    this.verifyParent(item);
    this.copyKeywordValue(item);
    this.countAttributes()
    this.nonExpectedValues()
  }
  showAlert(text, time = 2) {
    this.settings.alert = text;
    this.settings.alert_state = true;
    setTimeout(() => {
      this.settings.alert_state = false;
    }, time * 1000);
  }

  aanvragenInit() {
    if (!localStorage.checklistAanvraagId) { return; }

    let aanvraag;
    const id = localStorage.checklistAanvraagId;

    for (const klantId in this.aanvragen) {
      aanvraag = this.aanvragen[klantId].find(anv => anv.id == id);
      if (aanvraag) { break; }
    }
    this.selectedKlant = this.klanten.find(klant => klant.id == aanvraag.klant_id);

    this.selectedAanvragen[aanvraag.id] = true;
    localStorage.removeItem('checklistAanvraagId');
  }
  editInit() {
    if (!localStorage.checklistEditId) { return; }

    this.edit = true;
    const id = localStorage.getItem('checklistEditId');

    localStorage.removeItem('checklistEditId');

    this.fetchchecklist(id);
  }
  copyInit() {
    if (!localStorage.checklistCopyId) { return; }

    this.copy = true;
    const id = localStorage.getItem('checklistCopyId');

    localStorage.removeItem('checklistCopyId');

    this.fetchchecklist(id);
  }
  fetchchecklist(id) {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/edit`, {
      checklist: id.toString(),
    })
      .then(response => {
        this.post = true;
        if (response.status === 201) {
          this.checklist = response.data.checklist;
          this.fillDetails();
          this.fillValues();
          this.fillProject();
          this.fillKlant();
          this.fillLocatie();
          this.fillAanvragen();
          this.fillChange();
          this.redrawSignatures();
        }
      })
      .catch(err => {
        this.post = true;
        this.infordb.handleError(err);
      });
  }
  klantInit() {
    if (!localStorage.checklistKlantId) { return; }

    this.selectKlant(localStorage.checklistKlantId);
    localStorage.removeItem('checklistKlantId');
  }

  pagesInit() {
    this.pages.index = 'Start';
    this.pages.list = [];

    let index = this.pages.index;
    let pages = {};

    for (const i in this.template.keywords) {
      const item = this.template.keywords[i];
      if (item.data && item.data.paginate) {
        index = item.name
      }
      if (!pages[index]) { pages[index] = [] }
      pages[index].push(item);
    }

    for (const i in pages) {

      let lowest, highest;
      for (const item of pages[i]) {
        if (!lowest || item._order < lowest) { lowest = item._order; }
        if (!highest || item._order > highest) { highest = item._order; }
      }

      this.pages.list.push({
        title: i,
        lowest_order: lowest,
        highest_order: highest,
        pages: pages[i],
      })
    }
  }
  pagesToKeywords() {
    for (const list of this.pages.list) {
      for (const item of list.pages) {
        const keyword = this.template.keywords.find(row => row.id == item.id);
        if (keyword) { keyword.value = item.value; }
      }
    }
  }

  timelockInit() {
    for (const item of this.template.keywords) {
      if (item.timelock_parent_keyword) {
        const parent = this.template.keywords.find(row => row.keyword == item.timelock_parent_keyword);
        if (parent && item.parent_value) {
          item.timelock = true;
          const interval = setInterval(() => {
            if (parent.value && item.parent_value && this.timestamp() > Number(parent.value) + Number(item.parent_value)) {
              item.timelock = false;
              clearInterval(interval);
            }
          }, 1000);
        }
      }
    }
  }

  localInit() {
    this.local.index = localStorage.checklist_local_selected ? Number(localStorage.checklist_local_selected) : null;
    this.local.data = this.clientStorage.get('checklists_local_data', []);

    localStorage.removeItem('checklist_local_selected');

    if (this.local.index === null || !this.local.data[this.local.index]) { return; }
    const { edit, copy, checklist, template, selectedKlant, selectedProject, forcedProject, selectedAanvragen, details, files, anders, detailAnders } = this.local.data[this.local.index];

    this.edit = edit;
    this.copy = copy;
    this.checklist = checklist;
    this.template = template;
    this.selectedKlant = selectedKlant;
    this.selectedProject = selectedProject;
    this.forcedProject = forcedProject;
    this.selectedAanvragen = selectedAanvragen;
    this.selectedAanvragen = selectedAanvragen;
    this.details = details;
    this.files = files;
    this.anders = anders;
    this.detailAnders = detailAnders;
  }
  forcedProjectInit() {
    this.forcedProject = localStorage.getItem('checklistForcedProject');
    if (!this.forcedProject) { return; }
    localStorage.removeItem('checklistForcedProject');
    const project = this.projecten.find(project => project.id == this.forcedProject);
    this.selectedKlant = this.klanten.find(k => k.id == project?.klant_id ?? null);
  }
  localSubscribe() {
    this.settings.pause_listener = this.plt.pause.subscribe(() => {
      this.localUnsubscribe()
      this.saveLocal(true);
    });
  }
  localUnsubscribe() {
    this.settings.pause_listener.unsubscribe();
  }

  backButton() {
    if (this.local.index !== null && this.local.data[this.local.index]) {
      this.saveLocal();
      return;
    }

    this.saveLocalModal(true);
  }

  saveLocal(stay = false) {
    const data = {
      name: this.modals.name || (this.checklist ? this.checklist.checklistnummer : 'AutoSave'),
      time: this.convertDate(new Date()),
      edit: this.edit,
      copy: this.copy,
      checklist: this.checklist,
      template: this.template,
      selectedKlant: this.selectedKlant,
      selectedProject: this.selectedProject,
      forcedProject: this.forcedProject,
      selectedAanvragen: this.selectedAanvragen,
      details: this.details,
      files: this.files,
      anders: this.anders,
      detailAnders: this.detailAnders,
    }

    if (this.local.index !== null && this.local.data[this.local.index]) {
      data.name = this.local.data[this.local.index].name;
      this.local.data[this.local.index] = data;
    }
    else {
      this.local.data.push(data);
      this.local.index = (this.local.data.length - 1);
    }

    this.clientStorage.set('checklists_local_data', this.local.data);
    this.modals.local = false;

    this.showAlert(`Checklist opgeslagen ( lokaal )!`, 1.5)
    if (!stay) {
      this.localUnsubscribe();
      setTimeout(() => {
        if (this.forcedProject) {
          this.callPage('checklists/project');
        } else {
          this.callPage('checklists');
        }
      }, 1600)
    }
  }
  saveLocalModal(back = false) {
    this.local.back = back;
    this.modals.local = true;
    this.modals.name = (this.checklist ? this.checklist.checklistnummer : 'AutoSave')
  }

  scrollNavBar() {
    const offset = document.getElementById(`top-nav-${this.pages.index}`).offsetLeft;
    const container = document.getElementById(`top-nav`).offsetWidth;
    const element = document.getElementById(`top-nav-${this.pages.index}`).offsetWidth;

    document.getElementById('top-nav').scroll((offset - 12) - (container / 2) + (element / 2), 0);
    document.getElementById('bot-nav').scroll((offset - 12) - (container / 2) + (element / 2), 0);
  }
  navSelect(index) {
    if (this.settings.silent_save_loader) { return; }

    const list = this.pages.list.find(list => list.title == this.pages.index);
    for (const item of list.pages) {
      if (item.required == 1 && !item.value) {
        this.showAlert(`#${item._order} ${item.name} is verplicht!`)
        return
      }
    }

    this.pages.index = index;
    this.redrawSignatures();
    this.scrollNavBar();
    this.silentSubmit();
  }

  modalAddons(item) {
    this.modals.addons = true;
    this.modals.keyword = item.keyword;
    this.modals.item = item;
  }

  async addFile() {
    try {
      const image = await this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/checklists/upload`);
      if (!image?.data?.success) { throw image.data; }

      this.files.push({
        name: image.name,
        src: image.data.url,
        uri: null,
        uploaded: 1,
      });
    }
    catch (e) {
      this.infordb.handleError(e);
    }
  }

  deleteFile(i) {
    this.files.splice(i, 1);
  }

  addCustomRow(id) {
    const row = this.template.keywords.find(key => key.id == id);
    row.value.push(JSON.parse(JSON.stringify(row.data.rows)))
  }
  removeCustomRow(id, index) {
    const item = this.template.keywords.find(row => row.id == id);
    item.value.splice(index, 1);
  }


  addDetail(id) {
    const detailTemplate = this.detailTemplates[id];

    this.details.push({
      detailTemplate,
      keyword: detailTemplate.keyword,
      name: detailTemplate.name,
      title: '',
      image: {
        name: this.randomString(15) + '.png',
        filePath: null,
        uploaded: false,
        src: null,
      },
      description: '',
      adjustment: 0,
      price: 0,
      confirmed: false,
      messages: [],
      fields: JSON.parse(JSON.stringify(detailTemplate.velden)),
    });
  }
  removeDetail(index) {
    const name = this.details[index].name;
    if (confirm(name + ' verwijderen?')) {
      this.details.splice(index, 1);
    }
  }
  confirmDetail(index) {
    const detail = this.details[index];
    let rtn = true;
    detail.messages.splice(0, detail.messages.length);

    if (!detail.title) {
      detail.messages.push('Titel is verplicht!');
      rtn = false;
    }
    for (const row of detail.fields) {
      if (!row.value) {
        detail.messages.push(row.name + ' is verplicht!');
        rtn = false;
      }
    }

    if (!rtn) {
      return rtn;
    }

    this.details[index].confirmed = true;
  };
  redoDetail(index) {
    this.details[index].confirmed = false;
  }
  redoDetailImage(index) {
    const image = this.details[index].image;

    if (!this.edit) {
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/delete/file`, {
        file: image.src,
      });
    }

    image.src = null;
    image.filePath = null;
    image.uploaded = false;
  }
  calculateDetailPrice(index) {
    const detail = this.details[index];
    const price = detail.adjustment;
    detail.price = price;
  }

  selectAnders() {
    for (const item of this.template.keywords) {
      if (item.type === 'select_edit') {
        let anders = true;
        if (item.data && item.data.options) {
          for (const option of item.data.options) {
            if (option.value === item.value) {
              anders = false;
            }
          }
        }
        this.anders[item.id] = anders;
      }
    }
  }

  postLoops() {
    for (const detail of this.template.details) {
      for (const row of detail.velden) {
        if (row.type === 'select' || row.type === 'select_edit') {
          row.select = JSON.parse(row.data);
        }
      }
      this.detailTemplates[detail.id] = detail;
    }

    let excluded = {
      header: true,
      card_break: true,
      count_data_attribute: true,
      non_expected_values: true,

    }
    let order = 1;

    for (const row of this.template.keywords) {
      if (!excluded[row.type]) {
        row._order = order;
        order++;
      }

      if (row.data) {
        row.data = JSON.parse(row.data || '{}');
      }
      if (row.type == 'custom_row') {
        row.value = JSON.parse(row.value || '[]');
        if(row.data.show_random_rows){

          let new_value = [];
          let temp = JSON.parse(JSON.stringify( row.value ))
          for(let x = 0; x < row.data.show_random_rows; x++){
            const random_index = Math.round(Math.random() * (temp.length - 1));
            new_value.push(temp[random_index]);
            temp.splice(random_index, 1);
          }

          row.value = new_value;
        }
        if (!row.data.multiple && !row.value.length) { this.addCustomRow(row.id); }
      }
      if (row.type == 'image') {
        row.value = [];
      }
    }
    for (const item of this.datasetItems) {
      const arr = [];
      const json = JSON.parse(item.value);
      for (const n in json) {
        arr.push({ index: n, value: json[n] });
      }
      this.jsonDatasetItems[item.value] = arr;
    }
    for (const k in this.aanvragen) {
      for (const anv of this.aanvragen[k]) {
        this.selectedAanvragen[anv.id] = false;
      }
    }
  }

  callPage(page) {
    try {
      this.router.navigate([`/${page.toLowerCase()}`]);
    } catch (e) {
      alert(e);
    }
  }

  selectKlant(id) {
    this.selectedKlant = this.klanten.find(klant => klant.id == id);
    this.selectedKlant.Bezoekadres = (this.selectedKlant.straat || '') + ' ' + (this.selectedKlant.huisnummer || '') + (this.selectedKlant.toevoeging || '') + ', ' + (this.selectedKlant.plaats || '');
    this.selectedKlant.postadres = (this.selectedKlant.postadres_straat || '') + ' ' + (this.selectedKlant.postadres_huisnummer || '') + (this.selectedKlant.postadres_toevoeging || '') + ', ' + (this.selectedKlant.postadres_plaats || '');
    this.modals.klant = false;
  }
  selectLocatie(id) {
    this.prefill = false;
    this.selectedLocatie = this.selectedLocatie || {};
    if (id == 'Bezoekadres') {
      this.selectedLocatie.id = id;
      this.selectedLocatie.adres = this.selectedKlant.Bezoekadres;
    }
    else if (id == 'postadres') {
      this.selectedLocatie.id = id;
      this.selectedLocatie.adres = this.selectedKlant.postadres;
    }
    else {
      this.selectedLocatie = this.selectedKlant.locaties.find(locatie => locatie.id == id) || null;
    }

    if (this.user.values['checklist_prefill_obv_klant_locatie'] && this.user.values['checklist_prefill_obv_klant_locatie'].value == '1' && !localStorage.getItem('checklistEditId')) {
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/checklists/previouschecklistlocatie`, {
        locatie_id: this.selectedLocatie.id,
        klant_id: this.selectedKlant.id,
        template_id: this.template.id,
      })
        .then(response => {
          if (Object.keys(response.data.checklist).length > 0) {
            this.checklist = response.data.checklist;
            this.template = response.data.template;
            this.prefill = true;

            this.fillValues();
            this.pagesInit();
          }
        })
        .catch(err => {
          this.infordb.handleError(err);
        });
    }

    this.modals.locatie = false;
  }

  selectProject(id) {
    this.selectedProject = this.projecten.find(project => project.id == id);
    this.modals.project = false;
  }

  fillDetails() {
    for (const index in this.checklist.details) {
      const detail = this.checklist.details[index];
      const detailTemplate = detail.template;

      const img = {
        name: this.randomString(15) + '.png',
        filePath: null,
        uploaded: false,
        src: null,
      };
      if (detail.image) {
        img.src = detail.image;
        img.uploaded = true;
      }

      const values = JSON.parse(JSON.stringify(detailTemplate.velden));
      for (let row of values) {

        for (let value of detail.values) {
          if (row.keyword === value.keyword) {
            row.value = value.value;
            break;
          }
        }

        if (row.type === 'select' || row.type === 'select_edit') {
          row.select = JSON.parse(row.data);
        }

        if (row.type === 'select_edit') {
          let anders = true;
          for (const option of row.select) {
            if (option.value === row.value) {
              anders = false;
            }
          }
          this.detailAnders[index + '_' + row.id] = anders;
        }




      }

      this.details.push({
        detailTemplate,
        keyword: detailTemplate.keyword,
        name: detailTemplate.name,
        title: detail.title,
        image: img,
        description: detail.description,
        adjustment: Number(detail.adjustment),
        price: Number(detail.price),
        confirmed: true,
        messages: [],
        fields: values,
      });
    }
  }
  fillValues() {
    itemsLoop: for (const item of this.checklist.keywords) {
      for (const key of this.template.keywords) {
        if (key.keyword === item.keyword) {
          let value = item.value;

          if (item.type == 'custom_row') { value = JSON.parse(item.value || '[]'); }
          if (item.type == 'signature' && item.value) { key.existing = true; }
          if (item.type == 'image') { value = item.files; }

          key.value = value;
          key.src = item.file ?? null;
          key.opmerking_value = item.opmerking;


          if (item.opmerking) { key.opmerking_show = true; }
          if (item.file) { key.file_show = true; }

          continue itemsLoop;
        }
      }
    }
    for (const file of this.checklist.files ?? []) {
      this.files.push({
        name: file.name,
        src: file.src,
        uri: null,
        uploaded: 1,
      });
    }
    this.selectAnders();
  }
  fillProject(){
    if(this.checklist.project){
      this.selectedProject = this.checklist.project;
    }
  }
  fillKlant() {
    if (this.checklist.klant) {
      this.selectedKlant = this.checklist.klant;
    }
  }
  fillLocatie() {
    if (this.checklist.klant_locatie_id) {
      const id = this.checklist.klant_locatie_id;
      const bezoekAdres = (this.selectedKlant.straat || '') + ' ' + (this.selectedKlant.huisnummer || '') + (this.selectedKlant.toevoeging || '') + ', ' + (this.selectedKlant.plaats || '');
      const postAdres = (this.selectedKlant.postadres_straat || '') + ' ' + (this.selectedKlant.postadres_huisnummer || '') + (this.selectedKlant.postadres_toevoeging || '') + ', ' + (this.selectedKlant.postadres_plaats || '');

      this.selectedLocatie = this.selectedLocatie || {};

      if (id == 'Bezoekadres') {
        this.selectedLocatie.id = id;
        this.selectedLocatie.adres = bezoekAdres;
      } else if (id == 'postadres') {
        this.selectedLocatie.id = id;
        this.selectedLocatie.adres = postAdres;
      } else {
        this.selectedLocatie = this.selectedKlant.locaties.find(locatie => locatie.id == id) || null;
      }
    }
  }
  fillAanvragen() {
    if (this.checklist.aanvragen) {
      for (const id of JSON.parse(this.checklist.aanvragen)) {
        this.selectedAanvragen[id] = true;
      }
    }
  }
  fillChange() {
    this.nonExpectedValues();
    this.countAttributes();
    for (const item of this.template.keywords) { this.verifyParent(item); }
  }

  randomString(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }
    return result;
  }
  convertDate(date) {
    let d = new Date(date);
    if (isNaN(d.getTime())) {
      date = Number(date);
      d = new Date(date);
    }
    const datum = ('0' + d.getDate()).slice(-2) + '-' + ('0' + (d.getMonth() + 1)).slice(-2) + '-' + d.getFullYear();
    const tijd = ('0' + d.getHours()).slice(-2) + ':' + ('0' + d.getMinutes()).slice(-2);
    const reverse = d.getFullYear() + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + `0${d.getDate()}`.slice(-2);
    return { date: datum, time: tijd, reverse: reverse };
  }

  //  Signature
  startDrawing(ev) {
    ev.preventDefault();
    this.canvas.drawing = true;
    const canvasPosition = ev.target.getBoundingClientRect();
    this.canvas.saveX = ev.touches[0].pageX - canvasPosition.x;
    this.canvas.saveY = ev.touches[0].pageY - canvasPosition.y;
  }
  moved(ev) {
    ev.preventDefault();
    if (!this.canvas.drawing) { return; }
    const canvasPosition = ev.target.getBoundingClientRect();
    const ctx = ev.target.getContext('2d');
    const currentX = ev.touches[0].pageX - canvasPosition.x;
    const currentY = ev.touches[0].pageY - canvasPosition.y;
    ctx.lineJoin = 'round';
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = this.canvas.lineWidth;
    ctx.beginPath();
    ctx.moveTo(this.canvas.saveX, this.canvas.saveY);
    ctx.lineTo(currentX, currentY);
    ctx.closePath();
    ctx.stroke();
    this.canvas.saveX = currentX;
    this.canvas.saveY = currentY;
  }
  endDrawing(item) {
    this.canvas.drawing = false;
    const cvs = document.getElementById('signature-item-' + item.keyword) as HTMLCanvasElement;
    item.value = cvs.toDataURL();
    item.existing = false;
  }
  clearCanvas(item) {
    const cvs = document.getElementById(`signature-item-${item.keyword}`) as HTMLCanvasElement;
    const ctx = cvs.getContext('2d');
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    item.value = null;
    item.existing = false;
  }
  redrawSignatures(pages = this.pages, attrs = this.canvas, fnc = this.redrawSignatures) {
    const keywords = pages.list.find(row => row.title == pages.index);

    if (!keywords) { return; }

    for (const item of keywords.pages) {
      if (item.type != 'signature' || !item.value) { continue; }


      const canvas = document.getElementById(`signature-item-${item.keyword}`) as HTMLCanvasElement;
      const subdomain = localStorage.subdomain

      if (!canvas) {
        setTimeout(fnc, 50, pages, attrs, fnc);
        return;
      }

      const ctx = canvas.getContext('2d');
      const img = new Image;

      img.onload = () => {
        ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, attrs.width, attrs.height);
      }
      img.crossOrigin = 'anonymous'
      item.value.includes('data:image/png;base64') ? img.src = item.value : img.src = `https://${subdomain}.ikbentessa.nl/api/file/${item.value}`;
    }
  }

  // Camera
  async selectDetailImage(index) {
    const loading = await this.loadingController.create({ message: 'Toevoegen...' });
    await loading.present();

    try {
      const detail = this.details[index];
      const image = await this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/checklists/upload`);

      if (!image?.data?.success) { throw image.data; }

      loading.dismiss();
      detail.image.uploaded = true;
      detail.image.src = image.data.url;
    }
    catch (e) {
      loading.dismiss();
      this.infordb.handleError(e);
    }

  }
  async selectImage(item) {
    const loading = await this.loadingController.create({ message: 'Toevoegen...' });
    await loading.present();

    try {
      const image = await this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/checklists/upload`);
      if (!image?.data?.success) { throw image.data; }

      loading.dismiss();
      item.src = image.data.url;
    }
    catch (e) {
      loading.dismiss();
      this.infordb.handleError(e);
    }
  }
  async selectKeywordImage(item, skipExplorer = false) {
    if (this.forcedProject && !skipExplorer) {
      this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/explorer/get`, {
        path: `/Projecten/(${this.forcedProject})`,
        extension: ['jpg', 'jpeg', 'png', 'webp', 'bmp', 'jfif']
      }).then(response => {
        this.explorerFiles = response.data;
        this.explorerFiles['item'] = item;
        this.modals.explorer = true;
      });
      return;
    }
    const { data, status } = await this.infordb.getImage(`https://${this.subdomain}.ikbentessa.nl/api/explorer/upload`, {
      name: item.name,
      path: '/Checklists/temp',
      source: item.data.cameraOnly ? 'camera' : null,
    });
    if (!status) {
      await this.infordb.notification({ message: 'Er is iets foutgegaan!' });
      return;
    }

    item.data?.multiple
      ? item.value.push(data.file)
      : item.value = [data.file];
  }
  selectExplorerImage(item, file) {
    if (!file || !file.id) { return; }
    item.data?.multiple
      ? item.value.push(file)
      : item.value = [file];
    this.explorerFiles = null;
    this.modals.explorer = false;
  }
  removeKeywordImage(item, file_id) {
    const file_index = item.value.findIndex(file => file.id == file_id);
    item.value.splice(file_index, 1);
  }

  timestamp() {
    const date = new Date();
    return date.getTime();
  }

  async editImage(item, file_id) {
    const file_index = item.value.findIndex(file => file.id == file_id);
    const keyword_index = this.template.keywords.findIndex(file => file.keyword == item.keyword);

    const modal = await this.modalCtrl.create({
      component: CanvasPage,
      componentProps: {
        uploadUrl: `https://${this.subdomain}.ikbentessa.nl/api/explorer/upload`,
        fileUrl: `https://${this.subdomain}.ikbentessa.nl/api/file/explorer/files/`,
        image: item.value[file_index].src,
        dataset: 42
      },
      cssClass: 'fullscreen-modal',
      breakpoints: [0, 1],
      initialBreakpoint: 1,
      backdropDismiss: false
    });

    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data) {
      item.value[file_index].src = data.file.src;
      item.value[file_index].id = data.file.id;
      this.template.keywords[keyword_index].value[0].src = data.file.src;
    }
  }

  async editFile(i) {
    const modal = await this.modalCtrl.create({
      component: CanvasPage,
      componentProps: {
        uploadUrl: `https://${this.subdomain}.ikbentessa.nl/api/checklists/upload`,
        fileUrl: `https://${this.subdomain}.ikbentessa.nl/api/file/`,
        image: this.files[i].src,
        dataset: 42
      },
      cssClass: 'fullscreen-modal',
      breakpoints: [0, 1],
      initialBreakpoint: 1,
      backdropDismiss: false
    });

    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data) {
      this.files[i].src = data.url;
    }
  }
}
