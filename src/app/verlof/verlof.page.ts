import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Infordb, timeSelect, dateSelect } from 'infordb';

@Component({
  selector: 'app-verlof',
  templateUrl: './verlof.page.html',
  styleUrls: ['./verlof.page.scss'],
	providers: [Infordb]
})
export class VerlofPage implements OnInit {

  public disabled = false;
  public subdomain = window.localStorage.getItem('subdomain');
  public user = JSON.parse(window.localStorage.getItem('user'));
  public verlof;

	public timeSelect = timeSelect
	public dateSelect = dateSelect

  public verlofsaldo = 0;
  public year = new Date().getFullYear();

	public form = {
		dagen: false,
		van: '',
		tot: '',
		vanTijd: '',
		totTijd: '',
		opmerking: '',
	}

	constructor(
    private infordb: Infordb,
    public router: Router,
  ) {}

  ngOnInit() {
    this.getVerlof();
    this.getVerlofsaldo();
  }

  getVerlof(){
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/verlof`, {
      token: this.user.api_token
    }, {})
    .then(res => {
      this.verlof = res.data;
    });
  }

  getVerlofsaldo(){
    if(!this.user.values['verlof_saldo_weergeven_app']){return;}
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/users/getverlofsaldo`, {
      token: this.user.api_token,
      year: String(this.year),
    })
      .then((response) => {
        if (response.status != 404){
          this.verlofsaldo = response.data.saldo;
        }else{
          this.verlofsaldo = -999;
          alert("Er is iets misgegaan met het inladen van verlof probeer opnieuw of neem contact <NAME_EMAIL>");
        }
      })
      .catch(err => {
        this.verlofsaldo = -999;
        this.infordb.handleError(err);
      });
  }

  onSubmit() {
		const { dagen, van, tot, vanTijd, totTijd, opmerking } = this.form;

		if(dagen && (!van || !tot)){
			this.infordb.notification({message: 'Vul alle verplichte velden in'});
			return;
		}
		if(!dagen && (!van || !vanTijd || !totTijd)){
			this.infordb.notification({message: 'Vul alle verplichte velden in'});
			return;
		}

    this.disabled = true;
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/verlof/store`, {
      van: van,
      tot: tot,
      vanTijd: vanTijd,
      totTijd: totTijd,
      token: this.user.api_token,
      opmerking: opmerking
    }, {})
	    .then(response => {
	      if (response.status == 201) {
	        this.disabled = false;
	        alert("Uw verlof is aangevraagd.");
	        this.router.navigate(['/home'])
	      }
	    }).catch(this.infordb.handleError);
  }

  doRefresh(event){
    this.getVerlof();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

}
