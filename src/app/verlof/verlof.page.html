<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>
    <ion-title>Verlof</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-refresher slot="fixed" pullFactor="0.5" pullMin="100" pullMax="200" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <div class="pb-2">

    <ion-item *ngIf="user.values['verlof_saldo_weergeven_app']">
      <ion-label>Resterend verlof saldo {{year}}:</ion-label>
      <ion-label slot="end">{{verlofsaldo}} uur</ion-label>
    </ion-item>

    <ion-item>
      <ion-label>Hele dagen</ion-label>
      <ion-checkbox class="ion-text-end" [(ngModel)]="form.dagen"></ion-checkbox>
    </ion-item>

    <div *ngIf="form.dagen">
      <ion-item>
        <ion-label>Van*</ion-label>
        <ion-button fill="clear" (click)="dateSelect.select(form, 'van')">{{form.van || 'Datum selecteren'}}</ion-button>
      </ion-item>
      <ion-item>
        <ion-label>Tot*</ion-label>
        <ion-button fill="clear" (click)="dateSelect.select(form, 'tot')">{{form.tot || 'Datum selecteren'}}</ion-button>
      </ion-item>
    </div>

    <div *ngIf="!form.dagen">
      <ion-item>
        <ion-label>Datum*</ion-label>
        <ion-button fill="clear" (click)="dateSelect.select(form, 'van')">{{form.van || 'Datum selecteren'}}</ion-button>
      </ion-item>
      <ion-item>
        <ion-label>Begintijd*</ion-label>
        <ion-button fill="clear" (click)="timeSelect.select(form, 'vanTijd')">{{form.vanTijd || 'Tijd selecteren'}}</ion-button>
      </ion-item>
      <ion-item>
        <ion-label>Eindtijd*</ion-label>
        <ion-button fill="clear" (click)="timeSelect.select(form, 'totTijd')">{{form.totTijd || 'Tijd selecteren'}}</ion-button>
      </ion-item>
    </div>


    <ion-item>
      <ion-label>Opmerking</ion-label>
      <ion-textarea [(ngModel)]="form.opmerking"></ion-textarea>
    </ion-item>
    <ion-button [disabled]="disabled" (click)="onSubmit()" style="margin-top: 15px" type="submit" expand="full">Verlof aanvragen</ion-button>
  </div>

  <div *ngFor="let aanvraag of verlof">

    <ion-card *ngIf="aanvraag.akkoord == 1" class="bg-success">
      <ion-card-header>
        <ion-card-title>{{ aanvraag.datum }} <span *ngIf="aanvraag.van">{{ aanvraag.van }} - {{ aanvraag.tot }}</span></ion-card-title>
        <ion-card-subtitle class="text-white">Beoordeeld door {{ aanvraag.door.name + ' ' + aanvraag.door.lastname }}</ion-card-subtitle>
        <ion-card-subtitle class="text-white">{{ aanvraag.opmerkingen }}</ion-card-subtitle>
      </ion-card-header>
    </ion-card>

    <ion-card *ngIf="aanvraag.beoordeeld == 1 && aanvraag.akkoord == 0" class="bg-danger">
      <ion-card-header>
        <ion-card-title>{{ aanvraag.datum }} <span *ngIf="aanvraag.van">{{ aanvraag.van }} - {{ aanvraag.tot }}</span></ion-card-title>
        <ion-card-subtitle class="text-white">Beoordeeld door {{ aanvraag.door.name + ' ' + aanvraag.door.lastname }}</ion-card-subtitle>
        <ion-card-subtitle class="text-white">{{ aanvraag.opmerkingen }}</ion-card-subtitle>
      </ion-card-header>
    </ion-card>

    <ion-card *ngIf="aanvraag.beoordeeld == 0" class="bg-warning">
      <ion-card-header>
        <ion-card-title class="text-white">{{ aanvraag.datum }} <span *ngIf="aanvraag.van">{{ aanvraag.van }} - {{ aanvraag.tot }}</span></ion-card-title>
        <ion-card-subtitle class="text-white">{{ aanvraag.opmerkingen }}</ion-card-subtitle>
      </ion-card-header>
    </ion-card>

  </div>

  <div class="modal-container" *ngIf="timeSelect.modal" (click)="timeSelect.modal = false;">
    <ion-card class="p-2 text-dark" style="width: 90vw; max-width: 750px;" (click)="$event.stopPropagation()">
      <div class="d-flex ion-justify-content-around font-size-125">

        <div class="flex-between w-100">
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

        <!--      Hours-->
        <div (scroll)="timeSelect.scrollTime('hour')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-hour">
          <div style="height: 39px"></div>
          <div class="ion-text-center my-4" id="custom-time-hour-{{h}}" *ngFor="let h of timeSelect.vars.hours" (click)="timeSelect.selectTimeValue('hour', h)">
            <span class="{{timeSelect.vars.time.hour == h ? 'text-primary' : ''}}">{{('0' + h).slice(-2)}}</span>
          </div>
          <div style="height: 39px"></div>
        </div>

        <div class="flex-between w-50">
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

        <!--      Minutes-->
        <div (scroll)="timeSelect.scrollTime('min')" style="max-height: 150px;" class="overflow-auto w-100" id="custom-time-min">
          <div style="height: 39px"></div>
          <div class="ion-text-center my-4" id="custom-time-min-{{m}}" *ngFor="let m of timeSelect.vars.minutes" (click)="timeSelect.selectTimeValue('min', m)">
            <span class="{{timeSelect.vars.time.min == m ? 'text-primary' : ''}}">{{('0' + m).slice(-2)}}</span>
          </div>
          <div style="height: 39px"></div>
        </div>

        <div class="flex-between w-100">
          <div class="bg-secondary h-1 w-100 px-2"></div>
        </div>

      </div>
      <div class="mt-1 pt-1 ion-text-right border-top">
        <ion-button fill="clear" (click)="timeSelect.confirmTime()">OK</ion-button>
      </div>
    </ion-card>
  </div>
  <div class="modal-container" *ngIf="dateSelect.modal" (click)="dateSelect.modal = false;">
    <ion-card class="p-2 text-dark custom-date-picker-modal" (click)="$event.stopPropagation()">

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(false)">
          <ion-icon name="chevron-back-outline"></ion-icon>
        </ion-button>
        <span class="mx-2">{{dateSelect.vars.date.year}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeYear(true)">
          <ion-icon name="chevron-forward-outline"></ion-icon>
        </ion-button>
      </div>

      <div class="flex-between my-1 mx--2">
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(false)">
          <ion-icon name="chevron-back-outline"></ion-icon>
        </ion-button>
        <span class="mx-2">{{dateSelect.vars.months[dateSelect.vars.date.month]}}</span>
        <ion-button fill="clear" class="mx-2" (click)="dateSelect.selectDateChangeMonth(true)">
          <ion-icon name="chevron-forward-outline"></ion-icon>
        </ion-button>
      </div>
      <div class="flex-between custom-date-picker-days">
        <div>ma</div>
        <div>di</div>
        <div>wo</div>
        <div>do</div>
        <div>vr</div>
        <div>za</div>
        <div>zo</div>
      </div>
      <div class="custom-date-picker">
        <div
          *ngFor="let day of dateSelect.vars.completeDays"
          [ngClass]="{
                    'active': dateSelect.vars.date.date === (dateSelect.vars.date.year + '-' + ('0' + dateSelect.vars.date.month).slice(-2) + '-' + ('0' + day.day).slice(-2)),
                    'prev-month': day.type === 'prev',
                    'current-month': day.type === 'current',
                    'next-month': day.type === 'next',
                    'disabled': day.status === 'past'
                  }"
          (click)="dateSelect.confirmDate(day)">
          {{ day.day }}
        </div>
      </div>

    </ion-card>
  </div>


</ion-content>
