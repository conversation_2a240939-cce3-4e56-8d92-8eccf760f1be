import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';

import { CorrectiePageRoutingModule } from './correctie-routing.module';

import { CorrectiePage } from './correctie.page';

const routes: Routes = [
  {
    path: '',
    component: CorrectiePage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    CorrectiePageRoutingModule
  ],
  declarations: [CorrectiePage]
})
export class CorrectiePageModule {}
