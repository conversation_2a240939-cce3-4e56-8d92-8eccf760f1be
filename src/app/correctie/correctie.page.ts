import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {Infordb} from "infordb";

@Component({
  selector: 'app-correctie',
  templateUrl: './correctie.page.html',
  styleUrls: ['./correctie.page.scss'],
	providers: [Infordb]
})
export class CorrectiePage implements OnInit {

  private token = JSON.parse(window.localStorage.getItem('user')).api_token;
  private subdomain: String = window.localStorage.getItem('subdomain');
  
	public date = window.localStorage.getItem('urenregistratieDate');
	public message = '';
	
  constructor(
		private infordb: Infordb,
    private router: Router,
  ) {}

  ngOnInit() {
  }

  onSubmit() {
    this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/correction`, {
      api_token: localStorage.api_token,
      message: this.message,
      token: this.token,
      date: this.date
    }, {})
	    .then(response => {
	      alert("Het verzoek is ingediend.");
	      this.router.navigate(['/home'], { skipLocationChange: true })
	    })
	    .catch(this.infordb.handleError);
  }

}
