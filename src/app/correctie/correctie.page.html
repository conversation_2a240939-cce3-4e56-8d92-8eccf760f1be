<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/urenregistratie"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ date }}</ion-title>

  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="px-3">
    <h1>Correctie aanvragen voor {{ date }}</h1>

    <div>
      <ion-item>
        <ion-textarea label="Bericht" labelPlacement="floating" rows="8" [(ngModel)]="message" ></ion-textarea>
      </ion-item>

      <ion-button [disabled]="!message" (click)="onSubmit()" style="margin-top: 15px" type="submit" expand="full">Versturen</ion-button>
    </div>
  </div>
</ion-content>
