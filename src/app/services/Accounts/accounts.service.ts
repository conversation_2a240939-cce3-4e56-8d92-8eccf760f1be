import { Injectable } from '@angular/core';
import {Account} from "../../models/Account/account";
import {Router} from "@angular/router";

@Injectable({
  providedIn: 'root'
})
export class AccountsService {

  constructor(
      private router: Router,
  ) {
    this.initStorage();
  }

  initStorage() {
    if(!localStorage.accounts) {
      localStorage.setItem('accounts', JSON.stringify([]));
    }
  }

  getAccounts(): Account[] {
    const local_data =  JSON.parse(localStorage.accounts);
    const accounts = local_data.map(data => new Account(data));
    accounts.sort((a, b) => {
      const current = ` ${a.subdomain} ${a.first_name || ''} ${a.last_name || ''}`;
      const next = ` ${b.subdomain} ${b.first_name || ''} ${b.last_name || ''}`;

      return current.localeCompare(next)
    });

    return accounts;
  }
  setAccounts(accounts: Account[]) {
    localStorage.setItem('accounts', JSON.stringify(accounts));
  }

  select(account: Account) {
    localStorage.login_prefill = JSON.stringify({
      subdomain: account.subdomain,
      email: account.email,
      password: account.password,
    });

    this.router.navigate(['/login']);
  }
  add(subdomain: string, user: any) {
    this.remove(subdomain, user.user_id);

    const accounts = this.getAccounts();
    accounts.push(new Account({ subdomain, ...user }));

    this.setAccounts(accounts);
  }
  remove(subdomain: string, user_id: number) {
    let accounts = this.getAccounts()
    let filtered_accounts = accounts.filter((account: Account) => {
      return !(account.subdomain === subdomain && account.user_id === user_id);
    });

    this.setAccounts(filtered_accounts);
  }

}
