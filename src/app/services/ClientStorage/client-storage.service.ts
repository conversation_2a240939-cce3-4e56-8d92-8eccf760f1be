import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ClientStorageService {

  private subdomain: string;

  constructor() {
    this.initStorage();
  }


  //Init
  initStorage(){
    this.subdomain = localStorage.subdomain

    if(localStorage.getItem('client_storage')){ return; }
    localStorage.setItem('client_storage', JSON.stringify({}));
  }

  //Get
  get(key: string, def: any = null){
    this.initStorage();

    const client_storage = this.getClientStorage();

    let value = client_storage[key]
    if(value == undefined){ value = def; }
    if(this.isJsonString(value)){ value = JSON.parse(value) }

    return value;
  }
  private getClientStorage(){
    if(!this.subdomain){ return {}; }

    const storage = JSON.parse(localStorage.getItem('client_storage'));
    if(storage[this.subdomain] === undefined){
      storage[this.subdomain] = {}

      localStorage.setItem('client_storage', JSON.stringify(storage));
    }

    return storage[this.subdomain];
  }

  //Set
  set(key: string, value: any){
    this.initStorage();

    if(typeof value == 'object'){ value = JSON.stringify(value); }

    const client_storage = this.getClientStorage();

    client_storage[key] = value;
    this.setClientStorage(client_storage);
  }
  private setClientStorage(client_storage: object){
    const storage = JSON.parse(localStorage.getItem('client_storage'));

    storage[this.subdomain] = client_storage;
    localStorage.setItem('client_storage', JSON.stringify(storage));
  }


  //Utility
  isJsonString(str: string) {
    try {
      JSON.parse(str);
    }
    catch (e) {
      return false;
    }
    return true;
  }

}
