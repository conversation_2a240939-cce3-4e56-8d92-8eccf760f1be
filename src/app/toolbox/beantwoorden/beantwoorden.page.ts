import {Component, OnInit} from '@angular/core';
import {Infordb} from 'infordb';
import {ActivatedRoute, Router} from "@angular/router";

@Component({
	selector: 'app-beantwoorden',
	templateUrl: './beantwoorden.page.html',
	styleUrls: ['./beantwoorden.page.scss'],
	providers: [Infordb],
})
export class BeantwoordenPage implements OnInit {

	public subdomain = window.localStorage.getItem('subdomain');
	public user = JSON.parse(window.localStorage.getItem('user'));
	public multipleChoiceAnswers = {};
	public gebruikerAntwoord = {};
	public base = {
		is_loading: false,
	}
	public search = {
		searchVal: '',
	}
	public data = {
		toolbox: null,
		toolboxId: 0,
	}

	constructor(
		private router: Router,
		private infordb: Infordb,
		private route: ActivatedRoute,
	) {
	}

	ngOnInit() {
		this.getToolbox();
	}

	getToolbox() {
		this.route.queryParams.subscribe(params => {
			this.data.toolboxId = params['id'];
		})
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/toolbox/getsingletoolbox`, {
			id: String(this.data.toolboxId),
			app: '1',
		})
			.then(response => {
				if (response.status === 200) {
					this.fillToolbox(response);
				}
			})
			.catch(this.infordb.handleError);
	}

	fillToolbox(response) {
		this.data.toolbox = response.data.toolbox;

		this.data.toolbox['vragen'].forEach(vraag => {
			if (vraag.type == "meerkeuze") {
				const parsedAntwoorden = JSON.parse(vraag.antwoorden);
				const antwoordenArray = Object.keys(parsedAntwoorden).map(key => ({
					key: key,
					checkBox: parsedAntwoorden[key].checkBox,
					value: parsedAntwoorden[key].value
				}));
				vraag.antwoorden = antwoordenArray;
				vraag.gebruikerAntwoord = [];
			} else if (vraag.type == "openvraag") {
				vraag.gebruikerAntwoord = "";
			}
		});
	}

	hasPermission(permissionName: string): boolean {
		var found = false;
		this.user.permissions.forEach(permission => {
			if (permission.permission == permissionName) {
				found = true;
			}
		});
		return found;
	}

	callPage(page) {
		try {
			this.router.navigate([`/${page.toLowerCase()}`]).then(() => {
				window.location.reload();
			});
		} catch (e) {
			alert(e);
		}
	}

	submitAnswer() {
		this.route.queryParams.subscribe(params => {
			this.data.toolboxId = params['id'];
		});
		const answers = this.data.toolbox['vragen'].map(vraag => {
			if (vraag.type === 'meerkeuze') {
				return {
					toolboxId: this.data.toolboxId,
					vraagId: vraag.id,
					antwoorden: vraag.antwoorden.filter(antwoord => antwoord.checked).map(antwoord => antwoord.key)
				};
			} else if (vraag.type === 'openvraag') {
				return {
					toolboxId: this.data.toolboxId,
					vraagId: vraag.id,
					antwoord: vraag.gebruikerAntwoord
				};
			}
			return null;
		}).filter(answer => answer !== null);
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/toolbox/storeAnswers`, {
			data: JSON.stringify(answers),
			app: '1',
		})
			.then(response => {
				if (response.status !== 404 && response.status !== 400) {
					alert('Succevol ingevuld en verstuurd');
					this.callPage("/toolbox");
				} else {
					alert('Er is iets misgegaan, probeer opnieuw of neem contact <NAME_EMAIL>.');
				}
			})
			.catch(this.infordb.handleError);

	}

	openBestand(bestand = '', video = '') {
		if (!video) {
			window.location.href = `https://${this.subdomain}.ikbentessa.nl/api/file/toolbox/${bestand}`;
			return;
		}

		window.location.href = video;
	}

  download(src){
    const link = 'https://' + this.subdomain + '.ikbentessa.nl/api/file/explorer/files/' + src;
    window.open(link);
  }

}
