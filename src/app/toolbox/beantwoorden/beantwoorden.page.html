<ion-header xmlns="http://www.w3.org/1999/html">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/toolbox"></ion-back-button>
    </ion-buttons>
    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading" > <ion-spinner></ion-spinner> </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div *ngIf="data.toolbox" >
    <ion-item>{{data.toolbox.titel}}</ion-item>

      <div *ngIf="data?.toolbox?.explorer_file" class="py-1">
          <div class="flex-between border rounded p-1 bg-inverse-secondary">
              <div class="d-flex">
                  <img *ngIf="data.toolbox.explorer_file.icon" src="https://{{subdomain}}.ikbentessa.nl/client/public/img/explorer/files/{{data.toolbox.explorer_file.icon}}" height="35">
                  <div>
                      <div *ngIf="data.toolbox.explorer_file.view_name">
                          {{ data.toolbox.explorer_file.view_name }}
                      </div>
                      <div class="font-size-09" *ngIf="!data.toolbox.explorer_file.view_name">
                          {{ data.toolbox.explorer_file.name }}.{{ data.toolbox.explorer_file.extension }}
                      </div>
                      <div class="text-muted font-size-085">
                          {{ data.toolbox.explorer_file.path }}
                      </div>
                  </div>
              </div>
              <ion-button fill="clear" (click)="download(data.toolbox.explorer_file?.src)">
                  <ion-icon name="cloud-download-outline"></ion-icon>
              </ion-button>
          </div>
      </div>

    <ion-item *ngIf="data.toolbox.video">Video: <a style="margin-left: 5px" (click)="openBestand('',data.toolbox.video)">{{data.toolbox.video}}</a> </ion-item>
  </div>

  <div *ngIf="data.toolbox" >
    <ion-label class="pl-3">Beschrijving:</ion-label>
    <ion-item><ion-textarea [autoGrow]="1" disabled="1" [innerHTML]="data.toolbox.beschrijving"></ion-textarea></ion-item>
  </div>

  <div *ngIf="data.toolbox" >
    <div *ngFor="let vraag of data.toolbox.vragen; index as i">
      <div *ngIf="vraag.type == 'meerkeuze'">
        <ion-list>
          <ion-list-header><ion-label>Vraag: {{ vraag.vraag }}</ion-label></ion-list-header>
          <ion-item *ngFor="let antwoord of vraag.antwoorden;">
            <ion-checkbox justify="start" [(ngModel)]="antwoord.checked" name="antwoord_{{i}}"></ion-checkbox>
            <ion-label style="margin-left: 10px">{{ antwoord.value }}</ion-label>
          </ion-item>
        </ion-list>
      </div>
      <ion-label *ngIf="vraag.type == 'openvraag'">
        <ion-list>
          <ion-list-header><ion-label>Vraag: {{ vraag.vraag }}</ion-label></ion-list-header>
          <ion-item>
            <ion-input [(ngModel)]="vraag.gebruikerAntwoord" name="answers"></ion-input>
          </ion-item>
        </ion-list>
      </ion-label>
    </div>
  </div>

  <!-- Submit button -->
  <div class="p-2">
    <div class="flex-between overflow-auto">
      <div class="w-100 ion-text-center mr-1">
        <a class="btn btn-inverse-primary w-100" (click)="submitAnswer()" *ngIf="hasPermission('Toolbox bekijken')">Verstuur</a>
      </div>
    </div>
  </div>
</ion-content>
