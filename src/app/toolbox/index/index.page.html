<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button text="Terug" defaultHref="/home"></ion-back-button>
    </ion-buttons>

    <ion-title slot="end" class="ion-text-right" *ngIf="base.is_loading" > <ion-spinner></ion-spinner> </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-item>
    <ion-label position="floating">Zoeken</ion-label>
    <ion-input [(ngModel)]="search.searchVal">

    </ion-input>
  </ion-item>
  <ion-card *ngFor="let toolbox of data.toolboxen" >
    <ion-list lines="none" *ngIf="searchRegel(toolbox)">
      <ion-item>
        <ion-label>
          <h1>{{toolbox.titel ?? '[naamloze toolbox]'}}</h1>
          <ion-label class="d-block">
            Datum: {{ toolbox.created_at ? (toolbox.created_at | date: 'dd-MM-yyyy') : '' }}
          </ion-label>
        </ion-label>
      </ion-item>
      <div class="p-2" >
        <div class="flex-between overflow-auto">
          <div class="w-100 ion-text-center mr-1" *ngIf="hasPermission('Toolbox bekijken')">
            <a class="btn btn-inverse-primary w-100" (click)="callPage('toolbox/beantwoorden', toolbox.id)" *ngIf="toolbox.ingevuld != 1">Invullen</a>
            <a class="btn btn-inverse-success w-100 rounded" *ngIf="toolbox.ingevuld == 1" disabled="">Voldaan</a>
          </div>
        </div>
      </div>
    </ion-list>
  </ion-card>
</ion-content>

