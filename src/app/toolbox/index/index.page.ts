import {Component, OnInit} from '@angular/core';
import {Infordb} from 'infordb';
import {Router} from "@angular/router";

@Component({
	selector: 'app-index',
	templateUrl: './index.page.html',
	styleUrls: ['./index.page.scss'],
	providers: [Infordb],
})
export class IndexPage implements OnInit {

	public subdomain = window.localStorage.getItem('subdomain');
	public user = JSON.parse(window.localStorage.getItem('user'));
	public base = {
		is_loading: false,
	}
	public search = {
		searchVal: '',
	}
	public data = {
		toolboxen: null,
	}

	constructor(
		private router: Router,
		private infordb: Infordb,
	) {
	}

	ngOnInit() {
		this.getToolboxen();
	}

	getToolboxen() {
		this.infordb.post(`https://${this.subdomain}.ikbentessa.nl/api/toolbox/get`, {
			app: '1',
			userid: this.user.user_id,
			api_token: localStorage.api_token,
		})
			.then(response => {
				if (response.status === 200) {
          this.data.toolboxen = response.data.toolbox;
				}
			})
			.catch(this.infordb.handleError);
	}

	hasPermission(permissionName: string): boolean {
		var found = false;
		this.user.permissions.forEach(permission => {
			if (permission.permission == permissionName) {
				found = true;
			}
		});
		return found;
	}

	callPage(page, id) {
		try {
			this.router.navigate([`/${page.toLowerCase()}`], {queryParams: {id: id}});
		} catch (e) {
			alert(e);
		}
	}

	searchRegel(toolbox) {
		const words = this.search.searchVal.toLowerCase().split(' ')
		const {titel, datum} = toolbox;
		let concat = `${titel || ''} ${datum || ''}`;
		concat = concat.toLowerCase()

		for (const word of words) {
			if (!concat.includes(word.toLowerCase())) {
				return false;
			}
		}

		return true;
	}
}

