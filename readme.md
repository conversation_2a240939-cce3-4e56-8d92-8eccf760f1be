### Build parameters
<hr>

#### Global
`Ionic CLI` 6.20.9 <br>
`Node.js` 20.11.1

#### Android Studio
`Android Studio` Iguana 2023.2.1 <br>
`Android Gradle Plugin Version` 8.0.0 <br>
`Gradle Version` 8.0.2 <br>
`Gradle JDK` Corretto-17

#### Android Pemrissions
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-feature android:name="android.hardware.location.gps" />

#### Apple Permissions
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Deze app gebruikt je locatie om locatiegebaseerde diensten te leveren.</string>
    
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Deze app gebruikt je locatie om locatiegebaseerde diensten te leveren.</string>