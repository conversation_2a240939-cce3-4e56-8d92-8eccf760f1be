{"name": "<PERSON>", "version": "0.0.1", "author": "InforDB Development", "homepage": "https://www.infordb.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^17.0.2", "@angular/common": "^17.0.2", "@angular/compiler": "^17.0.2", "@angular/core": "^17.0.2", "@angular/forms": "^17.0.2", "@angular/platform-browser": "^17.0.2", "@angular/platform-browser-dynamic": "^17.0.2", "@angular/router": "^17.0.2", "@capacitor-community/barcode-scanner": "^4.0.1", "@capacitor/android": "5.7.0", "@capacitor/app": "5.0.7", "@capacitor/camera": "^5.0.9", "@capacitor/core": "^5.7.2", "@capacitor/device": "^5.0.7", "@capacitor/geolocation": "^5.0.8", "@capacitor/haptics": "5.0.7", "@capacitor/ios": "^5.7.2", "@capacitor/keyboard": "5.0.8", "@capacitor/push-notifications": "^5.1.1", "@capacitor/status-bar": "5.0.7", "@ionic/angular": "7.0.0", "angular-froala-wysiwyg": "^4.1.4", "axios": "^1.6.7", "infordb": "^1.2.42", "ionicons": "^7.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.0", "@angular-eslint/builder": "^17.0.0", "@angular-eslint/eslint-plugin": "^17.0.0", "@angular-eslint/eslint-plugin-template": "^17.0.0", "@angular-eslint/schematics": "^17.0.0", "@angular-eslint/template-parser": "^17.0.0", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.2", "@angular/language-service": "^17.0.2", "@capacitor/cli": "5.7.0", "@ionic/angular-toolkit": "^9.0.0", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^7.26.0", "eslint-plugin-import": "2.22.1", "eslint-plugin-jsdoc": "30.7.6", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.2"}, "description": "An Ionic project"}